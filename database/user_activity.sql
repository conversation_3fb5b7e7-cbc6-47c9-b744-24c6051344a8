use nexs;
DROP TABLE IF EXISTS `user_activity`;

CREATE TABLE  `user_activity` (
  `sno` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL,
  `action`  varchar(100) NOT NULL,
  `facility` varchar(50) NOT NULL,
  `grn_code` varchar(20) NOT NULL,
  `assigned_to` varchar(50) NOT NULL,
  `performed_at` datetime NOT NULL default current_timestamp,
  PRIMARY KEY (`sno`),
  INDEX `grn_index` (`grn_code`),
  INDEX `assigned_to_index` (`assigned_to`),
  INDEX `user_id_index` (`user_id`)
);
