alter table `nexs`.`grn_items`
modify `created_at` datetime not null default current_timestamp;

alter table `nexs`.`grn_items`
modify `updated_at` datetime not null default current_timestamp;

alter table `nexs`.`grn_master`
modify `created_at` datetime not null default current_timestamp;

alter table `nexs`.`grn_master`
modify `updated_at` datetime not null default current_timestamp;

alter table `nexs`.`grn_pid_master`
modify `created_at` datetime not null default current_timestamp;

alter table `nexs`.`grn_pid_master`
modify `updated_at` datetime not null default current_timestamp;

alter table `nexs`.`grn_qc_log`
modify `created_at` datetime not null default current_timestamp;

alter table `nexs`.`grn_qc_log`
modify `updated_at` datetime not null default current_timestamp;

alter table `nexs`.`user_activity`
modify `performed_at` datetime not null default current_timestamp;