use nexs;
DROP TABLE IF EXISTS `grn_items`;

CREATE TABLE `grn_items` (
	`barcode` varchar(20) NOT NULL,
	`grn_code` varchar(20) NOT NULL,
	`status` varchar(20) NOT NULL,
	`pid` varchar(45) NOT NULL,
	`po_id` varchar(20) NOT NULL,
	`invoice_ref_num` varchar(20) NOT NULL,
	`vendor_code` varchar(45) NOT NULL,
	`expiry_date` datetime  NULL,
	`lot_no` varchar(45) NULL,
	`estimated_qty` int(11) NOT NULL,
	`qc_pass_box_barcode` varchar(20) NULL,
	`qc_status` varchar(20) NOT NULL,
	`qc_fail_code` varchar(20) NULL default 'NA',
    `qc_reason` varchar(200) NULL default 'NA',
	`qc_master_sampling` int(3) NOT NULL,
	`qc_gradient_sampling` int(3) NOT NULL,
	`failure_threshold_qc_master` int(3) NOT NULL,
	`failure_threshold_qc_gradient` int(3) NOT NULL,
    `facility` varchar(50) NOT NULL,
    `channel_status` int(11) NOT NULL default 0,
	`created_by` varchar(100) NOT NULL,
	`updated_by` varchar(100) NOT NULL,
	`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (`barcode`),
	FOREIGN KEY (grn_code) REFERENCES grn_master(grn_code),
	INDEX `grn_code_index` (`grn_code`, `pid`),
	INDEX `status_index` (`status`),
    INDEX `qc_status_index` (`qc_status`),
    INDEX `qc_pass_box_barcode_index` (`qc_pass_box_barcode`),
  INDEX `invoice_index` (`invoice_ref_num`)
);
