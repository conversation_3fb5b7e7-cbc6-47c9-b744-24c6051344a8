use nexs;
DROP TABLE IF EXISTS `grn_master`;

CREATE TABLE `grn_master` (
	`grn_code` varchar(20) NOT NULL,
	`unicom_grn_code` varchar(20) NOT NULL,
	`po_id` varchar(20) NOT NULL,
	`invoice_id` varchar(20) NOT NULL,
	`invoice` json NOT NULL,
    `po` json NOT NULL,
	`grn_status` varchar(20) NOT NULL,
	`grn_synced_to_unicom` tinyint NOT NULL,
	`grn_synced_to_NAV` tinyint NOT NULL,
    `facility` varchar(50) NOT NULL,
    `grn_sync_status` tinyint NOT NULL,
	`created_by` varchar(100) NOT NULL,
	`updated_by` varchar(100) NOT NULL,
	`created_at` datetime NOT NULL default current_timestamp,
	`updated_at` datetime NOT NULL default current_timestamp,
	PRIMARY KEY (`grn_code`),
	UNIQUE INDEX `unicom_grn_code_UNIQUE` (`unicom_grn_code`),
	INDEX `po_id_index` (`po_id`),
    INDEX `grn_status_index` (`grn_status`),
	INDEX `invoice_id_index` (`invoice_id`)
);
