#!/usr/bin/bash


DB_USER='root'
DB_PASSWD=unroot
DB_PORT=3308
DB_HOST='127.0.0.1'
DB_NAME='nexs'

sql_query='CREATE DATABASE IF NOT EXISTS nexs'
MYSQL -u$DB_USER -p$DB_PASSWD -P$DB_PORT -h$DB_HOST <<EOF
$sql_query
EOF

mysql -u$DB_USER -p$DB_PASSWD -P$DB_PORT -h$DB_HOST < barcode_range.sql
mysql -u$DB_USER -p$DB_PASSWD -P$DB_PORT -h$DB_HOST < grn_master.sql
mysql -u$DB_USER -p$DB_PASSWD -P$DB_PORT -h$DB_HOST < grn_pid_master.sql
mysql -u$DB_USER -p$DB_PASSWD -P$DB_PORT -h$DB_HOST < grn_items.sql
mysql -u$DB_USER -p$DB_PASSWD -P$DB_PORT -h$DB_HOST < user_activity.sql
