use nexs;
DROP TABLE IF EXISTS `grn_qc_log`;

CREATE TABLE `grn_qc_log` (
    `sno` int(11) NOT NULL AUTO_INCREMENT,
	`grn_code` varchar(20) NOT NULL,
	`pid` varchar(20) NOT NULL,
	`barcode` varchar(20) NOT NULL,
	`action` varchar(50) NOT NULL,
	`reason` varchar(200) NOT NULL,
	`created_by` varchar(100) NOT NULL,
	`updated_by` varchar(100)  NULL,
	`created_at` datetime NOT NULL default current_timestamp,
	`updated_at` datetime NOT NULL default current_timestamp,
	PRIMARY KEY (`sno`),
	INDEX `grn_code_index` (`grn_code`, `pid`),
    INDEX `barcode_index` (`barcode`)
);