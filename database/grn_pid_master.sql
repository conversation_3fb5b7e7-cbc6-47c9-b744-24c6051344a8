use nexs;
DROP TABLE IF EXISTS `grn_pid_master`;

CREATE TABLE `grn_pid_master` (
  `grn_code` varchar(20) NOT NULL,
  `pid` varchar(20) NOT NULL,
  `invoice_id` varchar(20) NOT NULL,
  `invoice_ref_num` varchar(50) NOT NULL,
  `vendor` varchar(30) NOT NULL,
  `brand` varchar(30) NOT NULL,
  `category_id` int(10) NOT NULL,
  `pid_description` varchar(200) NULL,
  `price` decimal(12,2) NULL,
  `tax_rate` decimal(12,2) NULL,
  `cgst_rate` decimal(12,2) NULL,
  `sgst_rate` decimal(12,2) NULL,
  `igst_rate` decimal(12,2) NULL,
  `estimated_total_quantity` int(11) NOT NULL,
  `estimated_quantity_list` json NOT NULL,
  `grn_pid_status` varchar(20) NOT NULL,
  `manual_override` tinyint(1) NOT NULL default 0,
  `created_by` varchar(100) NOT NULL,
  `updated_by` varchar(100) NOT NULL,
  `created_at` datetime NOT NULL default current_timestamp,
  `updated_at` datetime NOT NULL default current_timestamp,
  PRIMARY KEY (`grn_code`, `pid`),
  CONSTRAINT uc_grn_pid UNIQUE (`grn_code`, `pid`),
  INDEX `invoice_id_index` (`invoice_id`, `pid`),
  INDEX `invoice_ref_num_index` (`invoice_ref_num`, `pid`)
);
