# syntax=docker/dockerfile:experimental

# build
FROM public.ecr.aws/z5y1f1y8/maven:3.6-amazoncorretto-8 as builder

ARG TARGETPLATFORM
ARG BUILDPLATFORM
RUN echo "Building on $BUILDPLATFORM, for $TARGETPLATFORM"

COPY ./pom.xml /app/
WORKDIR /app/
RUN aws s3 cp s3://deployment.lenskartprod.internal/settings.xml /root/.m2/
RUN --mount=type=cache,target=/root/.m2 mvn -e -B dependency:resolve

RUN aws s3 cp s3://deployment.lenskartprod.internal/newrelic-java.zip .
RUN unzip newrelic-java.zip

COPY . .
RUN --mount=type=cache,target=/root/.m2 mvn clean package -DskipTests

# run
FROM public.ecr.aws/z5y1f1y8/maven:3.6-amazoncorretto-8

RUN mkdir /app
COPY --from=builder /app/target/*.jar /app/
COPY --from=builder /app/newrelic/ /app/newrelic/
RUN chown -R 1000 /app/

EXPOSE 8080
WORKDIR /app/
ENTRYPOINT ["sh", "-c", "java -Djava.security.egd=file:/dev/./urandom -javaagent:/app/newrelic/newrelic.jar -Dserver.port=8080 -Dspring.profiles.active=$PROFILE $JVM_ARGS -jar /app/*.jar"]
