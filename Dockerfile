FROM lenskart/maven3-jdk8 as builder
RUN mkdir -p /app
RUN mkdir -p /root/.m2/
COPY . /app/
WORKDIR /app/
RUN aws s3 cp s3://deployment.lenskartprod.internal/newrelic-java-5.2.0.zip .
RUN unzip newrelic-java-5.2.0.zip
RUN aws s3 cp s3://deployment.lenskartprod.internal/settings.xml /root/.m2/
RUN mvn clean install -DskipTests

FROM openjdk:8-jre-alpine
RUN mkdir -p /app
WORKDIR /app/
COPY --from=builder /app/target/*.jar .
COPY --from=builder /app/newrelic/ ./newrelic/
ENTRYPOINT java $JVM_ARGS -javaagent:/app/newrelic/newrelic.jar -Dserver.port=80 -Dspring.profiles.active=$PROFILE -Dserver.port=80 -jar /app/*.jar
