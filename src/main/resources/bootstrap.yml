spring:
  cloud:
    consul:
      host: ${CONSUL_SERVER:localhost}
      port: ${CONSUL_PORT:8500}
      config:
        acl-token: ${ACL_TOKEN}
        enabled: true
        prefix: ${CONFIG_PREFIX}
        name: nexs-grn,${PROFILE:preprod-k8s}
        format: PROPERTIES
        data-key: ${KV_VERSION:data}
        defaultContext: common
        profileSeparator: ','
        watch:
          enabled: false
    vault:
      enabled: ${VAULT_ENABLED:true}
      uri: ${VAULT_URI:http://vault.infra.svc.cluster.local:8200}
      authentication: ${VAULT_AUTHENTICATION:KUBERNETES}
      kubernetes:
        role: ${VAULT_ROLE:vault-nexs-config-grn-auth-role}
        kubernetes-path: ${VAULT_KUBERNETES-PATH:kubernetes}
        service-account-token-file: ${VAULT_SERVICE-ACCOUNT-TOKEN-FILE:/var/run/secrets/kubernetes.io/serviceaccount/token}
      kv:
        enabled: true
        backend: ${VAULT_BACKED:lenskart/nexs/nexs-config}
        default-context: ${VAULT_DEFAULT_CONTEXT:nexs-grn}
        application-name: ${VAULT_DEFAULT_CONTEXT:nexs-grn}