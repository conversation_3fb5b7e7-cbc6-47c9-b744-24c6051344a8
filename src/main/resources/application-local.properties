spring.application.name=nexs-grn

#DB config inventory
jdbcUrl.in=jdbc:mysql://${INV_MYSQL_HOST:mysql}:3306/inventory?allowPublicKeyRetrieval=true&useSSL=false
dataSource.in.user=${INV_MYSQL_USER}
dataSource.in.password=${INV_MYSQL_PASSWORD}
dataSource.in.cachePrepStmts=true
dataSource.in.prepStmtCacheSize=250
dataSource.in.prepStmtCacheSqlLimit=2048
driverClassName.in=com.mysql.cj.jdbc.Driver
dataSource.in.driverClassName=com.mysql.cj.jdbc.Driver

#database
spring.jpa.hibernate.ddl-auto=none
spring.datasource.url=**********************://${NEXS_MYSQL_HOST:mysql}:3306/nexs?allowPublicKeyRetrieval=true&useSSL=false
spring.datasource.username=${NEXS_MYSQL_USER}
spring.datasource.password=${NEXS_MYSQL_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect

spring.datasource-nexs-archive-read.jdbcUrl=*******************************************************************************************
spring.datasource-nexs-archive-read.username=user_wms
spring.datasource-nexs-archive-read.password=W1ld!7Vgh:9zXyXMp^n4<-m8
spring.datasource-nexs-archive-read.hikari.idle-timeout=10000
spring.datasource-nexs-archive-read.maximum-pool-size=10
spring.datasource-nexs-archive-read.minimum-idle=5
spring.datasource-nexs-archive-read.pool-name=nexsArchiveReadHikariPool
spring.datasource.datasource-nexs-archive-read.hikari.max-lifetime=600000

#hibernate
spring.jpa.hibernate.naming-strategy=org.hibernate.cfg.DefaultNamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#spring.jpa.properties.hibernate.generate_statistics=true
spring.datasource.hikari.maximumPoolSize=20
spring.datasource.hikari.minimumIdle=2
#logging.level.org.hibernate.SQL=DEBUG
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
#spring.jpa.properties.org.hibernate.flushMode=ALWAYS
logging.level.org.hibernate.envers=DEBUG
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_hist
spring.jpa.properties.org.hibernate.envers.revision_field_name=rev
spring.jpa.properties.org.hibernate.envers.revision_type_field_name=rev_type


#logging
#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true



#Redis config
spring.redis.sentinel.master=${REDIS_SENTINEL_MASTER}
spring.redis.password=${REDIS_PASSWORD}
spring.redis.sentinel.nodes=${REDIS_SENTINEL_NODES}

key.prefix=nexs:grn:
nexs.grn.item.scan.count.ttl=3

#Kafka config
grn.topic.name=${GRN_UNICOM_TOPIC:dev-nexs-grn-sync-unicom}
spring.kafka.bootstrap.servers=${KAFKA_BROKERS}

#Base urls
base.url=${GRN_ENTITIES_HOST_PORT:http\://grn-entities\:8080}
vendor.standard.info.url=/nexs/api/grnentities/v1/vendor-standard-info

unicom.base.url=${UNICOM_HOST_PORT:http\://unicom-adapter:8080}
unicom.create.grn=/nexs/api/unicom/grn/v1/create-grn
unicom.check.barcode=/nexs/api/unicom/grn/v1/barcode/{barcode}/{facility}/validation
unicom.fetch.grn=/nexs/api/unicom/grn/v1/data/inflow/receipt/fetch/{facility}/{grn_code}

invoice.base.url=${INVOICE_API_BASE_URL:http\://po-invoice-api\:8080}
facility.base.url=${FACILITY_BASE_URL:http://ui.nexs.preprod.lenskart.com}
#facility.base.url=${FACILITY_API_BASE_URL:http\://nexs-fms-api\:8080}
invoice.pid.sync.url=/nexs/api/invoice/v1/grn/close
invoice.grn.validation.url=/nexs/api/invoice/v1/s2s/validate/grn
invoice.pid.validation.url=/nexs/api/invoice/v1/s2s/validate/grn
facility.details.url=${FACILITY_DETAILS_URL:/internal/facility/}
spring.redis.database=61

#Qc config
grn.qc.reasons.qc_code_1=item is expired
grn.qc.reasons.qc_code_2=item is broken
barcode.refresh.time=600000

invoice.config.lock.ttl=2000
grn.pageSize=10
grn.exportCSV.size=4000
grn.view.pdf.template.version=1
grn.view.pdf.template.id=GRN_VIEW_PDF

auth.filter.include.urls=/*
auth.filter.exclude.urls=

env.prefix=${GRN_NUM_PREFIX:L}

# Auth Config
authorized.app=nexs_grn
filter.exclude.urls=/nexs/api/grn/v1/master/check-grn-closed,/nexs/api/grn/v1/master/grn-details,/nexs/api/grn/v1/master/is-grn-open,/nexs/api/grn/v1/master/closed-grns,/nexs/api/grn/v1/master/get-grn-numbers,/nexs/api/grn/v1/grn-item,/nexs/api/grn/v1/master/create/transfer/grn,/nexs/api/grn/v1/validate/duplicate/barcode,/nexs/api/grn/v1/master/create/invoice/grn,/nexs/api/grn/v1/scan/grn/barcode,/nexs/api/grn/v1/get/barcode/price,/nexs/api/grn/v1/get/barcode/expiryDate,/nexs/api/grn/v1/get/barcode/priceWithTax,/nexs/api/grn/v1/master/internal/create/grn/details,/nexs/api/grn/v1/master/internal/closeAutoGRN/,/nexs/api/grn/v1/master/internal/autoGrn/message/push/ems,/internal/nexs/api/grn/v1/master/create/autoGrn/details
filter.enabled.urls=/*
auth.secret.key=7AcXlf~uXz3sv7WqvjA;po5FroxXB39hmVvkL!)}y_j36]J{bdjZ</%~+}&m'K#7
nexs.auth.private.key=${NEXS_AUTH_PRIVATE_KEY}
lkauth.authme.url=http://lk-auth-api/v1/user/authme
lkauth.token.header.name=X-Lenskart-Auth-Token
lkauth.authme.validation.enabled=true

grn.putaway.base.url=${PUTAWAY_API_BASE_URL:http://10.230.24.9:9094}
putaway.sync.grn.items=${PUTAWAY_SYNC_GRN_ITEMS:nexs-putaway-sync-grn-items}
putaway.redis.cache.time=28800000

#Retry Logic max attempt
retryMaxAttempts=3
retryMaxDelay=1000

#Email properties
from.email.id=${FROM_MAIL_ID:<EMAIL>}
to.email.ids=${TO_MAIL_ID:<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>}
retry.fail.mail.subject=Retry failed for GRN Service
sendgrid.apiKey=${SEND_GRID_KEY:*********************************************************************}
ims.sync.grn.items=${IMS_SYNC_GRN_ITEMS:nexs-ims-sync-grn-items}

server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.suffix=.log
server.tomcat.accesslog.prefix=access_log
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd
server.tomcat.basedir=tomcat
server.tomcat.accesslog.directory=logs
server.tomcat.accesslog.pattern={"remote_ip_address":"%a" ,"local_ip_address":"%A", "remote_user":"%u","time":"%t","path":"%U", "request_first_line":"%r","bytes":"%b", "referrer": "%{Referer}i" , "user_agent":"%{User-Agent}i" , "request_thread":"%I", "response_code":"%s", "url":"%U" , "response_time":"%D"}

ims.base.url=${IMS_BASE_URL}
ims.stockInAndOutV2.url=/nexs/api/ims/stockInAndOutV2

management.health.elasticsearch.enabled: false
kafka.topic.ems.exception.handler=${NEXS_EMS_TOPIC:nexs-ems}
jit.barcode.ems.sync.retry.count=5

# WM Service
athena.wm.base.url=${ATHENA_WM_BASE_URL:http://wm.scm.preprod-eks.internal}
create.invoiceItem.cl.url=/nexs/api/invoice/v1/createPurchaseInvoiceItemForCl

unicom.adaptor.fetch.barcode=/nexs/api/unicom/nrgp/v1/fetchItemBarcode
unicom.fetch.barcode.facility=${UNICOM_FETCH_FACILITY:DK02}
#unicom.fetch.barcode.facility=LKH03

grn.ims.batchSize=${IMS_BATCH_SIZE:4}

nexs.ims.fetchBarcodeItemDetails.url=/nexs/api/ims/fetchBarcodeItemDetails
nexs.ims.response.size=${IMS_BARCODE_SIZE:4}
nexs.ims.fetch.barcode.detail.size=${IMS_FETCH_BARCODE_SIZE:4}

unicom.adaptor.fetch.grn=/nexs/api/unicom/grn/v1/data/inflow/receipt/fetch/
fetch.barcode.expiry.date.list=${BARCODE_EXPIRY_DATE_BATCH_SIZE:50}

inventoryAdapterClient.baseurl=${INVENTORY_ADAPTER_BASE_URL}
inventoryAdapterClientV1.timeout=8000
facility.code.list=0QNXS,QNXS2
nexs.inventory.transfer.url=${NEXS_INVENTORY_TRANSFER_BASE_URL}

#Consul Config
spring.cloud.consul.host=${CONSUL_SERVER:localhost}
spring.cloud.consul.port=${CONSUL_PORT:8500}
spring.cloud.consul.discovery.enabled=false
spring.cloud.consul.discovery.register=false

ims.validateStockInAndOutV2.url=/nexs/api/ims/validate/barcode

close.grn.sync.unsynced.putaway.barcode.count=${CLOSE_GRN_SYNC_UNSYNCED_PUTAWAY_BARCODE_COUNT:50}
close.grn.retry.to.sync.barcode.in.putaway.time=${CLOSE_GRN_RETRY_TO_SYNC_BARCODE_TIME:10}

close.grn.reconcile.exclude.category=NA

closeGrnCreateGrnItemPutaway.async.threadPool.corePoolSize=10
closeGrnCreateGrnItemPutaway.async.threadPool.awaitTerminationSeconds=120
closeGrnCreateGrnItemPutaway.async.threadPool.maxPoolSize=20
closeGrnCreateGrnItemPutaway.async.threadPool.queueCapacity=10
closeGrnCreateGrnItemPutaway.queueSize=10

#Expiry Days To add
nexs.po.procurement.type.ttl.in.hrs=1
expiry.months.category.JIT=12
expiry.months.category.BULK_SO=24
expiry.months.category.19153=12
expiry.months.category.24148=12
expiry.months.category.CL=18

ims.stockInwardUpdate.url=/nexs/api/ims/stockInwardUpdate

expiry.months.mapping={"LKIN": { "CL": {"DEFAULT": {"19153": 12, "24148": 12, "DEFAULT": 18}, "JIT": {"DEFAULT": 12}}}, "LKAE": {"CL": {"DEFAULT": {"DEFAULT": 24}}}}