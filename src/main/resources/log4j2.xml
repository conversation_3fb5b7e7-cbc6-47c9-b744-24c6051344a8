<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">
	<Scripts>
		<Script name="selector" language="javascript"><![CDATA[
            var result;
            if (logEvent.getContextData().containsKey("traceId") && logEvent.getContextData().getValue("traceId") != null) {
                result = "request";
            } else {
                result = "default";
            }
            result;
            ]]></Script>
	</Scripts>
	<Appenders>
		<Console name="ApplicationInfoLog" target="SYSTEM_OUT">
			<JsonLayout compact="true" eventEol="true" stacktraceAsString="true" objectMessageAsJsonObject="true">
				<KeyValuePair key="@timestamp" value="$${date:yyyy-MM-dd'T'HH:mm:ss.SSSZ}"/>
				<KeyValuePair key="service" value="$${bundle:application:spring.application.name}"/>
				<KeyValuePair key="traceId" value="$${ctx:traceId}"/>
				<KeyValuePair key="spanId" value="$${ctx:spanId}"/>
				<KeyValuePair key="client" value="$${ctx:client}"/>
			</JsonLayout>
		</Console>
	</Appenders>
	<Loggers>
		<AsyncLogger level="info" name="com.lenskart" additivity="false">
			<AppenderRef ref="ApplicationInfoLog"/>
		</AsyncLogger>
		<AsyncRoot level="info">
			<AppenderRef ref="ApplicationInfoLog"/>
		</AsyncRoot>
	</Loggers>
</Configuration>