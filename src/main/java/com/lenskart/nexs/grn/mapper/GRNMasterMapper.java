package com.lenskart.nexs.grn.mapper;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.lenskart.nexs.grn.constants.GRNConstants;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import com.lenskart.nexs.grn.dto.GRNMasterDTO;
import com.lenskart.nexs.grn.dto.request.CreateGRNMasterDTO;
import com.lenskart.nexs.grn.dto.request.CreateGRNPIDTO;
import com.lenskart.nexs.grn.model.GRNMaster;
import com.lenskart.nexs.grn.model.PIDEstimatedQty;
import com.lenskart.nexs.grn.util.GRNUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

@Mapper
public interface GRNMasterMapper extends GRNConstants {

    GRNMasterMapper INSTANCE = Mappers.getMapper(GRNMasterMapper.class);

    @Mappings({
            @Mapping(target = "facility", source = "createGRNMasterDTO.facilityCode"),
            @Mapping(target = "poId", source = "createGRNMasterDTO.invoice.poId"),
            @Mapping(target = "invoiceId", source = "createGRNMasterDTO.invoice.invoiceRefNum"),
            @Mapping(target = "createdBy", expression = "java(getUser())"),
            @Mapping(target = "updatedBy", expression = "java(getUser())")
    })
    GRNMaster mapToGRNMaster(CreateGRNMasterDTO createGRNMasterDTO) throws Exception;

    @Mappings({
            @Mapping(target = "grnCode", source = "grnCode"),
            @Mapping(target = "grnStatus", source = "grnStatus"),
            @Mapping(target = "facility", expression = "java(getFacility())"),
            @Mapping(target = "updatedBy", expression = "java(getUser())"),
            @Mapping(target = "updatedAt", expression = "java(getTimeStamp())")
    })
    GRNMaster mapToGRNMaster(GRNMasterDTO grnMasterDTO) throws Exception;

    @Mappings({
            @Mapping(target = "facility", expression = "java(getFacility())"),
            @Mapping(target = "poId", source = "meta.invoice.poId"),
            @Mapping(target = "invoiceId", source = "meta.invoice.invoiceRefNum"),
//            @Mapping(target = "estimatedTotalQuantity", source = "pid", qualifiedByName = "estimated_qty_pid"),
//            @Mapping(target = "estimatedQuantityList", source = "pid", qualifiedByName = "estimated_qty_history_pid"),
            @Mapping(target = "createdBy", expression = "java(getUser())"),
            @Mapping(target = "updatedBy", expression = "java(getUser())")
    })
    GRNMaster mapToGRNMaster(CreateGRNPIDTO createGRNPIDTO) throws Exception;

    default String getFacility() {
        String facility = MDC.get(FACILITY_CODE);
        if(facility == null)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code must be passed in header");
        return facility;
    }

    default String getUser() {
        return Objects.nonNull(MDC.get(USER_ID))? MDC.get(USER_ID) : "nexs-user";
    }

    default Timestamp getTimeStamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    @Named("estimated_qty_pid")
    default List<PIDEstimatedQty> getEstimatedQtyPID(PIDEstimatedQty pidEstimatedQty) {
        return new ArrayList<>(Collections.singletonList(pidEstimatedQty));
    }

//    @Named("estimated_qty_history_pid")
//    default List<PIDEstimatedQtyHistory> getEstimatedQtyHistoryPID(PIDEstimatedQty pidEstimatedQty) {
//        return new ArrayList<>(
//                Collections.singletonList(
//                        new PIDEstimatedQtyHistory(
//                                pidEstimatedQty.getPid(),
//                                new ArrayList<>(Collections.singleton(pidEstimatedQty.getEstimatedQuantity()))
//                        )
//                )
//        );
//    }
}
