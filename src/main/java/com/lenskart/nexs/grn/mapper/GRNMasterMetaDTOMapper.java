package com.lenskart.nexs.grn.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.model.GRNMaster;

@Mapper
public interface GRNMasterMetaDTOMapper {

    GRNMasterMetaDTOMapper INSTANCE = Mappers.getMapper(GRNMasterMetaDTOMapper.class);

    @Mappings({
            @Mapping(target = "grnCode", source = "grnMaster.grnCode"),
            @Mapping(target = "unicomGrnCode", source = "grnMaster.unicomGrnCode"),
            @Mapping(target = "invoice", source = "grnMaster.invoice"),
            @Mapping(target = "po", source = "grnMaster.po")
    })
    GRNMasterMetaDTO mapToGRNMasterMetaDTO(GRNMaster grnMaster);
    
}
