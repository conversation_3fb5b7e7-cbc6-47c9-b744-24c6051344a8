package com.lenskart.nexs.grn.constants;

public interface GRNConstants {

    //states
    public String GRN_STATUS_CREATED = "created";
    public String GRN_STATUS_CLOSED = "closed";
    public String GRN_STATUS_IN_PROGRESS = "in_progress";
    public String GRN_STATUS_QC_PENDING = "qc_pending";
    public String GRN_STATUS_QC_REJECTED = "qc_rejected";
    public String GRN_STATUS_QC_COMPLETE = "qc_completed";
    public String GRN_STATUS_QC_CLOSED = "closed";
    public String GRN_STATUS_QC_PAUSED = "paused";

    //redis
    public String GRN_NUM_PREFIX = "G";
    public String HD = "-";
    public String GRN = "GRN";
    public String IQC_GRN_DONE = "IQC_GRN_DONE";
    public int MARCH = 3;
    public String ZERO = "0";
    public String US = "_";
    public String INVOICE = "invoice";
    public String INVOICE_GRN = "invoice_grn";
    public String INVOICE_CL = "invoice_cl";
    public String ASN = "asn";
    public String SEQ_KEY = "seq";
    public String TOTAL_SCANNED = "TOTAL_SCANNED";
    public String TOTAL_SCANNED_COUNT = "TOTAL_SCANNED_COUNT";
    public String TOTAL_PASSED = "TOTAL_PASSED";
    public String TOTAL_FAILED = "TOTAL_FAILED";
    public String TOTAL_QTY = "TOTAL_QTY";
    public String TOTAL_PRICE = "TOTAL_PRICE";
    public String ITEM_PRICE = "ITEM_PRICE";
    public String GRN_TOTAL_PRICE = "GRN_TOTAL_PRICE";
    public String QC_CONFIG = "QC_CONFIG";
    public String STATUS = "STATUS";
    public String PASSED = "PASSED";
    public String FAILED = "FAILED";
    public String PENDING = "PENDING";
    public String QC_FAIL = "fail";
    public String QC_PASS = "pass";
    public String QC_MASTER_4 = "qc_master:4:";
    public String QC_MASTER_3 = "qc_master:3:";
    public String QC_MASTER_2 = "qc_master:2:";
    public String QC_MASTER_1 = "qc_master:1:";
    public String DEL = ":";
    public String QC_GRADIENT = "qc_gradient:";
    public String GRN_MANUAL_OVERRIDE = "MANUAL_OVERRIDE";
    public String BOX_REQUIRED = "BOX_REQUIRED";
    public String BARCODE_INVALID = "invalid barcode";
    public String BARCODE_DUPLICATE = "duplicate barcode";
    public String ESTIMATED_QTY = "ESTIMATED_QTY";
    public String SCANNED = "SCANNED";
    public String LOCK = "LOCK";
    public String GET_INVOICE_CONF = "getInvoiceConfig";
    public String SET_NEXT_GRADIENT = "setNextGradientLevel";
    public String SET_NEXT_GRADIENT_EX = "setNextGradientLevelException";
    public String SAVE_EX = "saveException";
    public String UPDATE_EX = "updateException";

    //GRN Sequence
    public String GRN_LEN_FORMAT = "%06d";

    //RESPONSE MESSAGES
    public String GRN_MASTER_CREATED = "GRN Created";
    public String GRN_MASTER_FETCHED = "Successfully got GRN";
    public String GRN_MASTER_UPDATED = "Successfully updated GRN";
    public String GRN_MASTER_CLOSED = "Successfully closed GRN";
    public String GRN_PID_ADDED = "Successfully pid added to GRN";
    public String GRN_GET_BOX_SUCCESS = "Successfully got boxes";
    public String ITEM_SCAN_SUCCESS = "Successfully scanned item";
    public String GRN_SUMMARY_SUCCESS = "Successfully fetched grn summary";
    public String GRN_PID_FETCHED = "Successfully fetched PID";
    public String GRN_SAMPLING_QUANTITY = "Successfully fetched Sampling Quantity";
    public String GRN_SUCCESS = "Successfull";

    //EXCEPTION MESSAGES
    public String GRN_EXCEPTION_CREATE_GRN = "Error creating GRN";
    
    //valid user actions
    public String USER_ACTION_ASSIGNMENT = "assignment";

    // GRN Master/Item Actions
    public String CREATE = "CREATE";
    public String CLOSE = "CLOSE";
    public String UPDATE = "UPDATE";
    public String SAVE = "SAVE";
    public String DELETE = "DELETE";
    public String DELETE_PO_INVOICE_COUNT = "DELETE_PO_INVOICE_COUNT";

    //Categories
    public Integer CONTACT_LENS = 11354;
    public Integer CONTACT_LENS_SOLUTION = 19153;

    //NEXS
    public String NEXS = "NEXS";
    public String NO_BOX = "NO_BOX";
    public String QC_FAILED = "qc_failed";
    public String QC_PASSED = "qc_passed";
    public String SUCCESS = "SUCCESS";
    public String USER_ID = "USER_ID";
    public String FACILITY_CODE = "FACILITY_CODE";

    //Barcode
    public String ITEM = "item";
    public String BOX = "box";

    // Box statuses
    public String REMOVED = "Removed";
    public String AVAILABLE = "Available";
    public String INUSE = "InUse";
    
    
    //Unicom create Grn Request custom fields
    public String SEND_TO_PARTY = "sendToParty";
    public String HANDOVER_BY = "handoverBy";
    public String B2B_INVOICE_DATE = "b2bInvoiceDate";
    public String BILL_OF_ENTRY_AMOUNT = "BillOfEntryAmount";
    public String BILL_OF_ENTRY_DATE = "BillOfEntryDate";

    // GRN Supervisor Role
    String CAN_CLOSE_ANY_GRN = "can_close_any_grn";

//    GRN Send Request to IMS contants
    String GRN_LOCATION = "GRN";
    String GRN_QC_PASS = "GRN_QC_PASS";
    String GRN_QC_FAIL = "GRN_QC_FAIL";
    String GRN_QC_PASS_IN_TRANSIT = "GRN_QC_PASS_IN_TRANSIT";
    String TRANSFER_IN_TRANSIT = "TRANSFER_IN_TRANSIT";

    String TRANSFER_TYPE = "TRANSFER";
    String JIT_TYPE = "JIT";
    String REGULAR_TYPE = "REGULAR";
    String DO = "DO";

    String JIT_LOCATION = "JIT";
    String AUTO_GRN_LOCATION = "AUTO_GRN";
    String FITTING_ID_META_DATA = "FITTING_ID";
    String UW_ITEM_ID_META_DATA = "UW_ITEM_ID";
    String SYNCED_STATUS_META_DATA = "SYNCED_STATUS";
    String EMS_SYNCED_RETRY_COUNT = "EMS_SYNCED_RETRY_COUNT";
    String PID_SWAP_STATUS_META_DATA = "PID_SWAP_ALLOW";

    String CL_TYPE = "CL";
    String CL_USER = "CL_USER";
    String TRANSFER_USER = "TRANSFER_USER";
    Integer GRN_EXPIRY_DAYS_TO_ADD = 18;
    Integer JIT_EXPIRY_DAYS_TO_ADD = 12;
    Integer UNICOM_MAX_RETRY = 5;
    String UNICOM_FACILITY_CODE = "DK02";

    String IQC_IN_PROGRESS = "IQC_IN_PROGRESS";
    String IQC_COMPLETE = "IQC_COMPLETE";
    String IQC_FAIL ="IQC_FAIL";
    String IQC_PASS ="IQC_PASS";

    String IQC_DONE = "IQC_DONE";

    Integer GRN_DAYS_AFTER = 7;
    Integer IMS_SYNC_MIN_BEFORE = 2;
    Integer MAX_RETRY_COUNT = 10;

    String GRN_ITEM_PASS = "pass";
    String GRN_ITEM_FAIL = "fail";
    String DISCARDED = "DISCARDED";

    String BULK = "BULK";
    String NEXS_END_POINT_FOR_BARCODE_COSTPRICE="/nexs/api/v1/iwt/transferBarcodes/internal/getByCostPriceByBarcodesAndFacilityCode";

    String GRN_VALIDATE_UPDATED_BY= "GRN_SYSTEM";
    String MANUAL_JIT= "MANUAL_JIT";
    String DEFAULT_LEGAL_OWNER= "LKIN";

}