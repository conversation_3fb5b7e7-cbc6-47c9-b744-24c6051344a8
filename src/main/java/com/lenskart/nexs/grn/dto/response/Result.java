package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Result<T> {

    private T data;

    @JsonInclude(Include.NON_NULL)
    private GRNMasterMetaDTO meta;
    
    public Result(T data) {
    	this.data = data;
    }
    
    public Result(T data, GRNMasterMetaDTO meta) {
    	this.data = data;
    	this.meta = meta;
    }
}
