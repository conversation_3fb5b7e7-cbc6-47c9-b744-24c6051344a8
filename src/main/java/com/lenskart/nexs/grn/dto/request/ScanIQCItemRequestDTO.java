package com.lenskart.nexs.grn.dto.request;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScanIQCItemRequestDTO {
    @JsonProperty(value = "barcode")
    private String barcode;
    
    @JsonProperty("boxcode")
    private String boxCode;

    @JsonProperty(value = "grnCode")
    @NotBlank(message = "grn_code must not be blank")
    private String grnCode;

    @JsonProperty(value = "pid")
    @NotBlank(message = "pid must not be blank")
    private String pid;

    @JsonProperty(value = "qc_status")
    @NotBlank(message = "qc_status must not be blank")
    private String qcStatus;

    @JsonProperty("qc_reason")
    private String qcReason;
    
    @JsonProperty("qc_code")
    private String qcCode;

    @JsonProperty("created_at")
    private Timestamp createdAt;
    @JsonProperty("updated_at")
    private Timestamp updatedAt;
    @JsonProperty("created_by")
    private String createdBy;
    @JsonProperty("updated_by")
    private String updatedBy;

}

