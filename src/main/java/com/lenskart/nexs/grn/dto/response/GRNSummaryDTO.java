package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.sql.Timestamp;
import java.util.List;

@Data
public class GRNSummaryDTO {

    @JsonProperty("grn_code")
    private String grnCode;

    @JsonProperty("unicom_grn_code")
    private String unicomGrnCode;

    @JsonProperty("grn_status")
    private String grnStatus;

    @JsonProperty("po_id")
    private String poId;

    private String vendor;

    private String currency;

    @JsonProperty("invoice_date")
    private String invoiceDate;

    @JsonProperty("created_at")
    private Timestamp createdAt;

    @JsonProperty("total_scanned")
    private long totalScanned;

    @JsonProperty("total_passed")
    private long totalPassed;

    @JsonProperty("total_failed")
    private long totalFailed;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("invoice_id")
    private String invoiceId;

    @JsonProperty("total_quantity")
    private long totalQuantity;

    @JsonProperty("vendor_name")
    private String vendorName;

    @JsonProperty("total_received_amount")
    private Double totalReceivedAmount;

    @JsonProperty("total_rejected_amount")
    private Double totalRejectedAmount;

    @JsonProperty("vendor_invoice_num")
    private String vendorInvoiceNum;

    @JsonProperty("vendor_invoice_date")
    private String vendorInvoiceDate;

    @JsonProperty("from_party")
    private String fromParty;

    private int boxes;

    private List<PIDSummaryDTO> pids;
    
    @JsonProperty("b2b_invoice_date")
    private String b2bInvoiceDate;
    
    @JsonProperty("send_to_party")
    private String sendToParty;
    
    @JsonProperty("handover_party")
    private String handoverParty;
    
    @JsonProperty("bill_of_entry_number")
    private String billOfEntryNumber;
    
    @JsonProperty("bill_of_entry_amount")
    private Double billOfEntryAmount;
    
    @JsonProperty("bill_of_entry_date")
    private String billOfEntryDate;

    @JsonProperty("total_tax_price")
    private Double totalPriceWithTax;

    @JsonProperty("order_accepted_quantity")
    private int orderAcceptedQuantity;

    @JsonProperty("order_rejected_quantity")
    private int orderRejectedQuantity;

    @JsonProperty("total_invoice_qty")
    private int totalInvoiceQty;

}
