package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;

@Data
public class IqcGrnScanItemResponseDTO {

    @Column(name = "barcode")
    private String barCode;

    @Column(name = "pid")
    private String pid;

    @Column(name = "total_qty")
    private Integer totalQty;

    @Column(name = "sampling_qty")
    private Integer samplingQty;

    @Column(name = "qc_pass_qty")
    private Integer qcPassQty;

    @Column(name = "qc_fail_qty")
    private Integer qcFailQty;

    @Column(name = "total_grn_qc_pass_qty")
    private Integer totalGRNQcPassQty;

    @Column(name = "total_grn_qc_fail_qty")
    private Integer totalGRNQcFailQty;

    private Integer totalSamplingQty;
}
