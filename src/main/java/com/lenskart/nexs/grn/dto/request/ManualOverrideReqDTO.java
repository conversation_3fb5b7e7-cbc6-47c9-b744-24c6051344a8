package com.lenskart.nexs.grn.dto.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import javax.validation.Valid;

@Data
public class ManualOverrideReqDTO {

	@JsonProperty("manual_override_allowed")
	@Valid
	private List<GRNPidListDTO> manualOverrideAllowed;
	
	@JsonProperty("manual_override_rejected")
	@Valid
	private List<GRNPidListDTO> manualOverrideRejected;
	
}
