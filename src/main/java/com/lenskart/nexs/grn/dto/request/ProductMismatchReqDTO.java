package com.lenskart.nexs.grn.dto.request;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductMismatchReqDTO {

	@JsonProperty(value = "grn_code", required = true)
	@NotBlank(message = "GRN code cannot be blank")
	private String grnCode;
	
	@JsonProperty(value = "pid", required = true)
	@NotBlank(message = "pid cannot be blank")
	private String pid;
	
	@JsonProperty(value = "scanned_pid", required = true)
	@NotBlank(message = "scanned pid cannot be blank")
	private String scannedPid;
	
	@JsonProperty(value = "barcode", required = true)
	@NotBlank(message = "barcode cannot be blank")
	private String barcode;
	
	@JsonProperty(required = true)
	@NotBlank(message = "action cannot be blank")
	private String action;
	
	@JsonProperty(required = true)
	@NotBlank(message = "reason cannot be blank")
	private String reason;
	
	@NotNull(message = "meta must not be null")
    @Valid
    private GRNMasterMetaDTO meta;
}
