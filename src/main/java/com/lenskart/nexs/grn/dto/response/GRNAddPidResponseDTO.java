package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import lombok.Data;

@Data
public class GRNAddPidResponseDTO {

	@JsonProperty("grn_code")
	private String grnCode;
	
	@JsonProperty("invoice_ref_num")
	private String invoiceRefNum;
	
	@JsonProperty("invoice_date")
	private String invoiceDate;
	
	@JsonProperty("grn_status")
	private String grnStatus;
	
	@JsonProperty("po_id")
	private String poId;
	
	@JsonProperty("vendor")
	private String vendor;
	
	@JsonProperty("grn_pid")
	private GRNPidDetailsDTO grnPidInfo;
	
	@JsonIgnore
	private GRNMasterMetaDTO meta;
}
