package com.lenskart.nexs.grn.dto.request;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@Valid
public class GRNPidListDTO {

	@JsonProperty("grn_code")
	@NotBlank(message = "grn code is blank in list")
	private String grnCode;
	
	@NotBlank(message = "pid is blank in list")
	private String pid;
}
