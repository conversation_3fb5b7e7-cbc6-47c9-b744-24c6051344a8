package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.model.Invoice;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNProductResponse {

    @JsonProperty("grn_code")
    private String grnCode;

    @JsonProperty("grn_status")
    private String grnStatus;

    @JsonProperty("created_on")
    private String createdOn;

    @JsonProperty("vendor_name")
    private String vendorName;

    private String currency;

    @JsonProperty("po_id")
    private String poId;

    @JsonProperty("assigned_to")
    private String assignedTo;

    @JsonProperty("invoice_ref_num")
    private String invoiceRefNum;

    @JsonIgnore
    private Invoice invoice;

    @JsonIgnore
    private Map<String, GRNProduct> pids;

    private List<GRNProduct> products;

    private boolean iqc;

    @JsonProperty("putaway_list")
    private List<String> putawayList;

    public GRNProductResponse(String grnCode, String grnStatus, String createdOn, String poId, String assignedTo, Invoice invoice) {
        this.grnCode = grnCode;
        this.grnStatus = grnStatus;
        this.createdOn = createdOn;
        this.poId = poId;
        this.invoice = invoice;
        this.assignedTo = assignedTo;
    }
}
