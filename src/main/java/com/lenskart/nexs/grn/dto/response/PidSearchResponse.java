package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class PidSearchResponse {

	@JsonProperty("grn_code")
	private String grnCode;
	
	private String pid;
	
	@JsonProperty("created_by")
	private String createdBy;
	
	@JsonProperty("invoice_qty")
	private Long invoiceQty;
	
//	@JsonProperty("estimated_qty")
//	private Integer estimatedQty;
	
//	@JsonProperty("sampling_pct")
//	private Integer samplingPercent;
	
	@JsonProperty("failure_pct")
	private Integer failurePercent;
	
	@JsonProperty("qc_pass")
	private Integer qcPass;
	
	@JsonProperty("qc_fail")
	private Integer qcFail;
	
	@JsonProperty("failed_since")
	private String failedSince;

	@JsonProperty("category_id")
	private int categoryId;

	@JsonIgnore
	private String grnStatus;

	@JsonIgnore
	private String createdOn;

	@JsonIgnore
	private String vendorName;

	@JsonProperty("po_id")
	private String poId;
}
