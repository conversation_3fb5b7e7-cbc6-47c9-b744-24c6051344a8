package com.lenskart.nexs.grn.dto.request;

import com.lenskart.nexs.grn.enums.Source;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

@NoArgsConstructor
@Setter
@Getter
@AllArgsConstructor
@SuperBuilder
public class BaseEventCreationRequest {

    @NotBlank
    FinanceServiceEventTypes eventName;
    @NotBlank
    String eventId;
    Object payload;
    Source source;
    String legalEntity;
    String facilityCode;
    public enum FinanceServiceEventTypes{
        GRN
    }
}
