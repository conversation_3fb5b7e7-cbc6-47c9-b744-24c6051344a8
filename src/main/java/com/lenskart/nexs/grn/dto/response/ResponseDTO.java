package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ResponseDTO<T> {

    @JsonProperty("error_code")
    private String errorCode;

    @JsonProperty("display_message")
    private String displayMessage;

    private T result;
    
    public ResponseDTO(T result) {
    	this.result = result;
    }
    
    public ResponseDTO(T result, String message) {
    	this.result = result;
    	this.displayMessage = message;
    }

}
