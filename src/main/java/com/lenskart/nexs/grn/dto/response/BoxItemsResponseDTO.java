package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BoxItemsResponseDTO {

    @JsonProperty("qc_passed")
    private Map<String, List<Map<String, Object>>> qcPassed;

    @JsonProperty("qc_failed")
    private List<Map<String, Object>> qcFailed;
    
    @JsonProperty("others")
    private List<Map<String, Object>> others;
}
