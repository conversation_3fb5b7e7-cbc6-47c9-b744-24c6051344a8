package com.lenskart.nexs.grn.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.PurchaseOrder;
import com.lenskart.nexs.putaway.model.response.Putaway;

import lombok.Data;

@Data
public class GRNMasterMetaDTO {

    @NotNull(message = "grn_code must not be null")
    @NotBlank(message = "grn_code must not be blank")
    @JsonProperty(value = "grn_code", required = true)
    private String grnCode;

    @JsonProperty(value = "unicom_grn_code")
    private String unicomGrnCode;

    @NotNull(message = "invoice must not be null")
    @Valid
    private Invoice invoice;

    @NotNull(message = "po must not be null")
    @Valid
    private PurchaseOrder po;

    @JsonProperty("new_putaway")
    private Putaway newPutaway;

    @JsonProperty("old_putaway")
    private Putaway oldPutaway;
}
