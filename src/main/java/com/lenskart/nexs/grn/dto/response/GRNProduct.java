package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class GRNProduct {

    private String pid;

    @JsonProperty("pid_status")
    private String pidStatus;

    @JsonProperty("vendor_sku")
    private String vendorSku;

    @JsonProperty("batch_code")
    private String lot;

    private long received;

    private long pending;

    private double price;

    private double cgst;

    private double igst;

    private double sgst;

    @JsonProperty("category_id")
    private int categoryId;

    private String description;
}
