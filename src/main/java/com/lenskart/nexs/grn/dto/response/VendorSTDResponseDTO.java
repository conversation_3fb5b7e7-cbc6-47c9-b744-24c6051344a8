package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VendorSTDResponseDTO {

//    @JsonProperty("estimated_qty")
//    private Long estimatedQty;

    @JsonProperty("is_box_required")
    private <PERSON>ole<PERSON> isBoxRequired;

    @JsonProperty("expiry_offset")
    private Integer expiryOffset;

    @JsonProperty("expiry_format")
    private String expiryFormat;

    @JsonProperty("expiry_date_threshold")
    private Long expiryDateThreshold;

    @JsonProperty("lot_offset")
    private Integer lotOffset;

    @JsonProperty("lot_length")
    private Integer lotLength;

    @JsonProperty("total_failed")
    private int totalFailed;

    @JsonProperty("total_scanned")
    private int totalScanned;
}
