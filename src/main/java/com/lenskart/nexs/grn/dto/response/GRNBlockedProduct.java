package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.model.Invoice;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNBlockedProduct {

    @JsonProperty("grn_code")
    private String grnCode;

    @JsonProperty("grn_status")
    private String grnStatus;

    @JsonProperty("created_on")
    private String createdOn;

    @JsonProperty("vendor_name")
    private String vendorName;

    @JsonProperty("po_id")
    private String poId;

    private List<PidSearchResponse> products;

    public GRNBlockedProduct(String grnCode, String grnStatus, String createdOn, String vendorName, String poId) {
        this.grnCode = grnCode;
        this.grnStatus = grnStatus;
        this.createdOn = createdOn;
        this.vendorName = vendorName;
        this.poId = poId;
    }
}
