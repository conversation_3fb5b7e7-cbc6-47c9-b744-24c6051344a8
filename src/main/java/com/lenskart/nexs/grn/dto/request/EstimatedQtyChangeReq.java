package com.lenskart.nexs.grn.dto.request;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EstimatedQtyChangeReq {

	@JsonProperty(value = "grn_code", required = true)
	@NotBlank(message = "grn_code must not be blank")
	private String grnCode;
	
	@JsonProperty(required = true)
	@NotBlank(message = "pid must not be blank")
	private String pid;
	
//	@JsonProperty(value = "estimated_qty", required = true)
//	@NotNull(message = "estimated_qty must not be null")
//	private Long estimatedQty;

	@NotNull(message = "meta must not be null")
	@Valid
	private GRNMasterMetaDTO meta;
}
