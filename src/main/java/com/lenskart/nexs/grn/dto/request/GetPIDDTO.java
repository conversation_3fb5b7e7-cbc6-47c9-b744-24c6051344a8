package com.lenskart.nexs.grn.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetPIDDTO {

    @NotNull(message = "scanned_id must not be null")
    @NotBlank(message = "scanned_id must not be blank")
    @JsonProperty(value = "scanned_id", required = true)
    private String scannedId;

    @NotBlank(message = "grn_code must not be blank")
    @JsonProperty(value = "grn_code")
    private String grnCode;

    @NotNull(message = "meta must not be null")
    @Valid
    private GRNMasterMetaDTO meta;

    private String type;

    @JsonProperty(value = "invoice_level")
    private String invoiceLevel;
}
