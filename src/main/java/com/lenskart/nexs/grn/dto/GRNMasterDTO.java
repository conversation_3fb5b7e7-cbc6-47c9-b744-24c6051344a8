package com.lenskart.nexs.grn.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class GRNMasterDTO {

    @NotNull(message = "grn_code must not be null")
    @NotBlank(message = "grn_code must not be blank")
    @JsonProperty("grn_code")
    private String grnCode;

    @NotNull(message = "status must not be null")
    @NotBlank(message = "status must not be blank")
    @JsonProperty("status")
    private String grnStatus;

    @NotNull(message = "meta must not be null")
    @Valid
    private GRNMasterMetaDTO meta;
}
