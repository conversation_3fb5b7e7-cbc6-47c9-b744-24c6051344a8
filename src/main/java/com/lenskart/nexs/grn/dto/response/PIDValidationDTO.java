package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.model.Product;
import lombok.Data;

import java.util.List;

@Data
public class PIDValidationDTO {

    @JsonProperty("invoice_ref_no")
    private String invoiceRefNum;

    private List<Product> items;

    public PIDValidationDTO(String invoiceRefNum, List<Product> items) {
        this.invoiceRefNum = invoiceRefNum;
        this.items = items;
    }
}
