package com.lenskart.nexs.grn.dto.response;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import lombok.Data;

@Data
public class GetGRNResponseDTO {

	@JsonProperty("grn_code")
	private String grnCode;
	
	@JsonProperty("invoice_ref_num")
	private String invoiceRefNum;

	@JsonProperty("vendor_invoice_number")
	private String vendorInvoiceNumber;
	
	@JsonProperty("invoice_date")
	private String invoiceDate;
	
	@JsonProperty("grn_status")
	private String grnStatus;
	
	@JsonProperty("po_id")
	private String poId;
	
	@JsonProperty("vendor")
	private String vendor;
	
	@JsonProperty("grn_pids")
	private List<GRNPidStatusDTO> grnPids;
	
	@JsonIgnore
	private GRNMasterMetaDTO meta;

	@JsonProperty("type")
	private String type;

	@JsonProperty("legal_owner")
	private String legalOwner;

	@JsonProperty("invoice_level")
	private String invoiceLevel;
}
