package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNPidDetailsDTO {

	private String pid;
	
	@JsonInclude(Include.NON_NULL)
	@JsonProperty("grn_code")
	private String grnCode;
	
	@JsonInclude(Include.NON_NULL)
	@JsonProperty("invoice_reference_number")
	private String invoiceRefNumber;
	
	@JsonInclude(Include.NON_NULL)
	@JsonProperty("pid_description")
	private String pidDesc;

	@JsonInclude(Include.NON_NULL)
	private String vendor;

	private String brand;

	private Integer categoryId;

	private String status;

	private String currency;
	
//	@JsonInclude(Include.NON_NULL)
//	@JsonProperty("sampling_percent")
//	private Integer samplingPercent;

	@JsonProperty("total_scanned")
	private int totalScanned;

	@JsonProperty("qc_fail_count")
	private int qcFailedCount;

	@JsonProperty("qc_pass_count")
	private int qcPassCount;

//	@JsonProperty("estimated_qty")
//	private Long estimatedQty;
	
	@JsonProperty("min_to_scan")
	private Long minToScan;

	private Double price;
	
	@JsonInclude(Include.NON_NULL)
	@JsonProperty("tax_rate")
	private Double taxRate;

	@JsonInclude(Include.NON_NULL)
	@JsonProperty("is_box_required")
	private Boolean isBoxRequired;

	@JsonInclude(Include.NON_NULL)
	@JsonProperty("expiry_offset")
	private Integer expiryOffset;

	@JsonInclude(Include.NON_NULL)
	@JsonProperty("expiry_format")
	private String expiryFormat;

	@JsonInclude(Include.NON_NULL)
	@JsonProperty("expiry_date_threshold")
	private Long expiryDateThreshold;

	@JsonInclude(Include.NON_NULL)
	@JsonProperty("lot_offset")
	private Integer lotOffset;

	@JsonInclude(Include.NON_NULL)
	@JsonProperty("lot_length")
	private Integer lotLength;

	@JsonProperty("sampling_percent")
	private Double samplingPercent;

	@JsonInclude(Include.NON_NULL)
	@JsonProperty("is_lot_no_required")
	private Boolean isLotNoRequired;
}
