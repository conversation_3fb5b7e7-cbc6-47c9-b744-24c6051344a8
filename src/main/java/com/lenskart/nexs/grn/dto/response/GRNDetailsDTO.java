package com.lenskart.nexs.grn.dto.response;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNDetailsDTO {

	@JsonProperty("grn_code")
	private String grnCode;

	@JsonProperty("invoice_reference_num")
	private String invoiceReferenceNum;
	
	@JsonProperty("po_id")
	@JsonInclude(Include.NON_NULL)
	private String poId;
	
	@JsonProperty("invoice_date")
	@JsonInclude(Include.NON_NULL)
	private String invoiceDate;
	
	@JsonProperty("po_date")
	@JsonInclude(Include.NON_NULL)
	private String poDate;
	
	@JsonInclude(Include.NON_NULL)
	private String vendor;
	
	@JsonProperty("created_on")
	private Date createdOn;
	
	@JsonProperty("created_by")
	private String createdBy;
	
	@JsonProperty("total_scanned")
	private int totalScanned;
	
	@JsonProperty("total_fail_count")
	private int totalFailCount;
	
	@JsonProperty("grn_status")
	private String grnStatus;
	
	@JsonProperty("grn_pids")
	@JsonInclude(Include.NON_NULL)
	private List<GRNPidDetailsDTO> grnPids;
}
