package com.lenskart.nexs.grn.dto.request;

import java.util.Map;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnicomCreateGRNDTO {

    @JsonProperty(value = "po_id", required = true)
    @NotBlank
    private String poId;

    @JsonProperty(value = "invoice_ref_num", required = true)
    @NotBlank
    private String invoiceReferenceNum;

    @JsonProperty(value = "invoice_date", required = true)
    @NotBlank
    private String invoiceDate;

    @JsonProperty(value = "facility", required = true)
    @NotBlank
    private String facility;
    
    @JsonProperty(value = "custom_fields", required = true)
	private Map<String,Object> customFields;

    @JsonProperty(value = "vendor_invoice_number")
    @NotBlank
    private String vendorInvoiceNumber;
}
