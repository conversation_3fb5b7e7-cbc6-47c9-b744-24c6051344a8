package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import lombok.Data;

@Data
public class GRNItemResponseDTO {

	private String barcode;
	
	@JsonProperty("grn_code")
	private String grnCode;
	
	private String status;
	
	@JsonProperty("qc_status")
	private String qcStatus;
	
	@JsonProperty("qc_reason")
	private String qcReason;
	
	@JsonProperty("qc_master_sampling")
	private int qcMasterSampling;

	@JsonProperty("qc_gradient_sampling")
	private int qcGradientSampling;

	@JsonProperty("qc_failure_threshold")
	private int qcFailureThreshold;

	@JsonProperty("qc_gradient_failure_threshold")
	private int qcGradientFailureThreshold;
	
	@JsonProperty("item_scanned")
	private Integer itemScanned;
	
	@JsonProperty("item_failed")
	private Integer itemFailed;

	@JsonProperty("item_passed")
	private int itemPassed;

//	@JsonProperty("estimated_quantity")
//	private int estimatedQuantity;

//	@JsonProperty("sampling_quantity")
//	private int samplingQuantity;
	
	private GRNMasterMetaDTO meta;
	
	//TODO can have totatscanned and failed quantity also
}
