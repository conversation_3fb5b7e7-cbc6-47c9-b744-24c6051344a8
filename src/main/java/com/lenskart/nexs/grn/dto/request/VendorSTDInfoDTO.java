package com.lenskart.nexs.grn.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorSTDInfoDTO {

    private String pid;

    private String vendor;

    private String brand;

    @JsonProperty("category_id")
    private Integer categoryId;

//    @JsonProperty("estimated_qty")
//    private Long estimatedQty;
}
