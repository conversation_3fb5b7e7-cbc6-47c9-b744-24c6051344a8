package com.lenskart.nexs.grn.dto.request;

import java.sql.Timestamp;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import com.lenskart.nexs.grn.model.Product;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNItemDTO {

	@JsonProperty(value = "barcode", required = true)
	@NotBlank(message = "barcode must not be blank")
	private String barcode;

	@JsonProperty(value = "grn_code", required = true)
	@NotBlank(message = "grn_code must not be blank")
	private String grnCode;

	@JsonProperty(value = "pid", required = true)
	@NotBlank(message = "pid must not be blank")
	private String pid;
	
	@JsonIgnore
	private String facilityCode;

	@JsonProperty("expiry_date")
	private Timestamp expiryDate;

	@JsonProperty("expiry_date_threshold")
	private Long expiryDateThreshold;

	@JsonProperty("lot_no")
	private String lotNo;

	@JsonProperty("qc_pass_box_barcode")
	private String qcPassBoxBarcode;

	@JsonProperty("qc_fail_box_barcode")
	private String qcFailBoxBarcode;

//	@JsonProperty(value = "estimated_quantity")
//	private long estimatedQuantity;

	@JsonProperty(value = "qc_status", required = true)
	@NotBlank(message = "qc_status must not be blank")
	private String qcStatus;
	
	@JsonProperty(value = "qc_fail_code", required = false)
    private String qcFailCode;

	@JsonProperty("qc_reason")
	private String qcReason;

	@JsonProperty(value = "meta")
	private GRNMasterMetaDTO meta;

	@JsonProperty("created_by")
	private String createdBy;

	@JsonProperty("updated_by")
	private String updatedBy;

	@JsonProperty(value = "product", required = true)
	private Product product;

	@JsonProperty(value = "po_id", required = true)
	private String poID;

	@JsonProperty(value = "invoice_id", required = true)
	private String invoiceID;

	@JsonProperty(value = "invoice_ref_number", required = true)
	private String invoiceRefNum;

	@JsonProperty(value = "vendor_code", required = true)
	private String vendorCode;

	@JsonProperty(value = "unicom_grn_code")
	private String unicomGrnCode;

	@JsonProperty(value = "poQuantity", required = true)
	private long poQuantity;

	@JsonProperty(value = "type")
	private String type;

	@JsonProperty(value = "sampling_percent")
	private Double samplingPercent;

	@JsonProperty(value = "putaway_code")
	private Integer putawayCode;

	@JsonProperty(value = "legal_owner")
	private String legalOwner;

	@JsonProperty(value = "invoice_level")
	private String invoiceLevel;

	@JsonProperty(value = "barcodeUrl")
	private String barcodeUrl;

	public void setBarcode(String barcode) {
		this.barcode = barcode != null ? barcode.trim() : null;
	}
}
