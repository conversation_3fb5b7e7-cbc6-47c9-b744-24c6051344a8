package com.lenskart.nexs.grn.dto.request;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.model.PIDEstimatedQty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateGRNPIDTO {

    @NotNull(message = "grnCode must not be null")
    @NotBlank(message = "grnCode must not be blank")
    private String grnCode;

    @NotNull(message = "pid must not be null")
    @Valid
    private PIDEstimatedQty pid;

    @NotNull(message = "meta must not be null")
    @JsonProperty(required = true)
    @Valid
    private GRNMasterMetaDTO meta;

    private String type;

    @JsonProperty(value = "invoice_level")
    private String invoiceLevel;
}
