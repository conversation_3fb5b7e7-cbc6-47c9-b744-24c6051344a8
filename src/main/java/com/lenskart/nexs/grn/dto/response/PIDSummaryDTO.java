package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Data
public class PIDSummaryDTO {

    private String pid;

    private String description;

    @JsonProperty("invoice_quantity")
    private long invoiceQuantity;

//    @JsonProperty("estimated_quantity")
//    private long estimatedQuantity;

//    @JsonProperty("sampling_percent")
//    private int samplingPercent;

    @JsonProperty("total_passed")
    private int totalPassed;

    @JsonProperty("total_failed")
    private int totalFailed;

    @JsonProperty("failed_on")
    private Timestamp failedOn; //

    @JsonProperty("vendor_sku")
    private String vendorSku;

    @JsonProperty("lot_no")
    private String lotNo; //

    private String status;

    @JsonProperty("expiry_date")
    private Timestamp expiryDate; //

    private double price;

    @JsonProperty("grn_closed_quantity")
    private long grnClosedQuantity; //

    @JsonProperty("pending_quantity")
    private long pending_quantity; //

    private String expiry;

    private double priceWithTax;
}
