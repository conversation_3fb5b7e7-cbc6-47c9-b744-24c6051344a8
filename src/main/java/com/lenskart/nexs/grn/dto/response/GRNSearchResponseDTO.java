package com.lenskart.nexs.grn.dto.response;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class GRNSearchResponseDTO {

	@JsonProperty("grn_code")
	private String grnCode;
	
	@JsonProperty("grn_status")
	private String grnStatus;
	
	@JsonProperty("po_num")
	private String poId;
	
	@JsonProperty("invoice_num")
	private String invoiceRefNum;
	
	private String vendor;

	@JsonProperty("vendor_name")
	private String vendorName;

	@JsonProperty("vendor_invoice_number")
	private String vendorInvoiceNumber;
	
	@JsonProperty("created_by")
	private String createdBy;
	
	@JsonProperty("assigned_to")
	private String assignedTo;
	
	@JsonProperty("created_on")
	private Timestamp createdOn;
	
	@JsonProperty("total_qty")
	private Integer totalQty;
	
	@JsonProperty("qc_complete")
	private Integer qcComplete;
}
