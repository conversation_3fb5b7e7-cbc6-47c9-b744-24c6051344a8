package com.lenskart.nexs.grn.dto.request;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.PurchaseOrder;
import lombok.Data;

@Data
public class CreateGRNMasterDTO {

    @NotNull(message = "invoice must not be null")
    @Valid
    private Invoice invoice;

    @NotNull(message = "po must not be null")
    @Valid
    private PurchaseOrder po;

    @JsonIgnore
    private String facilityCode;

    private String type;

}
