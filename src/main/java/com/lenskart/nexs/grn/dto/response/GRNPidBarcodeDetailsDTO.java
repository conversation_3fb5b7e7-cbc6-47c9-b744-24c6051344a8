package com.lenskart.nexs.grn.dto.response;

import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNPidBarcodeDetailsDTO {

	@JsonProperty("grn_code")
	private String grnCode;

	private String barcode;

	private String pid;

	@JsonProperty("qc_status")
	private String qcStatus;
	
	@JsonProperty("qc_reason")
	private String qcReason;
	
	@JsonProperty("scanned_by")
	private String scannedBy;
	
	@JsonProperty("scanned_at")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Timestamp scannedAt;
	
	private Double price;
	
	@JsonProperty("tax_rate")
	private Double taxRate;
	
	
}
