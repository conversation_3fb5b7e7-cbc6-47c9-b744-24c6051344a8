package com.lenskart.nexs.grn.dto.request;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateGRNItemDTO {

	@JsonProperty(value = "grn_code", required = true)
	@NotBlank(message = "grn_code must not be blank")
	private String grnCode;
	
	@JsonProperty(value = "pid", required = true)
	@NotBlank(message = "pid must not be blank")
	private String pid;
	
	@JsonProperty(value = "barcode", required = true)
	@NotBlank(message = "barcode must not be null")
	private String barcode;
	
	@JsonProperty(value = "qc_status", required = true)
	@NotBlank(message = "qc_status must not be null")
	private String qcStatus;
	
	@JsonProperty(value = "qc_fail_code", required = false)
    private String qcFailCode;
	
	@JsonProperty(value = "qc_reason")
	private String qcReason;
	
	@JsonProperty(value = "box_barcode")
	private String boxBarcode;

	@JsonProperty(value = "expiry_date", required = false)
	private Timestamp expiryDate;

	@JsonProperty("green_channel")
	@NotNull(message = "green_channel field must not be null")
	private Boolean greenChannel;
	
	@JsonProperty(required = true)
	@NotNull(message = "meta must not be null")
	@Valid
	private GRNMasterMetaDTO meta;

	String facilityCode;

	@JsonProperty("type")
	private String type;
}
