package com.lenskart.nexs.grn.dto.request;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class IsGRNOpenReqDTO {

	@JsonProperty(value = "invoice_id", required = true)
	@NotBlank(message = "Invoice id is invalid")
	private String invoiceId;
	
	@NotNull 
	@Size(min = 1, message = "List of pids cannot be empty")
	private List<String> pids;
	
	
}
