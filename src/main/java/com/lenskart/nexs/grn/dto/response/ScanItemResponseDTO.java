package com.lenskart.nexs.grn.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.putaway.model.response.Putaway;

import lombok.Data;

@Data
public class ScanItemResponseDTO {

    private String barcode;

    @JsonProperty(value = "grn_code",required = true)
    private String grnCode;

    @JsonProperty("qc_status")
    private String qcStatus;

    @JsonProperty("qc_reason")
    private String qcReason;
    
    @JsonProperty("qc_fail_code")
    private String qcFailCode;

    @JsonProperty("status")
    private String status;

    private int qcMasterSampling;

    private int failureThresholdQcMaster;

//    @JsonProperty("grn_estimated_quantity")
//    private long grnEstimatedQuantity;

    @JsonProperty("sampling_quantity")
    private long samplingQuantity;

    @JsonProperty("total_scanned")
    private long totalScanned;

    @JsonProperty("total_passed")
    private long totalPassed;

    @JsonProperty("total_failed")
    private long totalFailed;

    @JsonProperty("green_flag")
    private boolean greenFlag;

    @JsonProperty("min_scan_quantity")
    private long minScanQuantity;

    @JsonProperty("gradient_shift")
    private int gradientShift;

    @JsonProperty("new_putaway")
    private Putaway newPutaway;

    @JsonProperty("old_putaway")
    private Putaway oldPutaway;
}
