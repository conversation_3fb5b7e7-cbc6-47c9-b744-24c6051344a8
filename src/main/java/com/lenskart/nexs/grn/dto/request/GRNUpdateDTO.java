package com.lenskart.nexs.grn.dto.request;

import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.PurchaseOrder;
import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
public class GRNUpdateDTO {

    @NotNull(message = "invoice must not be null")
    @Valid
    private Invoice invoice;

    @NotNull(message = "po must not be null")
    @Valid
    private PurchaseOrder po;

    private String updatedBy;

    private String facilityCode;
}
