package com.lenskart.nexs.grn.putaway.service;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.ScanItemResponseDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.putaway.mapper.PutawayMapper;
import com.lenskart.nexs.grn.util.CacheUtils;
import com.lenskart.nexs.grn.util.CommonUtils;
import com.lenskart.nexs.grn.util.RetryUtils;
import com.lenskart.nexs.putaway.config.PutAwayConfig;
import com.lenskart.nexs.putaway.constants.CustomReference;
import com.lenskart.nexs.putaway.constants.PutAwayAction;
import com.lenskart.nexs.putaway.constants.PutAwayType;
import com.lenskart.nexs.putaway.model.request.CreatePutAwayRequest;
import com.lenskart.nexs.putaway.model.request.CreatePutawayItemRequest;
import com.lenskart.nexs.putaway.model.request.PutawayCloseGrnRequest;
import com.lenskart.nexs.putaway.model.response.CreatePutawayItemResponse;
import com.lenskart.nexs.putaway.model.response.CreatePutawayResponse;
import com.lenskart.nexs.putaway.model.response.Putaway;
import com.lenskart.nexs.putaway.service.PutawayIntegrationService;
import com.lenskart.nexs.putaway.utils.PutawayUtils;
import com.lenskart.nexs.service.RedisHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lenskart.nexs.grn.constants.GRNConstants.QC_PASS;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

@Service
@EnableAsync
@Slf4j
public class PutawayService {
	
	@Autowired
    private GRNConfig grnConfig;
	
	@Autowired
    private PutAwayConfig putAwayConfig;

    @Autowired
    PutawayIntegrationService putawayIntegrationService;

    @Autowired
    PutawayMapper putawayMapper;

    private String PUTAWAY_STATUS="CREATED";


	public List<CreatePutawayResponse> createPutAway(GRNItemDTO grnItemDTO, ScanItemResponseDTO scanItemResponseDTO,
											   String inventoryType) {
		log.info("Creating putaway for barcode {}, inventoryType {}", grnItemDTO.getBarcode(), inventoryType);
		String putawayCacheKey = CacheUtils.getPutawayCacheKey(grnConfig.getKeyPrefix(), grnItemDTO);
		if(inventoryType != null)
			putawayCacheKey = CacheUtils.getPutawayCacheKeyInventoryType(grnConfig.getKeyPrefix(), grnItemDTO, inventoryType);
		log.info("Creating putaway for barcode {}, inventoryType {}, putawayCacheKey {}", grnItemDTO.getBarcode(), inventoryType, putawayCacheKey);
		List<CreatePutawayResponse> response = null;
		if (RedisHandler.hasKey(putawayCacheKey)) {
			log.info("Fetching putaway response for barcode {} | cacheKey {}", grnItemDTO.getBarcode(), putawayCacheKey);
			response = fetchPutawayResponseFromCache(grnItemDTO, putawayCacheKey, response);
		}
		CreatePutAwayRequest putawayRequest = buildCreatePutawayRequestModel(grnItemDTO, PutAwayAction.create);
		if (response == null) {
			response = createPutawayAndUpdateCache(grnItemDTO, putawayCacheKey, putawayRequest);
			setPutawayCodeInGrnItem(grnItemDTO, response);
			log.info("Putaway response fetched from putaway {}, for barcode {}: ", response, grnItemDTO.getBarcode());
		} else {
			log.info("Putaway response fetched from cache for barcode {}: " + response, grnItemDTO.getBarcode());
			RetryUtils.sendMessage(PutawayUtils.serialisePutawayRequest(putawayRequest),
					putAwayConfig.getGrnItemSyncPutawayTopicName(), CommonUtils.getKafkaMesageHeaders());
		}
		if (response != null && !response.isEmpty()) {
			updatePutawayInResponse(scanItemResponseDTO, response);
		} else {
			throw new ApplicationException("Error from putaway service for barcode " + grnItemDTO.getBarcode() + " : " 
					+ response, GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
		log.info("[createPutAway] barcode {}, request {}, putaway response {}", grnItemDTO.getBarcode(), putawayRequest,
				response);
		return response;
	}

	private void setPutawayCodeInGrnItem(GRNItemDTO grnItemDTO, List<CreatePutawayResponse> response) {
		if (response != null && !response.isEmpty()) {
			for (CreatePutawayResponse createPutawayResponse : response) {
				List<CreatePutawayItemResponse> barcodeInfoList = createPutawayResponse.getBarcodeInfo();
				for (CreatePutawayItemResponse barcodeInfo : barcodeInfoList) {
					if (StringUtils.equalsIgnoreCase(barcodeInfo.getBarcodeNumber(), grnItemDTO.getBarcode())
							&& StringUtils.isNotBlank(barcodeInfo.getNewPutawayId())) {
						log.info("[scanGRNItem] barcode {}, grnCode {}, putawayCode {}", grnItemDTO.getBarcode(),
								grnItemDTO.getGrnCode(), barcodeInfo.getNewPutawayId());
						grnItemDTO.setPutawayCode(Integer.parseInt(barcodeInfo.getNewPutawayId()));
					}
				}
			}
		}
	}

	private List<CreatePutawayResponse> fetchPutawayResponseFromCache(GRNItemDTO grnItemDTO, String putawayCacheKey,
			List<CreatePutawayResponse> response) {
		try {
			String putawayResponseFromCache = (String) RedisHandler.redisOps(RedisOps.GET, putawayCacheKey);
			log.error("[fetchPutawayResponseFromCache] Fetched putaway frpm cache for grn {} | barcode {} : {}", 
					grnItemDTO.getGrnCode(), grnItemDTO.getBarcode(), putawayResponseFromCache);
			response = PutawayUtils.deserialisePutawayResponse(putawayResponseFromCache);
		} catch (Exception e) {
			log.error("[fetchPutawayResponseFromCache] Unable to fetch putaway frpm cache for grn {} | barcode {} : {}", 
					grnItemDTO.getGrnCode(), grnItemDTO.getBarcode(), e.getMessage());
		}
		return response;
	}

	private void updatePutawayInResponse(ScanItemResponseDTO scanItemResponseDTO,
			List<CreatePutawayResponse> response) {
		for (CreatePutawayResponse createPutawayResponse : response) {
			if (createPutawayResponse.getBarcodeInfo() != null && !createPutawayResponse.getBarcodeInfo().isEmpty()) {
				CreatePutawayItemResponse barcodeInfo = createPutawayResponse.getBarcodeInfo().get(0);
				scanItemResponseDTO.setNewPutaway(new Putaway(barcodeInfo.getNewPutawayId(),
						createPutawayResponse.getLocationId(), PUTAWAY_STATUS, 1));
				scanItemResponseDTO.setOldPutaway(new Putaway(barcodeInfo.getOldPutawayId()));
			}
		}
	}

	private List<CreatePutawayResponse> createPutawayAndUpdateCache(GRNItemDTO grnItemDTO, String putawayCacheKey,
			CreatePutAwayRequest putawayRequest) {
		log.info("[createPutAway] request for putaway service for barcode {}: " + putawayRequest, grnItemDTO.getBarcode());
		List<CreatePutawayResponse> response = putawayIntegrationService.addBarcodeToPutaway(putawayRequest);
		log.info("[createPutAway] response from putaway service for barcode {}: " + response, grnItemDTO.getBarcode());
		try {
			if (response != null && !response.isEmpty() && response.get(0).getSuccessful() != null
					&& response.get(0).getSuccessful().equals(true)) {
				response = response.stream().filter(CreatePutawayResponse::getSuccessful).collect(Collectors.toList());
				log.info("Caching putaway response for barcode {} | cacheKey {} : " + response, grnItemDTO.getBarcode(),
						putawayCacheKey);
				RedisHandler.redisOps(RedisOps.SETVALUETTL, putawayCacheKey, response,
						grnConfig.getPutawayResponseTTL(), TimeUnit.MILLISECONDS);
			}
		} catch (Exception e) {
			log.error("Unable to cache putaway for grn {} | barcode {} : {}", 
					grnItemDTO.getGrnCode(), grnItemDTO.getBarcode(), e.getMessage());
		}
		return response;
	}

	public static String getPutawayCacheKey(String grnPrefix, GRNItemDTO grnItemDTO) {
		return grnPrefix + grnItemDTO.getUpdatedBy() + grnItemDTO.getGrnCode() + grnItemDTO.getPid();
	}

	public boolean dicardItemInPutaway(GrnItemEntity grnItem) throws Exception {
		GRNItemDTO itemDTO = putawayMapper.mapToGrnItemEntity(grnItem);
		itemDTO.setCreatedBy(MDC.get("USER_ID"));
		List<CreatePutawayResponse> response = putawayIntegrationService
				.addBarcodeToPutaway(buildCreatePutawayRequestModel(itemDTO, PutAwayAction.discard));
		log.info("[dicardItemInPutaway] response from putaway service " + response);
		if (response != null && !response.isEmpty()) {
			return true;
		} else {
			throw new ResponseStatusException(INTERNAL_SERVER_ERROR,
					"Error from putaway service: " + response);
		}
	}

    @Async
    public void closeGRN(String grnNumber) {
    	try {
    		PutawayCloseGrnRequest putawayCloseGrnRequest = new PutawayCloseGrnRequest();
    		putawayCloseGrnRequest.setPutawayReferenceId(grnNumber);
    		putawayCloseGrnRequest.setType(PutAwayType.PUTAWAY_GRN_ITEM.name());
    		putawayIntegrationService.closeGRN(putawayCloseGrnRequest);
    		log.info("Putaway executed successfully for grn {}", grnNumber);
    	} catch (Exception e){
    		log.error("Exception caught while closing putaway for grn {} : {}", grnNumber, e);
    		throw new ApplicationException("Unable to move putaway to pending: " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
    	}
    }

    @Async
	public void markPutawayPending(String grnCode, List<String> putawayCodes) {
		try {
			log.info("[markPutawayPending] Request recieved for grn {} : {}", grnCode, putawayCodes);
			List<String> putawayIds = putawayIntegrationService.markPutawayPending(putawayCodes);
			log.info("[markPutawayPending] Putaway pending successful for grn {} : {}", grnCode, putawayIds);
		} catch (Exception e) {
			log.error("[markPutawayPending] Exception caught while marking putaway pending for grn {} : {}", grnCode,
					putawayCodes, e);
			throw new ApplicationException("Unable to move putaway to pending: " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	public boolean updateItemDetailsInPutaway(UpdateGRNItemDTO updateGRNItemDTO) throws Exception {
		GRNItemDTO itemDTO = putawayMapper.mapToGrnItem(updateGRNItemDTO);
		itemDTO.setCreatedBy(MDC.get("USER_ID"));
		List<CreatePutawayResponse> response = putawayIntegrationService
				.addBarcodeToPutaway(buildCreatePutawayRequestModel(itemDTO, PutAwayAction.update));
		log.info("[updatetemDetailsInPutaway] response from putaway service " + response);
		if (response != null && !response.isEmpty()) {
			for (CreatePutawayResponse createPutawayResponse : response) {
				if (createPutawayResponse.getBarcodeInfo() != null && !createPutawayResponse.getBarcodeInfo().isEmpty()) {
					if (updateGRNItemDTO.getMeta() != null) {
						updateGRNItemDTO.getMeta()
								.setNewPutaway(new Putaway(createPutawayResponse.getBarcodeInfo().get(0).getNewPutawayId(),
										createPutawayResponse.getLocationId(), PUTAWAY_STATUS, 1));
						updateGRNItemDTO.getMeta()
								.setOldPutaway(new Putaway(createPutawayResponse.getBarcodeInfo().get(0).getOldPutawayId()));
					}
				} 
			}
		} else {
			throw new ResponseStatusException(INTERNAL_SERVER_ERROR,
					"Error from putaway service: " + response);
		}
		return true;
	}

     private CreatePutAwayRequest buildCreatePutawayRequestModel(GRNItemDTO grnItemDTO, PutAwayAction action){
		 log.info("[buildCreatePutawayRequestModel] grn code {}, barcode {}, action {}",
				 grnItemDTO.getGrnCode(), grnItemDTO.getBarcode(), action);
		 CreatePutAwayRequest request =new CreatePutAwayRequest();
         request.setGrnNumber(grnItemDTO.getGrnCode());
         request.setUser(grnItemDTO.getCreatedBy());
         request.setType(PutAwayType.PUTAWAY_GRN_ITEM);
         request.setFacilityCode(grnItemDTO.getFacilityCode());
         request.setBarcodes(new ArrayList<>());
         CreatePutawayItemRequest items =new CreatePutawayItemRequest();
         items.setAction(action);
         items.setCondition(grnItemDTO.getQcStatus());
         if(grnItemDTO.getQcStatus().equalsIgnoreCase(QC_PASS))
              items.setBoxBarcode(grnItemDTO.getQcPassBoxBarcode());
         else
             items.setBoxBarcode(grnItemDTO.getQcFailBoxBarcode());
         items.setBarcodeNumber(grnItemDTO.getBarcode());
         items.setPid(grnItemDTO.getPid());
         request.getBarcodes().add(items);
         Map<CustomReference,Object> reference =new HashMap<>();
         if(grnItemDTO.getUnicomGrnCode()!=null && !grnItemDTO.getUnicomGrnCode().isEmpty())
            reference.put(CustomReference.UNICOM_GRN_NUMBER,grnItemDTO.getUnicomGrnCode());

         if(grnItemDTO.getPoID()!=null && !grnItemDTO.getPoID().isEmpty())
           reference.put(CustomReference.PO_NUMBER,grnItemDTO.getPoID());
         request.setReferences(reference);
         log.info("[buildCreatePutawayRequestModel] request {}", request);
         return request;
     }

	public List<CreatePutawayResponse> createGrnItemPutaway(String grnCode, String unicomGrnNum, List<GrnItemEntity> grnItem) {
		try {
			log.info("[createGrnItemPutaway] grncode {}", grnCode);
			CreatePutAwayRequest putawayRequest = buildGrnItemCreatePutawayRequestModel(grnItem, unicomGrnNum, PutAwayAction.create);
			List<CreatePutawayResponse> response = putawayIntegrationService.addBarcodeToPutaway(putawayRequest);
			log.info("[createGrnItemPutaway] grncode {}, response {}", grnCode, response);
			return response;
		} catch (Exception e) {
			log.error("[createGrnItemPutaway] grncode {}: error {}", grnCode, e.getMessage());
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	public void pushGrnItemsToKafkaToCreatePutaway(String grnCode, String unicomGrnNum, GrnItemEntity missingPutawayGrnItem) {
		try {
			log.info("[pushGrnItemsToKafkaToCreatePutaway] grncode {}, barcode {}", grnCode, missingPutawayGrnItem.getBarcode());
			List<GrnItemEntity> missingPutawayGrnItemList = new ArrayList<>();
			missingPutawayGrnItemList.add(missingPutawayGrnItem);
			CreatePutAwayRequest putawayRequest = buildGrnItemCreatePutawayRequestModel(missingPutawayGrnItemList,
					unicomGrnNum, PutAwayAction.create);
			RetryUtils.sendMessage(PutawayUtils.serialisePutawayRequest(putawayRequest),
					putAwayConfig.getGrnItemSyncPutawayTopicName(), CommonUtils.getKafkaMesageHeaders());
			log.info("[pushGrnItemsToKafkaToCreatePutaway] All grn items successfully pushed in kafka for grncode {}",
					grnCode);
		} catch (Exception e) {
			log.error("[pushGrnItemsToKafkaToCreatePutaway] grncode {}: error {}", grnCode, e.getMessage());
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	public CreatePutAwayRequest buildGrnItemCreatePutawayRequestModel(List<GrnItemEntity> grnItemEntityList,
																String unicomGrnNum, PutAwayAction action) {
		if (grnItemEntityList == null)
			throw new ApplicationException("Grn item list can not be empty", GRNExceptionStatus.GRN_BAD_REQUEST);
		log.info("[buildGrnItemCreatePutawayRequestModel] grnCode {}, action {}",
				grnItemEntityList.get(0).getGrnCode(), action);

		CreatePutAwayRequest request = new CreatePutAwayRequest();
		request.setGrnNumber(grnItemEntityList.get(0).getGrnCode());
		request.setUser(grnItemEntityList.get(0).getCreatedBy());
		request.setType(PutAwayType.PUTAWAY_GRN_ITEM);
		request.setFacilityCode(grnItemEntityList.get(0).getFacility());

		List<CreatePutawayItemRequest> barcodes = new ArrayList<>();
		for (GrnItemEntity entity : grnItemEntityList) {
			CreatePutawayItemRequest items = new CreatePutawayItemRequest();
			items.setAction(action);
			items.setCondition(StringUtils.isNotBlank(entity.getQcStatus()) ? entity.getQcStatus() : "pass");
			items.setBoxBarcode(entity.getQcPassBoxBarcode());
			items.setBarcodeNumber(entity.getBarcode());
			items.setPid(entity.getPid());
			barcodes.add(items);
		}
		request.setBarcodes(barcodes);

		Map<CustomReference, Object> reference = new HashMap<>();
		if (unicomGrnNum != null && !unicomGrnNum.isEmpty())
			reference.put(CustomReference.UNICOM_GRN_NUMBER, unicomGrnNum);

		if (grnItemEntityList.get(0).getPoId() != null && !grnItemEntityList.get(0).getPoId().isEmpty())
			reference.put(CustomReference.PO_NUMBER, grnItemEntityList.get(0).getPoId());

		request.setReferences(reference);

		log.info("[buildGrnItemCreatePutawayRequestModel] request {}", request);
		return request;
	}

	public CreatePutAwayRequest buildItemCreatePutawayRequestModel(List<GRNItem> grnItemEntityList,
																	  String unicomGrnNum, PutAwayAction action, String user) {
		if (grnItemEntityList == null)
			throw new ApplicationException("Grn item list can not be empty", GRNExceptionStatus.GRN_BAD_REQUEST);
		log.info("[buildItemCreatePutawayRequestModel] grnCode {}, action {}",
				grnItemEntityList.get(0).getGrnCode(), action);

		CreatePutAwayRequest request = new CreatePutAwayRequest();
		request.setGrnNumber(grnItemEntityList.get(0).getGrnCode());
		request.setUser(user);
		request.setType(PutAwayType.PUTAWAY_GRN_ITEM);
		request.setFacilityCode(grnItemEntityList.get(0).getFacility());

		List<CreatePutawayItemRequest> barcodes = new ArrayList<>();
		for (GRNItem entity : grnItemEntityList) {
			CreatePutawayItemRequest items = new CreatePutawayItemRequest();
			items.setAction(action);
			items.setCondition(StringUtils.isNotBlank(entity.getQcStatus()) ? entity.getQcStatus() : "pass");
			items.setBoxBarcode(entity.getQcPassBoxBarcode());
			items.setBarcodeNumber(entity.getBarcode());
			items.setPid(entity.getPid());
			barcodes.add(items);
		}
		request.setBarcodes(barcodes);

		Map<CustomReference, Object> reference = new HashMap<>();
		if (unicomGrnNum != null && !unicomGrnNum.isEmpty())
			reference.put(CustomReference.UNICOM_GRN_NUMBER, unicomGrnNum);

		if (grnItemEntityList.get(0).getPoId() != null && !grnItemEntityList.get(0).getPoId().isEmpty())
			reference.put(CustomReference.PO_NUMBER, grnItemEntityList.get(0).getPoId());

		request.setReferences(reference);

		log.info("[buildItemCreatePutawayRequestModel] request {}", request);
		return request;
	}

	public List<CreatePutawayResponse> createPutaway(String barcode, CreatePutAwayRequest putawayRequest) {
		try {
			log.info("[createPutAway] request for putaway service for barcode {}, {}: ", barcode, putawayRequest);
			List<CreatePutawayResponse> response = putawayIntegrationService.addBarcodeToPutaway(putawayRequest);
			log.info("[createPutAway] response from putaway service for barcode {} : response {} ", barcode, response);
			return response;
		} catch (Exception e) {
			log.error("[createPutAway] Unable to create putaway for grn barcode {} : {}", barcode, e.getMessage());
			throw new ApplicationException("Unable to create putaway for grn " + barcode + ", Error: " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
}
