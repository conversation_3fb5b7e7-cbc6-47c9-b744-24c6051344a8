package com.lenskart.nexs.grn.putaway.mapper;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.model.GRNItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
public interface PutawayMapper {

    @Mappings({
            @Mapping(target = "qcPassBoxBarcode", source = "boxBarcode"),
            @Mapping(target = "qcFailBoxBarcode", source = "boxBarcode"),

    })
    GRNItemDTO mapToGrnItem(UpdateGRNItemDTO grnItemDTO) throws Exception;
    
    GRNItemDTO mapToGrnItemEntity(UpdateGRNItemDTO grnItemDTO) throws Exception;

    @Mapping(target = "facilityCode", source = "facility")
    GRNItemDTO mapToGrnItem(GRNItem grnItemDTO) throws Exception;
    
    @Mapping(target = "facilityCode", source = "facility")
    GRNItemDTO mapToGrnItemEntity(GrnItemEntity grnItemDTO) throws Exception;

}
