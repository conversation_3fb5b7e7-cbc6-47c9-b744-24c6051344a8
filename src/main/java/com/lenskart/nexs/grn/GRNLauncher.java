package com.lenskart.nexs.grn;

import com.lenskart.nexs.fms.model.repo.LayoutStoreRepository;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@SpringBootApplication(scanBasePackages = { "com.lenskart.*"})
@EntityScan(basePackages = { "com.lenskart.*"})
@ComponentScan(basePackages = { "com.lenskart.*" })
@EnableJpaRepositories(basePackages = {"com.lenskart.nexs", "com.lenskart.platform.fl.utils.jpa.repository"},
		excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = LayoutStoreRepository.class))
@EnableScheduling
@EnableJpaAuditing
@EnableSchedulerLock(defaultLockAtMostFor = "PT30S")
public class GRNLauncher {

	public static void main(String[] args) {
		SpringApplication.run(GRNLauncher.class, args);
	}

	@Bean("threadPoolTaskExecutor")
	public TaskExecutor getAsyncExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(20);
		executor.setMaxPoolSize(1000);
		executor.setWaitForTasksToCompleteOnShutdown(true);
		executor.setThreadNamePrefix("Async-");
		return executor;
	}

}