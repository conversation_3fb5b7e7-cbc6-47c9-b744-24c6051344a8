package com.lenskart.nexs.grn.scheduler;

import com.lenskart.nexs.common.entity.entityService.grn.IqcGrnProductEntityService;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.model.GrnCodeAndStatusSchedulerResponse;
import com.lenskart.nexs.grn.service.IQCItemService;
import com.nexs.po.common.enums.IqcGrnProductStatusEnum;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class IQCBoxBarcodeGrnCompleteValidateScheduler {

    @CustomLogger
    private Logger log;

    @Autowired
    IQCItemService iqcItemService;

    @Autowired
    IqcGrnProductEntityService iqcGrnProductEntityService;

    @Value("${grn.complete.iqc.scheduler.lessthan.maxretryCount:10}")
    private int maxRetryCount;

    @Value("${grn.complete.iqc.scheduler.sync.ims.before.min:2}")
    private int imsSyncBefore;

    @Value("${grn.complete.iqc.scheduler.created.days.after:7}")
    private int createdDaysAfter;

    @Scheduled(cron = "* */2 * * * ?")
    @SchedulerLock(name = "validateIQCBoxBarcodeGrnCompleteSchedulerTask")
    public void validateIQCBoxBarcodeGrnCompleteScheduler() {
        log.info("Validate IQC Box Barcode Grn Complete Scheduler, started {}", new Date());
        validateIQCCompleteStatusScheduler();
    }

    private void validateIQCCompleteStatusScheduler() {
        try {
            log.info("[validateIQCCompleteStatusScheduler] Validate iqc status {}", new Date());
            List<Tuple> iqcGrnProductEntities = iqcGrnProductEntityService.findDistinctGrnCodeForStatus(
                    IqcGrnProductStatusEnum.IQC_DONE.getStatus(), maxRetryCount,
                    imsSyncBefore, createdDaysAfter);
            log.info("[validateIQCCompleteStatusScheduler] Validate iqc status iqcGrnProductEntities size {}",
                    iqcGrnProductEntities.size());
            for (Tuple tuple : iqcGrnProductEntities) {
                String grnCode = tuple.get("grn_code", String.class);
                String boxCode= tuple.get("box_code", String.class);
                String pid = tuple.get("pid", String.class);
                try {
                    log.info("[validateIQCCompleteStatusScheduler] Validate iqc status grnCode {}, boxCode {}, pid {}",
                            grnCode, boxCode, pid);
                    String message = iqcItemService.markGrnIqcComplete(grnCode, boxCode, pid);
                    log.info("[validateIQCCompleteStatusScheduler] Validate iqc status grnCode {}, boxCode {}, message {}",
                            grnCode, boxCode, message);
                } catch (Exception e) {
                    log.error("Error for grnCode {}, message {}",
                            grnCode, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("[validateIQCCompleteStatusScheduler] error {}", e.getMessage());
        }
    }

}
