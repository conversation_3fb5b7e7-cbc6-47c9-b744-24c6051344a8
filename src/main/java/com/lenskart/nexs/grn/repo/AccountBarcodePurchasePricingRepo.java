package com.lenskart.nexs.grn.repo;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.db.SlaveDbConfig;
import com.lenskart.nexs.grn.entity.AccountBarcodePurchasePricing;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.lenskart.nexs.grn.db.Queries.ACCOUNT_BARCODE_PURCHASE_PRICING;

@Service
public class AccountBarcodePurchasePricingRepo {

    @CustomLogger
    private Logger log;

    @Autowired
    private SlaveDbConfig slaveDbConfig;

    @Logging
    public List<AccountBarcodePurchasePricing> getBarcodePriceFromAccountBarcodePurchasePricingEntity(
            List<String> barcodeList, String facilityLegalOwner) throws ApplicationException {
        log.info("[getBarcodePriceFromAccountBarcodePurchasePricingEntity] barcodeList {}, facilityLegalOwner {}", barcodeList, facilityLegalOwner);
        String query = ACCOUNT_BARCODE_PURCHASE_PRICING;
        List<AccountBarcodePurchasePricing> barcodeDetails = new ArrayList<>();

        // Dynamically build placeholders for the IN clause
        String placeholders = String.join(",", Collections.nCopies(barcodeList.size(), "?"));
        query = String.format(query, placeholders);
        log.info("[getBarcodePriceFromAccountBarcodePurchasePricingEntity] query {}", query);

        try (Connection con = slaveDbConfig.getDataSourceSlave().getConnection();
             PreparedStatement pst = con.prepareStatement(query)) {

            // Set parameters for the barcode list
            for (int i = 0; i < barcodeList.size(); i++) {
                pst.setString(i + 1, barcodeList.get(i));
            }
            pst.setString(barcodeList.size() + 1, facilityLegalOwner);
            log.info("[getBarcodePriceFromAccountBarcodePurchasePricingEntity] barcodeList {}, facilityLegalOwner {}", barcodeList, facilityLegalOwner);

            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next()) {
                    AccountBarcodePurchasePricing barcodeInfo = new AccountBarcodePurchasePricing(
                            rs.getString("barcode"),
                            rs.getString("vendor_name"),
                            rs.getString("legal_owner"),
                            rs.getString("nexs_grn_code"),
                            rs.getString("unicom_grn_code"),
                            rs.getBigDecimal("unit_price_without_tax"),
                            rs.getString("currency"),
                            rs.getInt("product_id"),
                            rs.getString("invoice_ref_num"),
                            rs.getString("grn_facility"),
                            rs.getString("po_id"));
                    barcodeDetails.add(barcodeInfo);
                }
                log.info("[getBarcodePriceFromAccountBarcodePurchasePricingEntity] barcodeList {}, facilityLegalOwner {}, barcodeDetails {}",
                        barcodeList, facilityLegalOwner, barcodeDetails);
                return barcodeDetails;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception: " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception: " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
