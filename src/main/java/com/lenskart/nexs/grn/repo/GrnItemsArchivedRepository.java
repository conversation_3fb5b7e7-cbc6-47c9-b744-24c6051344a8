package com.lenskart.nexs.grn.repo;

import com.lenskart.nexs.common.base.EntitiesRepository;
import com.lenskart.nexs.grn.entity.GrnItemsArchivedEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.persistence.Tuple;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface GrnItemsArchivedRepository extends EntitiesRepository<GrnItemsArchivedEntity, BigInteger> {

    GrnItemsArchivedEntity findByBarcodeAndGrnCode(String barcode, String grnCode);

    @Query(
            value = "SELECT  gi.barcode, gi.pid, (poi.final_price * po.currency_conv_rate / poi.quantity) as price_with_tax, "
                    + "((poi.final_price - cgst_rate - igst_rate - ugst_rate - sgst_rate - flat_rate) * po.currency_conv_rate / poi.quantity) as cost_price "
                    + "FROM nexs.grn_items_old12feb2024 gi JOIN nexs.purchase_order_item poi ON gi.po_id = poi.po_num AND gi.pid = poi.product_id JOIN nexs.purchase_order po "
                    + "ON gi.po_id = po.po_num WHERE barcode in :barcodeList and facility = :facilityCode",
            nativeQuery = true
    )
    List<Tuple> findBarcodeListItemPriceByFacility(List<String> barcodeList, String facilityCode);

    @Query(value = "select * from grn_items_old12feb2024 where barcode in :barcodeList", nativeQuery = true)
    List<GrnItemsArchivedEntity> findByBarcodeIn(List<String> barcodeList);

    @Query(
            value = "select * from grn_items_old12feb2024 where barcode=:barcode order by created_at DESC limit 1;",
            nativeQuery = true
    )
    GrnItemsArchivedEntity findTopGrnItemByBarcode(String barcode);

    @Query(
            value = "select * from grn_items_old12feb2024 where barcode=:barcode and facility =:facilityCode order by created_at DESC limit 1;",
            nativeQuery = true
    )
    GrnItemsArchivedEntity getTopGrnItemDetailForBarcodeAndFacility(String barcode, String facilityCode);
}
