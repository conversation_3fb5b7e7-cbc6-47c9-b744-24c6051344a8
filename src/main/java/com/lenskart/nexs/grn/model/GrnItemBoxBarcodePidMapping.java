package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GrnItemBoxBarcodePidMapping implements Serializable {

    @JsonProperty(value = "grn_code",required = true)
    private String grnCode;

    @JsonProperty(value ="pid" ,required = true)
    private Integer pid;

    @JsonProperty("qc_pass_box_barcode")
    private String qcPassBoxBarcode;

    @JsonProperty("invoice_ref_num")
    private String invoiceRefNum;

    @JsonProperty("total_pid_count")
    private Integer totalPidCount;
}
