package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Valid
@JsonIgnoreProperties(ignoreUnknown = true)
public class POProduct {

    @NotNull(message = "product_id must not be null")
    @NotBlank(message = "product_id must not be blank")
    @JsonProperty(value = "product_id", required = true)
    private String pid;

//    @Min(value = 1, message = "quantity is mandatory field, must be > 0")
    @JsonProperty(required = true)
    private long quantity;

    @JsonProperty(value = "vendor_unit_cost_price", required = true)
    private double price;

    @DecimalMin(value = "0.0", message = "total_vendor_cost_price is mandatory field")
    @JsonProperty(value = "total_vendor_cost_price", required = true)
    private double totalVendorCostPrice;

    @DecimalMin(value = "0.0", message = "cgst_rate is mandatory field")
    @JsonProperty(value = "cgst_rate", required = true)
    private double cgstRate;

    @DecimalMin(value = "0.0", message = "sgst_rate is mandatory")
    @JsonProperty(value = "sgst_rate", required = true)
    private double sgstRate;

    @DecimalMin(value = "0.0", message = "igst_rate is mandatory field")
    @JsonProperty(value = "igst_rate", required = true)
    private double igstRate;

//    @DecimalMin(value = "1.0", message = "price_with_taxes is mandatory field")
    @JsonProperty("price_with_taxes")
    private double priceWithTaxes;

    @JsonProperty(value = "classification", required = true)
    private Integer categoryId;
}
