package com.lenskart.nexs.grn.model;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class CreateGrnMasterRequest {
    @JsonProperty(value = "vendor_invoice_number")
    private String vendorInvoiceNumber;

    @JsonProperty(value = "facility_code")
    private String facilityCode;

    @JsonProperty(value = "created_by")
    private String createdBy;

    @JsonProperty(value = "type")
    private String type;
}
