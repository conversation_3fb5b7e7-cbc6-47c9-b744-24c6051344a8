package com.lenskart.nexs.grn.model;

import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class GrnResponse extends UniCommerceResponse {

    private GrnInfoResponse inflowReceipt;
    private String vendorCode;

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GrnInfoResponse {
        private String code;
        private String statusCode;
        private Date created;
        private String createdBy;
        private String vendorInvoiceNumber;
        private String vendorInvoiceDate;
        private Map<String, Object> purchaseOrder;
        private int totalReceivedAmount;
        private int totalRejectedAmount;
        private int totalQuantity;
        private int totalRejectedQuantity;
        private List<GrnItemResponse> inflowReceiptItems;
        private Object customFieldValues;
    }

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GrnItemResponse {
        private int id;
        private int purchaseOrderItemId;
        private String itemSKU;
        private String itemTypeName;
        private String itemTypeImageUrl;
        private String itemTypePageUrl;
        private String vendorSkuCode;
        private int quantity;
        private boolean itemsLabelled;
        private int pendingQuantity;
        private int rejectedQuantity;
        private int detailedQuantity;
        private String rejectionComments;
        private Date expiry;
        private String status;
        private Double unitPrice;
        private Double maxRetailPrice;
        private Double additionalCost;
        private String batchCode;
        private Double discount;
        private Double discountPercentage;
    }

}
