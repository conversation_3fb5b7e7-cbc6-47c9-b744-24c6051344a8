package com.lenskart.nexs.grn.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNPIDMaster {

	@JsonProperty("grn_code")
    private String grnCode;

    private String pid;

    @JsonProperty("invoice_id")
    private String invoiceId;

    @JsonProperty("invoice_ref_num")
    private String invoiceReferenceNum;
    
    private String vendor;
    
    private String brand;

    @JsonProperty("category_id")
    private Integer categoryId;

    @JsonProperty("pid_description")
    private String pidDescription;
    
    @JsonProperty("price")
    private double price;

    @JsonProperty("tax_rate")
    private double taxRate;

    @JsonProperty("cgst_rate")
    private double cgstRate;

    @JsonProperty("sgst_rate")
    private double sgstRate;

    @JsonProperty("igst_rate")
    private double igstRate;

    @JsonProperty("estimated_total_quantity")
    private Long estimatedQuantity;

    @JsonProperty("estimated_quantity_list")
    private List<Long> estimatedQuantityList;

    @JsonProperty("manual_override")
    private short manualOverride;

    @JsonProperty("grn_pid_status")
    private String pidStatus;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    public GRNPIDMaster(String grnCode, String pid, String invoiceReferenceNum) {
        this.grnCode = grnCode;
        this.pid = pid;
        this.invoiceReferenceNum = invoiceReferenceNum;
    }

    public GRNPIDMaster(String grnCode, String pid, String invoiceReferenceNum, String pidStatus, short manualOverride) {
        this.grnCode = grnCode;
        this.pid = pid;
        this.invoiceReferenceNum = invoiceReferenceNum;
        this.pidStatus = pidStatus;
        this.manualOverride = manualOverride;
    }
}
