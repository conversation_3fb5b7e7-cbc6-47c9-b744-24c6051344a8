package com.lenskart.nexs.grn.model;

import lombok.*;

import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class UniCommerceResponse {
    private boolean successful;
    private String message;
    private List<UniCommerceResponse.UniCommerceError> errors;
    private String warnings;

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UniCommerceError {
        private int code;
        private String fieldName;
        private String description;
        private String message;
        private String errorParams;
    }
    
}
