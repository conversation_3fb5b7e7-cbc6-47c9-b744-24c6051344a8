package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class QCMaster {

    @JsonProperty("sampling_id")
    private int samplingId;

    @JsonProperty("category_id")
    private Integer categoryId;

    private String brand;

    private String vendor;

    @JsonProperty("product_id")
    private String productId;

    @JsonProperty("sampling_quantity")
    private int samplingQuantity;

//    @JsonProperty("sampling_percent")
//    private int samplingPercent;

    @JsonProperty("failure_percent")
    private int failurePercent;

    public QCMaster(){

    }

}
