package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.sql.Timestamp;
import java.util.Date;

@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class GRNScanItemRequest {
    @JsonProperty(value = "vendor_invoice_number", required = true)
    @NotBlank(message = "Vendor invoice number must not be blank")
    private String vendorInvoiceNumber;

    @JsonProperty(value = "barcode", required = true)
    @NotBlank(message = "Barcode must not be blank")
    private String barcode;

    @JsonProperty(value = "grn_code", required = true)
    @NotBlank(message = "Grn Code must not be blank")
    private String grnCode;

    @JsonProperty(value = "product_id", required = true)
    @NotBlank(message = "Product Id must not be blank")
    private String productId;

    @JsonProperty(value = "fitting_id", required = true)
    @NotBlank(message = "Fitting must not be blank")
    private String fittingId;

    @JsonProperty(value = "uw_tem_id", required = true)
    @NotBlank(message = "UW item id must not be blank")
    private String uwItemId;

    @JsonProperty(value = "facility_code")
    private String facilityCode;

    @JsonProperty(value = "expiry_date")
    private Date expiryDate;

    @JsonProperty(value = "qc_status")
    private String qcStatus;

    @JsonProperty(value = "qc_fail_code")
    private String qcFailCode;

    @JsonProperty(value = "qc_reason")
    private String qcReason;

    @JsonProperty(value = "created_by")
    private String createdBy;

    @JsonProperty(value = "ems_sync")
    private boolean emsSync = true;

    @JsonProperty(value = "vendor_code", required = true)
    @NotBlank(message = "Vendor Code must not be blank")
    private String vendorCode;

    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "pid_swap_allow")
    private boolean pidSwapAllow = true;

    @JsonProperty(value = "barcodeUrl")
    private String barcodeUrl;
}
