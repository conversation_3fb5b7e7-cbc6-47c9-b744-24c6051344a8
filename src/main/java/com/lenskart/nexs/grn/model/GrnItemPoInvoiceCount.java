package com.lenskart.nexs.grn.model;

import lombok.Data;

@Data
public class GrnItemPoInvoiceCount {

    private String grnCode;

    private String pid;

    private Integer scannedQty;

    private Integer poQty;

    private Integer invoiceQty;

    private Double poUnitPriceWithTax;

    public GrnItemPoInvoiceCount(String grnCode, String pid, Integer scannedQty,
                                 Integer poQty, Integer invoiceQty) {
        this.grnCode = grnCode;
        this.pid = pid;
        this.scannedQty = scannedQty;
        this.poQty = poQty;
        this.invoiceQty = invoiceQty;
    }

    public GrnItemPoInvoiceCount(String grnCode, String pid, Integer scannedQty) {
        this.grnCode = grnCode;
        this.pid = pid;
        this.scannedQty = scannedQty;
    }
}
