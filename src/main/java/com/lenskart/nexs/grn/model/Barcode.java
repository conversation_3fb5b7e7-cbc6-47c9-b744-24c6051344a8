package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class Barcode {

    @JsonProperty("start")
    private String fromSeries;

    @JsonProperty("end")
    private String toSeries;

    @JsonIgnore
    private int isBox;

    private String type;

    public Barcode(String fromSeries, String toSeries) {
        this.fromSeries = fromSeries;
        this.toSeries = toSeries;
    }

    public Barcode(String fromSeries, String toSeries, int isBox) {
        this.fromSeries = fromSeries;
        this.toSeries = toSeries;
        this.isBox = isBox;
    }
}
