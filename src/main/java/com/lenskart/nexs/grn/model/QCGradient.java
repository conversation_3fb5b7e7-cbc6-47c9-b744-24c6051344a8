package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class QCGradient {

    @JsonProperty("gradient_id")
    private int gradientId;

    private int order;

    @JsonProperty("category_id")
    private Integer categoryId;

//    @JsonProperty("sampling_percent")
//    private int samplingPercent;

    @JsonProperty("failure_percent")
    private int failurePercent;
    
    private String method;

    public QCGradient() {

    }
}
