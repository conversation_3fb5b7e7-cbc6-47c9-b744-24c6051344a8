package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexs.po.common.enums.GrnImsSyncStatusEnum;
import com.nexs.po.common.enums.GrnPutawaySyncStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Timestamp;

@Data
public class GRNItem implements Serializable {

	private BigInteger id;
   
    private String barcode;
    
    @JsonProperty(value = "grn_code",required = true)
    private String grnCode;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty(value ="pid" ,required = true)
    private String pid;
    
    @JsonProperty(value ="invoice_id",required = true)
    private String invoiceId;
    
    @JsonProperty(value ="invoice_ref_num",required = true)
    private String invoiceRefNum;
    
    @JsonProperty(value ="po_id",required = true)
    private String poId;
    
    @JsonProperty(value ="vendor_code",required = true)
    private String vendorCode;
    
    @JsonProperty("expiry_date")
    private Timestamp expiryDate;
    
    @JsonProperty("lot_no")
    private String lotNo;
    
    @JsonProperty("qc_pass_box_barcode")
    private String qcPassBoxBarcode;
    
    @JsonProperty("qc_fail_box_barcode")
    private String qcFailBoxBarcode;
    
    @JsonProperty("qc_status")
    private String qcStatus;
    
    @JsonProperty(value = "qc_fail_code", required = false)
    private String qcFailCode = "NA";
    
    @JsonProperty("qc_reason")
    private String qcReason = "NA";
    
    @JsonProperty("invoice_quantity")
    private long invoiceQuantity;

    @JsonProperty("po_quantity")
    private long poQuantity;

    @JsonProperty("grn_estimated_quantity")
    private long grnEstimatedQuantity;

    private String brand;
    
    private Integer categoryId;

    private int qcMasterSampling;
    
    private int qcGradientSampling;
    
    private int failureThresholdQcMaster;
    
    private int failureThresholdQcGradient;
    
    @JsonProperty("created_at")
    private Timestamp createdAt;
    @JsonProperty("updated_at")
    private Timestamp updatedAt;
    @JsonProperty("created_by")
    private String createdBy;
    @JsonProperty("updated_by")
    private String updatedBy;

    private String facility;

    private int channelStatus;

    private int gradientShift;

    @JsonProperty("putaway_code")
    private Integer putawayCode;

    @JsonProperty("legal_owner")
    private String legalOwner;

    private GrnPutawaySyncStatusEnum putawaySyncStatus;

    private GrnImsSyncStatusEnum imsSyncStatus;

    public GRNItem() {

    }
    
    public GRNItem(String qcStatus) {
        this.qcStatus = qcStatus;
    }

    public GRNItem(GRNItem grnItem){
        this.categoryId = grnItem.getCategoryId();
        this.brand = grnItem.getBrand();
        this.vendorCode = grnItem.getVendorCode();
        this.pid = grnItem.getPid();
        this.invoiceQuantity = grnItem.getInvoiceQuantity();
//        this.grnEstimatedQuantity = grnItem.getGrnEstimatedQuantity();
    }
}
