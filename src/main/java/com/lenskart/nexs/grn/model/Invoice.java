package com.lenskart.nexs.grn.model;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@Valid
@JsonIgnoreProperties(ignoreUnknown = true)
public class Invoice {

    @NotNull(message = "invoice_ref_number must not be null")
    @NotBlank(message = "invoice_ref_number must not be blank")
    @JsonProperty(value = "invoice_ref_number", required = true)
    private String invoiceRefNum;

    @NotNull(message = "vendor_invoice_number must not be null")
    @NotBlank(message = "vendor_invoice_number must not be blank")
    @JsonProperty(value = "vendor_invoice_number", required = true)
    private String invoiceId;
    
    @NotNull(message = "po_num must not be null")
    @NotBlank(message = "po_num must not be blank")
    @JsonProperty(value = "po_num", required = true)
    private String poId;

    @JsonProperty(value = "approved_at", required = true)
    @Min(value = 1, message = "approved_at is mandatory field")
    private long approvedAt;

    private String poDate;

    @JsonProperty(value = "invoice_date", required = true)
    @Min(value = 1, message = "invoice_date is mandatory field")
    private long invoice_date;

    private String invoiceDate;

    @JsonProperty(value = "prefilled_invoice", required = true)
    @NotNull(message = "prefilled_invoice must not be null")
    private Boolean preFilledInvoice;

    @NotNull(message = "vendor_code must not be null")
    @NotBlank(message = "vendor_code must not be blank")
    @JsonProperty(value = "vendor_code", required = true)
    private String vendor;

    @NotBlank(message = "vendor_name must not be blank")
    @JsonProperty(value = "vendor_name")
    private String vendorName;

    private String currency;

    @NotNull(message = "items must not be null")
    @JsonProperty(required = true, value = "items")
    @Valid
    private List<Product> pids;
    
    @JsonProperty("b2b_invoice_date")
    private Long b2bInvoiceDate;
    
    @JsonProperty("send_to_party")
    private String sendToParty;
    
    @JsonProperty("handover_party")
    private String handoverParty;
    
    @JsonProperty("bill_of_entry_number")
    private String billOfEntryNumber;
    
    @JsonProperty("bill_of_entry_amount")
    private Double billOfEntryAmount;
    
    @JsonProperty("bill_of_entry_date")
    private Long billOfEntryDate;

    @JsonProperty("legal_owner")
    private String legalOwner;

    @JsonProperty("order_accepted_quantity")
    private int orderAcceptedQuantity;

    @JsonProperty("order_rejected_quantity")
    private int orderRejectedQuantity;

    @JsonProperty("total_invoice_qty")
    private int totalInvoiceQty;

    @JsonProperty("total_received_amount")
    private Double totalReceivedAmount;

    @JsonProperty("total_rejected_amount")
    private Double totalRejectedAmount;

//    @JsonProperty("batch_no")
//    private String batchNo;
}
