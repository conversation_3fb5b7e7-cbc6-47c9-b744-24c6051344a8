package com.lenskart.nexs.grn.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IQCGRNItem implements Serializable {

    private String invoiceRefNumber;

    private String grnCode;

    private String boxCode;

    private String pid;

    private String productDesc;

    private Integer totalQty;

    private Integer samplingQty;

    private Integer qcPassQty;

    private Integer qcFailQty;

    private Double boxSamplingPcnt;

    private Double grnSamplingPcnt;

    private Integer version;

    private String status;
}


