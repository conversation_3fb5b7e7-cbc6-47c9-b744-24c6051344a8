package com.lenskart.nexs.grn.model;


import java.sql.Timestamp;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class UserActivity {

	@JsonProperty("user_id")
	private String userId;

    private String action;

    private String facility;

    @JsonProperty(value = "grn_code", required = true)
    @NotBlank
    private String grnCode;

    @JsonProperty("assigned_to")
    private String assignedTo;

    @JsonProperty("performed_at")
    private Timestamp performedAt;

    public UserActivity(){

    }

    public UserActivity(String userId, String action, String facility, String grnCode, String assignedTo){
        this.userId = userId;
        this.action = action;
        this.facility = facility;
        this.grnCode = grnCode;
        this.assignedTo = assignedTo;
    }
}
