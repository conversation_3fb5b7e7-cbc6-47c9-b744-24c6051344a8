package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnicomUnsyncedGrnCodeDetails {
    @JsonProperty(value = "grn_code")
    private String grnCode;

    @JsonProperty(value = "grn_type")
    private String grnType;

    @JsonProperty(value = "facility")
    private String facility;
}
