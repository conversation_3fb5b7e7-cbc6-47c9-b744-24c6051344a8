package com.lenskart.nexs.grn.model;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GRNQcLog {
	
	private Long sno;

	@JsonProperty("grn_code")
	private String grnCode;
	
	private String pid;
	
	@JsonProperty("barcode")
	private String barcode;
	private String action;
	private String reason;
	
	@JsonProperty("created_at")
	private Timestamp createdAt;
	
	@JsonProperty("updated_at")
	private Timestamp updatedAt;
	
	@JsonProperty("created_by")
	private String createdBy;
	
	@JsonProperty("updated_by")
	private String updatedBy;
	
	public GRNQcLog(String grnCode, String pid, String barcode, String action, String reason) {
		this.grnCode = grnCode;
		this.pid = pid;
		this.barcode = barcode;
		this.action = action;
		this.reason = reason;
	}
}
