package com.lenskart.nexs.grn.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GRNMaster {

    @JsonProperty("grn_code")
    private String grnCode;

    @JsonProperty("unicom_grn_code")
    private String unicomGrnCode;

    private String facility;

    @JsonProperty("po_id")
    private String poId;

    @JsonProperty("invoice_id")
    private String invoiceId;

    private Invoice invoice = new Invoice();

    private PurchaseOrder po = new PurchaseOrder();

//    @JsonProperty("estimated_total_quantity")
//    private List<PIDEstimatedQty> estimatedTotalQuantity = new ArrayList<>();

//    @JsonProperty("estimated_quantity_list")
//    private List<PIDEstimatedQtyHistory> estimatedQuantityList;

    @JsonProperty("grn_status")
    private String grnStatus;

    @JsonProperty("grn_synced_to_unicom")
    private int grnSyncedToUnicom;

    @JsonProperty("grn_synced_to_NAV")
    private int grnSyncedToNAV;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("created_at")
    private Timestamp createdAt;

    @JsonProperty("updated_at")
    private Timestamp updatedAt;

    @JsonProperty("grn_sync_status")
    private int grnSyncStatus;

    @JsonProperty("grn_type")
    private String grnType;

    @JsonProperty("batch_no")
    private String batchNo;

    @JsonProperty("currency_conv_rate")
    private Double currencyCovRate;
}
