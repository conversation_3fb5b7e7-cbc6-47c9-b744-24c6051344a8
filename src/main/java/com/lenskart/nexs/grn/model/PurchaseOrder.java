package com.lenskart.nexs.grn.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Valid
@JsonIgnoreProperties(ignoreUnknown = true)
public class PurchaseOrder {

    @NotNull(message = "po_num must not be null")
    @NotBlank(message = "po_num must not be blank")
    @JsonProperty(value = "po_num")
    private String poId;

    @JsonProperty(value = "invoice_level")
    private String invoiceLevel;

    @NotNull(message = "po_items must not be null")
    @JsonProperty("po_items")
    @Valid
    private List<POProduct> pids;
}
