package com.lenskart.nexs.grn.model;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PIDEstimatedQty {

    @NotNull(message = "pid must not be null")
    @NotBlank(message = "pid must not be blank")
    private String pid;

    @NotNull(message = "estimatedQuantity must not be null")
    private Long estimatedQuantity;
}
