package com.lenskart.nexs.grn.model;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PIDEstimatedQtyHistory {

    @NotNull
    @NotBlank
    private String pid;

//    private List<Long> estimatedQuantity;
}
