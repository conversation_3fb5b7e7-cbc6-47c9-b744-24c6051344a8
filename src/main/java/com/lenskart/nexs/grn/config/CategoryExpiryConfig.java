package com.lenskart.nexs.grn.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "expiry.months")
public class CategoryExpiryConfig {

    private Map<String, String> category = new HashMap<>();


    public void setCategory(Map<String, String> category) {
        this.category = category;
    }

    @Bean("categoryExpiryMonths")
    public Map<String, String> categoryExpiryMonths() {
        return this.category;
    }
}