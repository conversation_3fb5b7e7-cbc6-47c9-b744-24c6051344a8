package com.lenskart.nexs.grn.config;

import com.lenskart.nexs.grn.util.CloseGrnCreateGrnItemPutawayHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
public class CloseGrnCreateGrnItemPutaway {

    @Value("${closeGrnCreateGrnItemPutaway.async.threadPool.corePoolSize}")
    private Integer corePoolSize;

    @Value("${closeGrnCreateGrnItemPutaway.async.threadPool.awaitTerminationSeconds}")
    private Integer awaitTerminationSeconds;

    @Value("${closeGrnCreateGrnItemPutaway.async.threadPool.maxPoolSize}")
    private Integer maxPoolSize;

    @Value("${closeGrnCreateGrnItemPutaway.async.threadPool.queueCapacity}")
    private Integer queueCapacity;

    @Value("${closeGrnCreateGrnItemPutaway.queueSize}")
    private int blockingQueueSize;

    @Bean(name = "closeGrnCreateGrnItemPutawayExecutor")
    public Executor threadPoolTaskExecutor(CloseGrnCreateGrnItemPutawayHandler closeGrnCreateGrnItemPutawayHandler) {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("add-Edit-item-to-grn");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setRejectedExecutionHandler(closeGrnCreateGrnItemPutawayHandler);
        return executor;
    }

    @Bean(name = "closeGrnCreateGrnItemPutawayHandler")
    public CloseGrnCreateGrnItemPutawayHandler closeGrnCreateGrnItemPutawayHandler() {
        return new CloseGrnCreateGrnItemPutawayHandler(blockingQueueSize);
    }
}
