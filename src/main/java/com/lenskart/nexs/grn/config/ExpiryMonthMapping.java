package com.lenskart.nexs.grn.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Configuration
public class ExpiryMonthMapping {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    GRNConfig grnConfig;

    @Bean("expiryMonthMappingBean")
    public Map<String, Map<String, Map<String, Map<String, Integer>>>> getExpiryMonths() {
        Map<String, Map<String, Map<String, Map<String, Integer>>>> expiryDate = new HashMap<>();
        try {
            log.info("Converting string expiryDate {} to Map", expiryDate);
            expiryDate = objectMapper.readValue(grnConfig.getExpiryMonthMappingJson(), new TypeReference<Map<String, Map<String, Map<String, Map<String, Integer>>>>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Unable to map the JSON to expiryMonthMappingJson: {}, error {}", grnConfig.getExpiryMonthMappingJson(), e);
        }
        return expiryDate;
    }

    public Integer fetchExpiryMonths(String legalOwner, String poType, String procurementType, String classificationId) {
        Integer expiryDate = null;
        try {
            log.info("[fetchExpiryMonths] Fetching expiryDate legalOwner {}, poType {}, procurementType {}, classificationId {}",
                    legalOwner, poType, procurementType, classificationId);
            Map<String, Map<String, Map<String, Map<String, Integer>>>> expiryDateMap = getExpiryMonths();
            Map<String, Map<String, Map<String, Integer>>> legalOwnerMap = expiryDateMap.getOrDefault(legalOwner, expiryDateMap.get("DEFAULT"));
            log.info("[fetchExpiryMonths] Fetching expiryDate legalOwnerMap {}", legalOwnerMap);
            if (legalOwnerMap != null) {
                Map<String, Map<String, Integer>> poTypeMap = legalOwnerMap.getOrDefault(poType, legalOwnerMap.get("DEFAULT"));
                log.info("[fetchExpiryMonths] Fetching expiryDate poTypeMap {}", poTypeMap);
                if (poTypeMap != null) {
                    Map<String, Integer> procurementTypeMap = poTypeMap.getOrDefault(procurementType, poTypeMap.get("DEFAULT"));
                    log.info("[fetchExpiryMonths] Fetching expiryDate procurementTypeMap {}", procurementTypeMap);
                    if (procurementTypeMap != null) {
                        expiryDate = procurementTypeMap.getOrDefault(classificationId, procurementTypeMap.get("DEFAULT"));
                        log.info("[fetchExpiryMonths] Fetching expiryDate {}", expiryDate);
                    }
                }
            }
            return expiryDate;
        } catch (Exception e) {
            log.info("[fetchExpiryMonths] Fetching expiryDate legalOwner {}, poType {}, procurementType {}," +
                    " classificationId {}, error {}", legalOwner, poType, procurementType, classificationId, e.getMessage());
        }
        return expiryDate;
    }
}
