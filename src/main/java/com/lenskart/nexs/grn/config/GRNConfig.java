package com.lenskart.nexs.grn.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

@Configuration
@Data
public class GRNConfig {

    @Value("${base.url}")
    private String baseUrl;

    @Value("${unicom.base.url}")
    private String unicomBaseUrl;

    @Value("${unicom.call.enabled:true}")
    private boolean unicomCallEnabled;

    @Value("${vendor.standard.info.url}")
    private String vendorStandardInfoUrl;

    @Value("${unicom.create.grn}")
    private String unicomCreateGrn;

    @Value("${unicom.check.barcode}")
    private String unicomCheckBarcode;

    @Value("${grn.topic.name}")
    private String topicName;

    @Value("${invoice.base.url}")
    private String invoiceBaseUrl;

    @Value("${invoice.pid.sync.url}")
    private String invoicePidSyncUrl;

    @Value("${key.prefix}")
    private String keyPrefix;

    @Value("${invoice.grn.validation.url}")
    private String grnValidationUrl;

    @Value("${invoice.pid.validation.url}")
    private String pidValidationUrl;
    
    @Value("${grn.pageSize}")
    private Integer pageSize = 10;

    @Value("${invoice.config.lock.ttl}")
    private long lockTTL;
    
    @Value("${grn.exportCSV.size}")
    private Integer exportCSVSize = 4000;

    @Value("${grn.view.pdf.template.version}")
    private int grnViewPdfTemplateVersion;

    @Value("${grn.view.pdf.template.id}")
    private String grnViewPdfTemplateId;
    
    @Value("${facility.details.url}")
    private String facilityDetailsUrl;

    @Value("${env.prefix}")
    private String envPrefix;

    @Value("${auth.secret.key}")
    private String authSecretKey;

    @Value("${authorized.app}")
    private String appName;

    public static String fromEmailId;

    @Value("${facility.base.url}")
    private String fmsBaseURL;

    @Value("${from.email.id}")
    public void setFromEmailId(String fromEmailId) {
        GRNConfig.fromEmailId = fromEmailId;
    }

    public static String[] toEmailIds;

    @Value("${to.email.ids}")
    public void setToEmailIds(String[] toEmailIds) {
        GRNConfig.toEmailIds = toEmailIds;
    }

    public static String emailSubject;

    @Value("${retry.fail.mail.subject}")
    public void setMailSubject(String emailSubject) {
        GRNConfig.emailSubject = emailSubject;
    }

    public static Integer retryMaxAttempts;

    @Value("${retryMaxAttempts}")
    public void setRetryAttempts(Integer retryMaxAttempts) {
        GRNConfig.retryMaxAttempts = retryMaxAttempts;
    }

    public static Integer retryMaxDelay;

    @Value("${retryMaxDelay}")
    public void setRetryMaxDelay(Integer retryMaxDelay) {
        GRNConfig.retryMaxDelay = retryMaxDelay;
    }

    @Value("${ims.sync.grn.items}")
    private String grnItemSyncImsTopicName;
    
    @Value("${putaway.redis.cache.time}")
    private long putawayResponseTTL;

    @Value("${create.invoiceItem.cl.url}")
    String CreateInvoiceItemCl;

    @Value("${unicom.adaptor.fetch.barcode}")
    private String fetchBarcode;

    @Value("${unicom.fetch.barcode.facility}")
    private String unicomFetchBarcodeFacility;

    @Value("${grn.ims.batchSize}")
    private int imsBatchSize;

    @Value("${nexs.ims.fetch.barcode.detail.size}")
    private int fetchBarcodeDetailSize;

    @Value("${unicom.adaptor.fetch.grn}")
    private String fetchGrnUrl;

    @Value("${barcode.expiryDate.max.allowed.limit:50}")
    private int barcodeExpiryDateMaxAllowedLimit;

    @Value("${fetch.barcode.expiry.date.list}")
    private int fetchBarcodeExpiryDateList = 50;

    @Value("${close.grn.sync.unsynced.putaway.barcode.count}")
    private int closeGrnSyncUnsynedBarcodePutawayCount = 50;

    @Value("${close.grn.retry.to.sync.barcode.in.putaway.time}")
    private int closeGrnRetryToSyncBarcodeInPutawayTime = 10;

    @Value("${expiry.months.mapping}")
    private String expiryMonthMappingJson;

    @Value("${barcode.max.size:50}")
    private int barcodeMaxSize = 50;

    @Value("${grn.do.criteria.mapping:{\"LKSG\" : [\"WHOLESALE02\"]}}")
    private String grnDOCriteriaMapping;

    @Value("${po.type.check.enabled:true}")
    private boolean poTypeCheckEnable;

    @Value("#{'${po.type.ui.inward.not.allowed.po.type:JIT}'.split(',')}")
    private Set<String> poTypeUIInwardNotAllowedPoType;
}
