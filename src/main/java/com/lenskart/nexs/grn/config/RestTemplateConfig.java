package com.lenskart.nexs.grn.config;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
    @Qualifier("wmsRestTemplate")
    public RestTemplate wmsRestTemplate() {
        return new RestTemplate();
    }

    @Bean
    @Qualifier("itRestTemplate")
    public RestTemplate itRestTemplate() {
        return new RestTemplate();
    }
}