package com.lenskart.nexs.grn.config;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.model.GRNItem;

@Configuration
@ConfigurationProperties(prefix = "grn.qc")
public class QcReasonsConfig {

	Map<String, String> reasons = new HashMap<>();

	public Map<String, String> getReasons() {
		return reasons;
	}

	public void setReasons(Map<String, String> reasons) {
		this.reasons = reasons;
	}

	public String getQcReason(GRNItem grnItem) {
		if (StringUtils.isBlank(grnItem.getQcFailCode()) && GRNConstants.QC_FAIL.equalsIgnoreCase(grnItem.getQcStatus())) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid qc fail code");
		}
		String qcReason = reasons.getOrDefault(grnItem.getQcFailCode(), null);
		return qcReason != null ? qcReason : grnItem.getQcFailCode();
	}
	
	public String getQcReason(String qcFailCode) {
		if (StringUtils.isBlank(qcFailCode)) {
			return "NA";
		}
		String qcReason = reasons.getOrDefault(qcFailCode, null);
		return qcReason != null ? qcReason : qcFailCode;
	}
}
