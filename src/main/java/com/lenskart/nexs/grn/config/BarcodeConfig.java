package com.lenskart.nexs.grn.config;

import java.util.*;
import javax.annotation.PostConstruct;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import com.lenskart.nexs.grn.dao.BarcodeDAO;
import com.lenskart.nexs.grn.model.Barcode;
import com.lenskart.nexs.grn.util.BarcodeUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Configuration
@Data
@Slf4j
public class BarcodeConfig implements Runnable {

    private Map<String, Barcode> nonNexsMap;

    private Map<String, Barcode> nexsMap;

    private List<Barcode> numericList;

    @Value("${barcode.refresh.time}")
    private int barcodeRefreshTime;

    @Autowired
    @Qualifier(QualifierConstants.JPA_BARCODE_DAO)
    private BarcodeDAO barcodeDAO;

    @PostConstruct
    public void loadConfig() {
        new Thread(this).start();
    }

    @Override
    public void run() {
        try {
            while (true){
                List<Barcode> nexsBarcodes = barcodeDAO.getBarcode(true);
                List<Barcode> nonNexsBarcodes = barcodeDAO.getBarcode(false);
                Map<String, Barcode> nexsTempMap = new HashMap<>();
                Map<String, Barcode> nonNexsTemMap = new HashMap<>();
                List<Barcode> numericListTemp = new ArrayList<>();
                for(Barcode barcode : nonNexsBarcodes){
                    validateConfig(barcode);
                    String alpha = BarcodeUtils.getAlpha(barcode.getFromSeries());
                    if(alpha.isEmpty())
                        numericListTemp.add(barcode);
                    else
                        nonNexsTemMap.put(alpha, barcode);
                }
                for(Barcode barcode : nexsBarcodes){
                    validateConfig(barcode);
                    String alpha = BarcodeUtils.getAlpha(barcode.getFromSeries());
                    if(alpha.isEmpty())
                        numericListTemp.add(barcode);
                    else
                        nexsTempMap.put(alpha, barcode);
                }
                nonNexsMap = nonNexsTemMap;
                nexsMap = nexsTempMap;
                numericList = numericListTemp;
                threadSleep();
            }
        } catch (Exception ex) {
            log.error("Exception while loading barcode config ", ex);
            System.exit(1);
        }
    }

    public void validateConfig(Barcode barcode) {
        String fromAlpha = BarcodeUtils.getAlpha(barcode.getFromSeries());
        String toAlpha = BarcodeUtils.getAlpha(barcode.getToSeries());
        if(!fromAlpha.equals(toAlpha)){
            log.error("Barcode config in table is wrong for : " + barcode);
            System.exit(1);
        }
    }

    public void threadSleep() {
        try {
            Thread.sleep(barcodeRefreshTime);
        } catch (InterruptedException ex) {
            log.error("Sleeping thread got interrupted in Barcode Config : ", ex);
        }
    }
}
