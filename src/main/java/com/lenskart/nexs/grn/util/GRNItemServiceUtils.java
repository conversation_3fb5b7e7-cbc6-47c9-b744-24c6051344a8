package com.lenskart.nexs.grn.util;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.grn.model.*;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.CreateGRNPIDTO;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.response.ScanItemResponseDTO;

import java.sql.Timestamp;
import java.util.Map;

public class GRNItemServiceUtils implements GRNConstants {

	@Logging
	public static GRNItem convertDtoToGRNItem(GRNItemDTO grnItemDTO) {
		Product productDetails = grnItemDTO.getProduct();
		if(productDetails == null)
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Pid passed is not in the invoice pid list");
		GRNItem grnItem = new GRNItem();
		grnItem.setBarcode(grnItemDTO.getBarcode());
		grnItem.setExpiryDate(grnItemDTO.getExpiryDate());
		grnItem.setGrnCode(grnItemDTO.getGrnCode());
		grnItem.setInvoiceId(grnItemDTO.getInvoiceRefNum());
		grnItem.setInvoiceRefNum(grnItemDTO.getInvoiceRefNum());
		grnItem.setLotNo(grnItemDTO.getLotNo());
		grnItem.setPid(grnItemDTO.getPid());
		grnItem.setQcFailBoxBarcode(grnItemDTO.getQcFailBoxBarcode());
		grnItem.setQcPassBoxBarcode(grnItemDTO.getQcPassBoxBarcode());
		grnItem.setQcReason(grnItemDTO.getQcReason());
		grnItem.setQcStatus(grnItemDTO.getQcStatus());
		grnItem.setQcFailCode(grnItemDTO.getQcFailCode());
		grnItem.setCreatedBy(grnItemDTO.getCreatedBy());
		grnItem.setCategoryId(productDetails.getCategoryId());
		grnItem.setVendorCode(grnItemDTO.getVendorCode());
		grnItem.setBrand(productDetails.getBrand());
		grnItem.setInvoiceQuantity(productDetails.getQuantity());
//		grnItem.setGrnEstimatedQuantity(grnItemDTO.getEstimatedQuantity());
		grnItem.setUpdatedBy(grnItemDTO.getUpdatedBy());
		grnItem.setPoId(grnItemDTO.getPoID());
		grnItem.setFacility(grnItemDTO.getFacilityCode());
		grnItem.setPoQuantity(grnItemDTO.getPoQuantity());
		grnItem.setLegalOwner(grnItemDTO.getLegalOwner());
		grnItem.setStatus(GRNConstants.PASSED);
		return grnItem;
	}

	@Logging
	public static GRNItem convertGrnItemEntityToGRNItem(GrnItemEntity grnItemEntity) {
		GRNItem grnItem = new GRNItem();
		grnItem.setId(grnItemEntity.getId());
		grnItem.setBarcode(grnItemEntity.getBarcode());
		grnItem.setGrnCode(grnItemEntity.getGrnCode());
		grnItem.setStatus(grnItemEntity.getStatus());
		grnItem.setPid(grnItemEntity.getPid());
		grnItem.setPoId(grnItemEntity.getPoId());
		grnItem.setInvoiceRefNum(grnItemEntity.getInvoiceRefNum());
		grnItem.setInvoiceId(grnItemEntity.getInvoiceRefNum());
		grnItem.setVendorCode(grnItemEntity.getVendorCode());
		grnItem.setExpiryDate(
				grnItemEntity.getExpiryDate() == null ? null : new Timestamp(grnItemEntity.getExpiryDate().getTime()));
		grnItem.setLotNo(grnItemEntity.getLotNo());
		grnItem.setGrnEstimatedQuantity(grnItemEntity.getEstimatedQty());
		grnItem.setQcPassBoxBarcode(grnItemEntity.getQcPassBoxBarcode());
		grnItem.setQcStatus(grnItemEntity.getQcStatus());
		grnItem.setQcReason(grnItemEntity.getQcReason());
		grnItem.setQcFailCode(grnItemEntity.getQcFailCode());
		grnItem.setFacility(grnItemEntity.getFacility());
		grnItem.setCreatedBy(grnItemEntity.getCreatedBy());
		grnItem.setUpdatedBy(grnItemEntity.getUpdatedBy());
		grnItem.setLegalOwner(grnItemEntity.getLegalOwner());
		return grnItem;
	}
	
	@Logging
	public static final GRNItem buildGrnItem(GrnItemEntity grnItemEntity) {
        GRNItem grnItem = new GRNItem();
        grnItem.setId(grnItemEntity.getId());
        grnItem.setBarcode(grnItemEntity.getBarcode());
        grnItem.setGrnCode(grnItemEntity.getGrnCode());
        grnItem.setPid(grnItemEntity.getPid());
        grnItem.setPoId(grnItemEntity.getPoId());
        grnItem.setVendorCode(grnItemEntity.getVendorCode());
        grnItem.setLotNo(grnItemEntity.getLotNo());
        grnItem.setQcMasterSampling(grnItemEntity.getQcMasterSampling());
        grnItem.setFailureThresholdQcMaster(grnItemEntity.getFailureThresholdQcMaster());
        grnItem.setStatus(grnItemEntity.getStatus());
        grnItem.setFacility(grnItemEntity.getFacility());
        grnItem.setGrnEstimatedQuantity(grnItemEntity.getEstimatedQty());
        grnItem.setInvoiceRefNum(grnItemEntity.getInvoiceRefNum());
        grnItem.setQcPassBoxBarcode(grnItemEntity.getQcPassBoxBarcode());
        grnItem.setQcStatus(grnItemEntity.getQcStatus());
        grnItem.setQcFailCode(grnItemEntity.getQcFailCode());
        grnItem.setQcReason(grnItemEntity.getQcReason());
        if(grnItemEntity.getExpiryDate() != null)
            grnItem.setExpiryDate(new Timestamp(grnItemEntity.getExpiryDate().getTime()));
        grnItem.setCreatedAt(new Timestamp(grnItemEntity.getCreatedAt().getTime()));
        grnItem.setUpdatedAt(new Timestamp(grnItemEntity.getUpdatedAt().getTime()));
        grnItem.setCreatedBy(grnItemEntity.getCreatedBy());
        grnItem.setUpdatedBy(grnItemEntity.getUpdatedBy());
        return grnItem;
    }

	@Logging
	public static ScanItemResponseDTO convertToResponseDTO(GRNItem grnItem, long totalScanned, long allScanned, long allFailed,ScanItemResponseDTO scanItemResponseDTO) {
		scanItemResponseDTO.setBarcode(grnItem.getBarcode());
		scanItemResponseDTO.setGrnCode(grnItem.getGrnCode());
		scanItemResponseDTO.setQcStatus(grnItem.getQcStatus());
		scanItemResponseDTO.setQcReason(grnItem.getQcReason());
		scanItemResponseDTO.setQcFailCode(grnItem.getQcFailCode());
		scanItemResponseDTO.setStatus(grnItem.getStatus());
		scanItemResponseDTO.setQcMasterSampling(grnItem.getQcMasterSampling());
		scanItemResponseDTO.setFailureThresholdQcMaster(grnItem.getFailureThresholdQcMaster());
//		scanItemResponseDTO.setGrnEstimatedQuantity(estimatedQty);
//		long samplingQty = (long)Math.ceil(grnItem.getGrnEstimatedQuantity() * grnItem.getQcMasterSampling() / 100.0);
//		scanItemResponseDTO.setSamplingQuantity(samplingQty);
		scanItemResponseDTO.setTotalScanned(allScanned);
		long minScanQty = scanItemResponseDTO.getSamplingQuantity() - totalScanned;
		scanItemResponseDTO.setMinScanQuantity(minScanQty > 0 && !PASSED.equals(grnItem.getStatus()) ? minScanQty : 0L);
		scanItemResponseDTO.setTotalPassed(allScanned - allFailed);
		scanItemResponseDTO.setTotalFailed(allFailed);
		scanItemResponseDTO.setGreenFlag(false);
		scanItemResponseDTO.setGradientShift(grnItem.getGradientShift());
//		if (PASSED.equals(grnItem.getStatus()))
//			scanItemResponseDTO.setGreenFlag(true);
		return scanItemResponseDTO;
	}


    public static GRNItem getGRNItem(GRNMasterMetaDTO meta, String pid) {
		GRNItem grnItem = null;
		for(Product product : meta.getInvoice().getPids()){
			if(pid.equals(product.getPid())){
				grnItem = new GRNItem();
				grnItem.setCategoryId(product.getCategoryId());
				grnItem.setBrand(product.getBrand());
				grnItem.setVendorCode(meta.getInvoice().getVendor());
				grnItem.setPid(pid);
				grnItem.setInvoiceQuantity(product.getQuantity());
				break;
			}
		}
		return grnItem;
    }

    public static GRNItem getGRNItem(GRNMaster grnMaster, GRNItem grnItem) {
		for (Product product : grnMaster.getInvoice().getPids()){
			if(product.getPid() != null && product.getPid().equals(grnItem.getPid())){
				grnItem.setBrand(product.getBrand());
				grnItem.setCategoryId(product.getCategoryId());
				grnItem.setInvoiceQuantity(product.getQuantity());
			}
		}
		grnItem.setInvoiceId(grnMaster.getInvoiceId());
		return grnItem;
    }

	public static GRNItem getGRNItem(CreateGRNPIDTO createGRNPIDTO, Map<String, Object> productAttributesMapping) {
		GRNMasterMetaDTO meta = createGRNPIDTO.getMeta();
		Invoice invoice = meta.getInvoice();
		String brand = null;
		long qty = 0;
		Integer categoryId = null;
		if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(createGRNPIDTO.getInvoiceLevel())){
			if (productAttributesMapping.get("classification") != null)
				categoryId = (int) productAttributesMapping.get("classification");
			if (productAttributesMapping.get("brand") != null)
				brand = (String) productAttributesMapping.get("brand");
			POProduct poProduct = CommonUtils.getPOProductDetailsFromPo(meta.getPo().getPids(), createGRNPIDTO.getPid().getPid());
			if(poProduct == null)
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Pid passed is not in the po pid list");
			qty = poProduct.getQuantity();
		} else{
			Product product = CommonUtils.getProductDetailsFromInvoice(invoice.getPids(), createGRNPIDTO.getPid().getPid());
			if(product == null)
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Pid passed is not in the invoice pid list");
			brand = product.getBrand();
			categoryId = product.getCategoryId();
			qty = product.getQuantity();
		}

		GRNItem grnItem = new GRNItem();
		grnItem.setGrnCode(createGRNPIDTO.getGrnCode());
		grnItem.setInvoiceRefNum(invoice.getInvoiceRefNum());
		grnItem.setPid(createGRNPIDTO.getPid().getPid());
		grnItem.setCategoryId(categoryId);
		grnItem.setVendorCode(invoice.getVendor());
		grnItem.setBrand(brand);
		grnItem.setInvoiceQuantity(qty);
//		grnItem.setGrnEstimatedQuantity(createGRNPIDTO.getPid().getEstimatedQuantity());
		return grnItem;
	}
}
