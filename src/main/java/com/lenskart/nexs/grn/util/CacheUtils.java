package com.lenskart.nexs.grn.util;

import java.util.Set;

import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.model.QCMaster;
import com.lenskart.nexs.grn.model.QcConfig;
import org.springframework.web.server.ResponseStatusException;

public class CacheUtils implements GRNConstants {

    @Logging
    public static String getMasterKey(String prefix, GRNItem grnItem) {
        if(grnItem.getCategoryId() == null || grnItem.getCategoryId() == 0)
            throw new ApplicationException("CategoryId should not be null or empty", GRNExceptionStatus.GRN_BAD_REQUEST);
        boolean L2 = false;
        boolean L3 = false;
        boolean L4 = false;
        StringBuilder build = new StringBuilder();
        build.append(grnItem.getCategoryId());
        if(!StringUtils.isEmpty(grnItem.getBrand())) {
            L2 = true;
            build.append(DEL).append(grnItem.getBrand().toLowerCase());
            if(!StringUtils.isEmpty(grnItem.getVendorCode())){
                L3 = true;
                build.append(DEL).append(grnItem.getVendorCode().toLowerCase());
                if(!StringUtils.isEmpty(grnItem.getPid())) {
                    L4 = true;
                    build.append(DEL).append(grnItem.getPid().toLowerCase());
                }
            }
        }
        return prefix + (L2 && L3 && L4 ? QC_MASTER_4 : (L2 && L3 ? QC_MASTER_3 : (L2 ? QC_MASTER_2 : QC_MASTER_1))) + build.toString();
    }

    @Logging
    public static String getGradientKey(String prefix, Integer categoryId) {
        if(categoryId == null || categoryId == 0)
            throw new ApplicationException("Category should not be null or empty", GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(QC_GRADIENT).append(categoryId).toString();
    }

    @Logging
    public static String getGrnConfigKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ApplicationException("GrnCode and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(QC_CONFIG).toString();
    }

    @Logging
    public static String getItemConfigKey(String prefix, String invoiceRefNum, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(invoiceRefNum))
            throw new ApplicationException("InvoiceId and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE).append(DEL).append(invoiceRefNum).append(DEL).append(pid).append(DEL).append(QC_CONFIG).toString();
    }

    @Logging
    public static String getScanCountKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ApplicationException("GrnCode and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(TOTAL_SCANNED).toString();
    }

    @Logging
    public static String getItemScanCountKey(String prefix, String itemId, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(itemId))
            throw new ApplicationException("InvoiceId and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE).append(DEL).append(itemId).append(DEL).append(pid).append(DEL).append(TOTAL_SCANNED_COUNT).toString();
    }

    @Logging
    public static String getFailCountKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ApplicationException("GrnCode and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(TOTAL_FAILED).toString();
    }

    @Logging
    public static String getStatusKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ApplicationException("GrnCode and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(STATUS).toString();
    }

    @Logging
    public static String getGRNManualOverrideKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ApplicationException("GrnCode and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(GRN_MANUAL_OVERRIDE).toString();
    }

//    @Logging
//    public static QcConfig getQcConfig(Set<QCMaster> set, long quantity) {
//        QCMaster qcMaster = set.iterator().next();
//        QcConfig qcConfig = new QcConfig();
//        qcConfig.setGradientOrder(0);
//        qcConfig.setQuantity(quantity);
//        qcConfig.setSamplingPercent(qcMaster.getSamplingPercent());
//        qcConfig.setFailurePercent(qcMaster.getFailurePercent());
//        long samplingQty = (long)Math.ceil(quantity * qcMaster.getSamplingPercent() / 100.0);
//        qcConfig.setSamplingQuantity(samplingQty);
//        long failureQty = (long)Math.ceil(qcConfig.getSamplingQuantity() * qcMaster.getFailurePercent() / 100.0);
//        qcConfig.setFailureQuantity(failureQty);
//        return qcConfig;
//    }

    @Logging
    public static String getASNKey(String prefix, String poId, String vendorInvoiceNum) {
        if(StringUtils.isEmpty(poId) || StringUtils.isEmpty(vendorInvoiceNum))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "po_number and vendor_invoice_num must not be empty");
        return new StringBuilder().append(prefix).append(ASN).append(DEL).append(poId).append(DEL).append(vendorInvoiceNum).toString();
    }
    
    @Logging
    public static String getBoxRequiredKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ApplicationException("GrnCode and productId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(BOX_REQUIRED).toString();
    }

    @Logging
    public static String getAllEstimatedQtyKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "GrnCode and productId should not be empty");
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(ESTIMATED_QTY).toString();
    }

    @Logging
    public static String getAllScannedKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "GrnCode and productId should not be empty");
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(SCANNED).toString();
    }

    @Logging
    public static String getAllFailedKey(String prefix, String grnCode, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(grnCode))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "GrnCode and productId should not be empty");
        return new StringBuilder().append(prefix).append(grnCode).append(DEL).append(pid).append(DEL).append(FAILED).toString();
    }

    @Logging
    public static String getInvoiceLockKey(String prefix, String invoiceRefNum, String pid) {
        if(StringUtils.isEmpty(pid) || StringUtils.isEmpty(invoiceRefNum))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "invoiceRefNum and productId should not be empty");
        return new StringBuilder().append(prefix).append(INVOICE).append(DEL).append(invoiceRefNum).append(DEL).append(pid).append(DEL).append(LOCK).toString();
    }
    
	public static String getPutawayCacheKey(String grnPrefix, GRNItemDTO grnItemDTO) {
		return new StringBuilder().append(grnPrefix).append(grnItemDTO.getGrnCode()).append(DEL)
				.append(grnItemDTO.getUpdatedBy()).append(DEL).append(grnItemDTO.getPid()).toString();
	}

    public static String getPutawayCacheKeyInventoryType(String grnPrefix, GRNItemDTO grnItemDTO, String inventoryType) {
        return new StringBuilder().append(grnPrefix).append(grnItemDTO.getGrnCode()).append(DEL)
                .append(grnItemDTO.getUpdatedBy()).append(DEL).append(grnItemDTO.getPid()).append(DEL).append(inventoryType).toString();
    }

    @Logging
    public static String getGrnPurchaseInvoiceCountKey(String prefix, String invoiceRefNum) {
        if(StringUtils.isEmpty(invoiceRefNum))
            throw new ApplicationException("Invoice ref num should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE_CL).append(DEL).append(invoiceRefNum).append(DEL).append(TOTAL_QTY).toString();
    }

    @Logging
    public static String getGrnPurchaseInvoicePriceKey(String prefix, String invoiceRefNum) {
        if(StringUtils.isEmpty(invoiceRefNum))
            throw new ApplicationException("Invoice ref num should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE_CL).append(DEL).append(invoiceRefNum).append(DEL).append(TOTAL_PRICE).toString();
    }

    public static String getGrnPurchaseInvoiceItemPriceKey(String prefix, String invoiceRefNum, String pId) {
        if(StringUtils.isEmpty(invoiceRefNum) || StringUtils.isEmpty(pId))
            throw new ApplicationException("Invoice ref num or pId should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE_CL).append(DEL).
                append(invoiceRefNum).append(DEL).append(pId).append(DEL).append(ITEM_PRICE).toString();
    }

    @Logging
    public static String getCLInvoiceItemScanCountKey(String prefix, String invoiceRefNumber) {
        if(StringUtils.isEmpty(invoiceRefNumber))
            throw new ApplicationException("InvoiceId should not be null or empty", GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE_CL).append(DEL).
                append(invoiceRefNumber).append(DEL).append(TOTAL_SCANNED_COUNT).toString();
    }

    @Logging
    public static String getCLInvoiceItemPriceKey(String prefix, String invoiceRefNumber) {
        if(StringUtils.isEmpty(invoiceRefNumber))
            throw new ApplicationException("InvoiceId should not be null or empty", GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE_CL).append(DEL).
                append(invoiceRefNumber).append(DEL).append(GRN_TOTAL_PRICE).toString();
    }

    @Logging
    public static String fetchGrnCodeScannedItem(String prefix, String invoiceRefNum, String grnCode) {
        if(StringUtils.isEmpty(grnCode) || StringUtils.isEmpty(invoiceRefNum))
            throw new ApplicationException("InvoiceId and grnCode should not be null or empty",
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        return new StringBuilder().append(prefix).append(INVOICE_GRN).append(DEL).append(invoiceRefNum).append(DEL).
                append(grnCode).append(DEL).append(TOTAL_SCANNED_COUNT).toString();
    }

}