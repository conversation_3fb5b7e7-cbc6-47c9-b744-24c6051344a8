package com.lenskart.nexs.grn.util;

import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.model.BarcodeDetail;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BarcodeUtils {

    @Logging
    public static BarcodeDetail getBarcodeDetail(String barcode) {
        BarcodeDetail barcodeDetail = new BarcodeDetail();
        String alpha = "";
        int numericStart = 0;
        long numeric = 0L;
        
        for (int i = barcode.length() - 1; i >= 0; i--) {
            if(Character.isAlphabetic(barcode.charAt(i))) {
                numericStart = i + 1;
                break;
            }
        }
        
		try {
            if(numericStart != 0) {
			    numeric = Long.parseLong(barcode.substring(numericStart, barcode.length()));
			    alpha = barcode.substring(0, numericStart);
            }
		} catch (Exception ex) {
			log.error("Exception caught for barcode {}: {}", barcode, ex.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Barcode is invalid");
		}
		
        barcodeDetail.setAlpha(alpha.toString());
        barcodeDetail.setNumeric(numeric);
        barcodeDetail.setBarcode(barcode);
        return barcodeDetail;
    }

    @Logging
    public static String getAlpha(String barcode) {
    	String alpha = "";
        int numericStart = 0;
        
        for (int i = barcode.length() - 1; i >= 0; i--) {
            if(Character.isAlphabetic(barcode.charAt(i))) {
                numericStart = i + 1;
                break;
            }
        }
        
		try {
            if(numericStart != 0) {
			    alpha = barcode.substring(0, numericStart);
            }
		} catch (Exception ex) {
			log.error("Exception caught for barcode {}: {}", barcode, ex.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Barcode is invalid");
		}
		return alpha;
    }
}
