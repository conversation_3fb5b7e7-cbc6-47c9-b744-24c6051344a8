package com.lenskart.nexs.grn.util;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.*;

@Data
public class CloseGrnCreateGrnItemPutawayHandler implements RejectedExecutionHandler {
	
	private BlockingQueue<Runnable> blockingQueue;
	
	private ThreadPoolExecutor grnExecutor;
	
	public CloseGrnCreateGrnItemPutawayHandler(int blockingQueueSize) {
		this.blockingQueue = new LinkedBlockingDeque<>(blockingQueueSize);
	}
	

	@Override
	public void rejectedExecution(Runnable rejectedTask, ThreadPoolExecutor executor) {
		this.grnExecutor = this.grnExecutor == null ? executor : this.grnExecutor;
		blockingQueue.add(rejectedTask);
	}
	
	public boolean isQueueEmpty() {
		return blockingQueue.isEmpty();
	}
	
	public void processRejectedTasks() {
		
		List<Future<?>> tasks = new LinkedList<>();
		for(Runnable rejectedTask : blockingQueue) {
			if(grnExecutor.getQueue().remainingCapacity() > 0) {
				Future<?> future = grnExecutor.submit(rejectedTask);
				tasks.add(future);
				blockingQueue.remove(rejectedTask);
			}
			
		}
		
	}

}
