package com.lenskart.nexs.grn.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commons.kafka.service.KafkaProducer;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.retry.RetryHandler;
import com.lenskart.nexs.grn.service.CommunicationService;
import com.sendgrid.SendGridException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Map;

@Slf4j
@Component
public class RetryUtils {

    @Autowired
    private KafkaProducer producer;

    @Autowired
    private static KafkaProducer kafkaProducer;

    @PostConstruct
    private void initKafkaStaticBuilder() {
        kafkaProducer = this.producer;
    }

    @Autowired
    private CommunicationService comService;

    @Autowired
    private static CommunicationService communicationService;

    @PostConstruct
    private void initStaticBuilder() {
        communicationService = this.comService;
    }

    public static <T, U> U postData(String url, T data, Class<U> responseType) throws RestClientException {
        log.info("Post request details ==> url: {}, data: {} ", url, data);
        RetryTemplate template = RetryHandler.retryTemplate();
        U u1 = template.execute(context -> {
            log.info("EXTERNAL POST REQUEST");
            U u = RestUtils.postData(url, data, responseType);
            log.info("Response for the post request made on url: {}, request data: {}, response: {}", url, data, u);
            return u;
        }, retryContext -> {
            String errorMessage = String.valueOf(retryContext);
            log.info("Retry failed for EXTERNAL POST CALL ============> " + errorMessage);
            String message = "External POST call failure for URL " + url + ", data " + data +
                    "." + "<br>" + "Error message: " + errorMessage;
            sendEmail(message, GRNConfig.fromEmailId, GRNConfig.toEmailIds, GRNConfig.emailSubject);
            throw new Exception("External Post call failure for url " + url);
        });
        return u1;
    }

    public static <T, U> U postData(String url, Map<String, String> headerMap, T data, Class<U> responseType) throws RestClientException, SocketTimeoutException {
        log.info("Post request details ==> url: {}, data: {} ", url, data);
        RetryTemplate template = RetryHandler.retryTemplate();
        U u1 = template.execute(context -> {
            log.info("EXTERNAL POST REQUEST");
            U u =  RestUtils.postData(url,headerMap, data,null, responseType, 10000, 60000);
            log.info("Response for the post request made on url: {}, request data: {}, response: {}", url, data, u);
            return u;
        }, retryContext -> {
            String errorMessage = String.valueOf(retryContext);
            log.info("Retry failed for EXTERNAL POST CALL ============> " + errorMessage);
            String message = "External POST call failure for URL " + url + ", data " + data +
                    "." + "<br>" + "Error message: " + errorMessage;
            sendEmail(message, GRNConfig.fromEmailId, GRNConfig.toEmailIds, GRNConfig.emailSubject);
            throw new Exception("External Post call failure for url " + url);
        });
        return u1;
    }

    public static <U> U getData(String url, Map<String, String> headerMap, Map<String, Object> uriVariables, Class<U> responseType) throws RestClientException {
        log.info("Get request details ==> url: {}, headerMap: {}, uriVariables: {}", url, headerMap, uriVariables);
        RetryTemplate template = RetryHandler.retryTemplate();
        U u1 = template.execute(context -> {
            log.info("EXTERNAL GET REQUEST");
            U u = RestUtils.getData(url, headerMap, uriVariables, responseType);
            log.info("Response for the get request made on url: {}, response: {}", url, u);
            return u;
        }, retryContext -> {
            String errorMessage = String.valueOf(retryContext);
            log.info("Retry failed for EXTERNAL GET CALL ============> " + errorMessage);
            String message = "External GET call failure for URL " + url + ", uriVariables " + uriVariables +
                    "." + "<br>" + "Error message: " + errorMessage;
            sendEmail(message, GRNConfig.fromEmailId, GRNConfig.toEmailIds, GRNConfig.emailSubject);
            throw new Exception("External Get call failure for url " + url);
        });
        return u1;
    }

    public static void sendMessage(String message, String topicName, Map<String, Object> headers) {
        log.info("Kafka message details ==> message: {}, topicName: {}, headers: {} ", message, topicName, headers);
        RetryTemplate template = RetryHandler.retryTemplate();
        template.execute(context -> {
            log.info("Send KAFKA for grnCode: {}", message);
            kafkaProducer.sendMessage(message, topicName, headers);
            log.info("Kafka message sent for grnCode: {}", message);
            return null;
        }, retryContext -> {
            String errorMessage = String.valueOf(retryContext);
            log.info("Retry failed for sending KAFKA message ============> " + errorMessage);
            String sendErrorMessage = "Kafka retry failed for sending message for topic " + topicName + ", message " + message +
                    "." + "<br>" + "Error message: " + errorMessage;
            sendEmail(sendErrorMessage, GRNConfig.fromEmailId, GRNConfig.toEmailIds, GRNConfig.emailSubject);
            throw new Exception("Kafka message send failure for topic " + topicName);
        });
    }

    private static void sendEmail(String message, String fromEmailId, String[] toEmailId,
                                  String emailSubject) throws IOException, SendGridException {
        try {
            log.info("=========Sending email =========");
            communicationService.sendMail(message, fromEmailId, toEmailId, emailSubject);
        } catch (Exception e) {
            log.info("Unable to send email for call failure, error in sending email " + e.getMessage());
        }
    }

    public static <T> void sendMessageWithPartitionKey(T message, String topicName, Map<String, Object> headers,
                                                       String partitionKey) {
        log.info("Kafka message details with partition key ==> message: {}, topicName: {}, partitionKey: {}, headers: {} "
                , message, topicName, partitionKey, headers);
        RetryTemplate template = RetryHandler.retryTemplate();

        String kafkaMessage = "";
        try {
            ObjectMapper ob = new ObjectMapper();
            kafkaMessage = ob.writeValueAsString(message);
        } catch (Exception e) {
            log.info("Error in parsing object to string " + e.getMessage());
        }

        String finalKafkaMessage = kafkaMessage;
        template.execute(context -> {
            log.info("Send KAFKA message with partition key");
            kafkaProducer.sendMessage(finalKafkaMessage, topicName, headers, partitionKey);
            log.info("Kafka message sent");
            return null;
        }, retryContext -> {
            String errorMessage = String.valueOf(retryContext);
            log.info("Retry failed for sending KAFKA message with partition key ============> " + errorMessage);
            String sendErrorMessage = "Kafka retry failed for sending message with partition key for topic " + topicName +
                    ", partition key " + partitionKey + ", message " + message + "." + "<br>" + "Error message: " + errorMessage;
            sendEmail(sendErrorMessage, GRNConfig.fromEmailId, GRNConfig.toEmailIds, GRNConfig.emailSubject);
            throw new Exception("Kafka message with partition key send failure for topic " + topicName);
        });
    }

}
