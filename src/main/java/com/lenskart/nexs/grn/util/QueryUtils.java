package com.lenskart.nexs.grn.util;

import lombok.extern.slf4j.Slf4j;

import javax.persistence.Tuple;
import java.util.List;

@Slf4j
public class QueryUtils {

    public static String getQueryByBarcodeOrPid(String barcode, String grnCode, String pid) {
        String query = null;
        if (barcode != null && !"null".equalsIgnoreCase(barcode))
            query = "SELECT * FROM grn_items WHERE grn_code = '" + grnCode + "' and (barcode = '" + barcode + "' OR qc_pass_box_barcode = '"
                    + barcode + "') and qc_status is not null limit 1";
        else if (pid != null && !"null".equalsIgnoreCase(pid))
            query = "SELECT * FROM grn_items WHERE grn_code = '" + grnCode + "' AND pid = '"
                + pid + "' and qc_status is not null limit 1";
        else
            query = "SELECT * FROM grn_items WHERE grn_code = '" + grnCode + "' and qc_status is not null";
        log.info("[getGrnItemUsingBoxOrBarcode] query : {}", query);
        return query;
    }

    public static String getQueryByBoxOrBarcode(String query, List<Tuple> result) {
        if (result != null && !result.isEmpty() && result.get(0).get("qc_pass_box_barcode") != null) {
            query = "SELECT * FROM grn_items WHERE grn_code = '" + result.get(0).get("grn_code") +
                    "' AND qc_pass_box_barcode = '" + result.get(0).get("qc_pass_box_barcode") +
                    "' AND qc_status is not null";
        } else if (result != null && !result.isEmpty() && result.get(0).get("qc_pass_box_barcode") == null) {
            query = "SELECT * FROM grn_items WHERE grn_code = '" + result.get(0).get("grn_code") +
                    "' AND pid = '" + result.get(0).get("pid") +
                    "' AND qc_pass_box_barcode is null AND qc_status is not null";
        }
        return query;
    }

}
