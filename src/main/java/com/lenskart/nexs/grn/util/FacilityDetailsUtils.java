package com.lenskart.nexs.grn.util;

import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.fms.constant.FMSConstants;
import com.lenskart.nexs.fms.model.response.FacilityDetailsResponse;
import com.lenskart.nexs.fms.model.response.ResponseDTO;
import com.lenskart.nexs.grn.config.GRNConfig;

@Component
public class FacilityDetailsUtils {
	
	@Autowired
	private GRNConfig grnConfig;
	
	@CustomLogger
	private Logger log;

	@Logging
	@Cacheable("unicomFacilities")
	public boolean isUnicomFacility(String facilityCode) {
		FacilityDetailsResponse facilityDetailsResponse = getFacilityDetails(facilityCode);
		return checkIfUnicomFacility(facilityDetailsResponse);
	}

	@Logging
    public boolean checkIfUnicomFacility(FacilityDetailsResponse facilityDetailsResponse) {
		log.info("[isUnicomFacility] facilityDetailsResponse {}", facilityDetailsResponse);
		if(facilityDetailsResponse != null && facilityDetailsResponse.getFacilityDetails().getType() != null) {
			return !(FMSConstants.FacilityType.valueOf(facilityDetailsResponse.getFacilityDetails().getType()).isNexsFacilityType());
		}
		return true;
	}

	@Logging
	@Cacheable("facilities")
	public FacilityDetailsResponse getFacilityDetails(String facilityCode) {
		try {
			log.info("Calling getFacilityDetails :: {}",facilityCode);

			String baseUrl = grnConfig.getFmsBaseURL() + grnConfig.getFacilityDetailsUrl();
			String url = baseUrl + "/" + facilityCode;
			ResponseDTO response = RestUtils.getData(url, null, null, ResponseDTO.class);

			FacilityDetailsResponse facilityDetailsResponse = new ObjectMapper().convertValue(response.getData(), FacilityDetailsResponse.class);

			log.info("getFacilityDetails {} response :: {}", facilityCode, response);

			return facilityDetailsResponse;
		} catch (Exception e) {
			log.error("Error fetching facility {} : {}", facilityCode, e);
		}
		return null;
	}
}
