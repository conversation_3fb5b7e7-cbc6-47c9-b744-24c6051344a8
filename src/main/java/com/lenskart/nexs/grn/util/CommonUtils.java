package com.lenskart.nexs.grn.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.grn.dto.response.GRNSummaryDTO;
import com.lenskart.nexs.grn.dto.response.KafkaPostMsg;
import com.lenskart.nexs.grn.dto.response.PIDSummaryDTO;
import com.lenskart.nexs.grn.model.POProduct;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.dto.response.VendorSTDResponseDTO;
import com.lenskart.nexs.grn.model.Product;

public class CommonUtils {
	public static final ObjectMapper objectMapper;

	static {
		objectMapper = new ObjectMapper();
	}

	@Logging
	public static <T> List<T> loadObjectList(Class<T> type, MultipartFile csvFile) {

		if (fileValidation(csvFile, "CSV")) {
			try {
				CsvSchema bootstrapSchema = CsvSchema.emptySchema().withHeader();
				CsvMapper mapper = new CsvMapper();
				MappingIterator<T> readValues = mapper.readerFor(type).with(bootstrapSchema).readValues(csvFile.getInputStream());
				return readValues.readAll();
			} catch (Exception e) {
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
						"Exception occurred while reading the csv file. Please check the file and try again" + e);
			}
		}
		throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Provide proper csv file");
	}

	@Logging
	public static boolean fileValidation(MultipartFile multipartFile, String fileType) {

		String fileExtention[] = null;
		if (multipartFile != null) {
			try {
				if (multipartFile.getInputStream().available() != 0) {
					fileExtention = multipartFile.getOriginalFilename().split("\\.");
				}
			} catch (Exception e) {
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid file found. Kindly upload correct " + fileType + " file. ");
			}
		} else {
			throw new ResponseStatusException(HttpStatus.NO_CONTENT, fileType + " file is missing.");
		}

		if ((fileExtention.length == 2 && !fileType.equalsIgnoreCase(fileExtention[1]))) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid File Extension. Kindly upload " + fileType + " file.");
		} else if (fileExtention.length == 1 || fileExtention.length > 2) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid File Extension. Kindly upload " + fileType + " file.");
		}
		return true;
	}
	
	@Logging
	public static VendorSTDResponseDTO getProductDetailsFromGrnPids(List<Map<String, VendorSTDResponseDTO>> grnPids, String pid) {

		VendorSTDResponseDTO productDetails = null;
		
		if(CollectionUtils.isEmpty(grnPids)) {
			return productDetails;
		}

		for (Map<String, VendorSTDResponseDTO> product : grnPids) {
			
			productDetails = product.getOrDefault(pid, null);
		}
		
		return productDetails;
	}
	
	@Logging
	public static Product getProductDetailsFromInvoice(List<Product> grnPids, String pid) {

		Product productDetails = null;
		
		if(CollectionUtils.isEmpty(grnPids)) {
			return productDetails;
		}

		for (Product product : grnPids) {
			
			if(pid.equalsIgnoreCase(product.getPid())) {
				productDetails = product;
				break;
			}
		}
		
		return productDetails;
	}

	@Logging
	public static POProduct getPOProductDetailsFromPo(List<POProduct> grnPids, String pid) {

		POProduct productDetails = null;

		if(CollectionUtils.isEmpty(grnPids)) {
			return productDetails;
		}

		for (POProduct product : grnPids) {

			if(pid.equalsIgnoreCase(product.getPid())) {
				productDetails = product;
				break;
			}
		}

		return productDetails;
	}

	public static void updateFormat(GRNSummaryDTO grnSummaryDTO) {
		grnSummaryDTO.setTotalReceivedAmount(round(BigDecimal.valueOf(grnSummaryDTO.getTotalReceivedAmount())));
		grnSummaryDTO.setTotalPriceWithTax(round(BigDecimal.valueOf(grnSummaryDTO.getTotalPriceWithTax())));
		grnSummaryDTO.setTotalRejectedAmount(round(BigDecimal.valueOf(grnSummaryDTO.getTotalRejectedAmount())));
		List<PIDSummaryDTO> pidSummaryDTOS = grnSummaryDTO.getPids();
		pidSummaryDTOS.forEach(pidSummaryDTO -> {
			pidSummaryDTO.setPrice(round(BigDecimal.valueOf(pidSummaryDTO.getPrice())));
			Timestamp timestamp = pidSummaryDTO.getExpiryDate();
			if (Objects.nonNull(timestamp)) {
				String expiry = timestamp.toString().split(" ")[0];
				pidSummaryDTO.setExpiry(expiry);
			}
		});
	}

	private static Double round(BigDecimal value) {
		return (value).setScale(2, RoundingMode.HALF_UP).doubleValue();
	}

	@Logging
	public static <T> void createMessageToSendToKafka(T data, String actionName, String topicName, String partitionKey) {
		KafkaPostMsg kafkaMessage = new KafkaPostMsg();
		kafkaMessage.setData(data);
		kafkaMessage.setAction(actionName);
		RetryUtils.sendMessageWithPartitionKey(kafkaMessage, topicName, getKafkaMesageHeaders(), partitionKey);
	}

	public static Map<String, Object> getKafkaMesageHeaders() {
		Map<String, Object> headers = new HashMap<>();
		headers.put("USER_ID", MDC.get("USER_ID"));
		headers.put("USER_MAIL", MDC.get("USER_MAIL"));
		headers.put("FACILITY_CODE", MDC.get("FACILITY_CODE"));
		return headers;
	}

}
