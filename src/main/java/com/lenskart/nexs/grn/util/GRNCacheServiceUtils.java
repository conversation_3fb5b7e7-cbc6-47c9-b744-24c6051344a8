package com.lenskart.nexs.grn.util;

import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceItemEntityService;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnPidMasterEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnPidMasterEntity;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceItemEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.GRNItemDAO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GrnItemPoInvoiceCount;
import com.lenskart.nexs.grn.model.InvoiceQtyAndPriceResponse;
import com.lenskart.nexs.service.RedisHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class GRNCacheServiceUtils implements GRNConstants {

    @Autowired
    private GRNConfig grnConfig;

    @Value("${nexs.grn.item.scan.count.ttl}")
    private long itemScanCountKeyTTL;

    @Autowired
    private GrnItemEntityServiceImpl grnItemEntityService;

    @Autowired
    private GrnPidMasterEntityServiceImpl grnPidMasterEntityService;

    @Autowired
    PurchaseInvoiceEntityService purchaseInvoiceEntityService;

    @Autowired
    PurchaseInvoiceItemEntityService purchaseInvoiceItemEntityService;

    @Autowired
    @Qualifier(QualifierConstants.JPA_GRN_ITEM_DAO)
    private GRNItemDAO grnItemDAO;

    private static final DecimalFormat decimalFormat = new DecimalFormat("0.00");

    @Logging
    public long incrementAllScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    @Logging
    public long incrementScanCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    @Logging
    public long incrementFailCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    @Logging
    public long incrementAllFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    @Logging
    public long getTotalFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    @Logging
    public boolean isGRNManualOverriden(String grnCode, String pid) {
        try {
            return RedisHandler.hasKey(CacheUtils.getGRNManualOverrideKey(grnConfig.getKeyPrefix(), grnCode, pid));
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    @Logging
    public boolean isQcStatusFailed(String grnCode, String pid) {
        return FAILED.equals(getGrnStatus(grnCode, pid));
    }
    @Logging
    public String getGrnStatus(String grnCode, String pid) {
        try {
            if(RedisHandler.hasKey(CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)))
                return RedisHandler.redisOps(RedisOps.GET, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString();
            else
                return getStatusFromDB(grnCode, pid);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    private String getStatusFromDB(String grnCode, String pid) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByGrnCodeAndPid(grnCode, pid);
            return grnItemEntity.getStatus();
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    @Logging
    public boolean isChannelGreen(String grnCode, String pid) {
        return PASSED.equals(getGrnStatus(grnCode, pid));
    }

    @Logging
    public void setGrnStatus(String grnCode, String pid, String status) {
        if(!RedisHandler.hasKey(CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Status key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid), status);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    public void updateGRNPIDMasterStatus(String status, String grnCode, String pid) {
        try {
            GrnPidMasterEntity grnPidMasterEntity = grnPidMasterEntityService.findByIdGrnCodeAndIdPid(grnCode, pid);
            int i = 1;
            grnPidMasterEntity.setGrnPidStatus(status);
            grnPidMasterEntityService.getDao().save(grnPidMasterEntity);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    public long incrementItemQtyCount(String itemId, String pid) {
        return incrementItemQtyCount(itemId, pid, false);
    }

    @Logging
    public long incrementItemQtyCount(String itemId, String pid, boolean poCount) {
        try {
            String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), itemId, pid);
            if (!RedisHandler.hasKey(cacheKey)) {
                long countFromDB = getCountFromDB(itemId, pid, poCount);
                log.info("counter from db for key {} : {}", cacheKey, countFromDB);
                RedisHandler.redisOps(RedisOps.SETIFABSENT, cacheKey, countFromDB, itemScanCountKeyTTL, TimeUnit.HOURS);
            }
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, cacheKey).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private long getCountFromDB(String itemId, String pid, boolean poCount) {
        long count = 0;
        try {
            if (poCount)
                count = grnItemEntityService.countByPidAndPoId(pid, itemId);
            else
                count = grnItemEntityService.countByPidAndInvoiceRefNum(pid, itemId);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return count;
    }

    @Logging
    public long decrementItemQtyCount(String itemId, String pid) {
        return decrementItemQtyCount(itemId, pid, false);
    }

    @Logging
    public long decrementItemQtyCount(String itemId, String pid, boolean poCount) {
        try {
            String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), itemId, pid);
            if (RedisHandler.hasKey(cacheKey)) {
                RedisHandler.redisOps(RedisOps.DEL, cacheKey);
                log.info("Counter deleted from redis for key {}", cacheKey);
            }
            return getCountFromDB(itemId, pid, poCount);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public boolean hasReferenceKey(String refId, String pid) {
        return RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(), refId, pid));
    }

    public boolean hasGrnKey(String grnCode, String pid) {
        return RedisHandler.hasKey(CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), grnCode, pid));
    }

    @Logging
    public long fetchGrnCodeScannedItem(String invoiceRefNum, String grnCode) {
        try {
            log.info("[fetchGrnCodeScannedItem] grnCode {}, invoiceRefNum {}", grnCode, invoiceRefNum);
            String cacheKey = CacheUtils.fetchGrnCodeScannedItem(grnConfig.getKeyPrefix(), invoiceRefNum, grnCode);
            if (RedisHandler.hasKey(cacheKey)) {
                long counter = Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, cacheKey).toString());
                log.info("[fetchGrnCodeScannedItem] counter from redis for key {} : {}", cacheKey, counter);
                return counter;
            } else {
                long countFromDB = fetchGrnCodeScannedItemFromDB(invoiceRefNum);
                log.info("[fetchGrnCodeScannedItem] counter from db for key {} : {}", cacheKey, countFromDB);
                RedisHandler.redisOps(RedisOps.SETVALUETTL, cacheKey, countFromDB, itemScanCountKeyTTL, TimeUnit.HOURS);
                return countFromDB;
            }
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " +
                    ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private long fetchGrnCodeScannedItemFromDB(String invoiceRefNum) {
        try{
            log.info("[fetchGrnCodeScannedItemFromDB] invoiceRefNum {}", invoiceRefNum);
            List<GrnItemEntity> grnItemEntities = grnItemEntityService.findByInvoiceId(invoiceRefNum);
            log.info("[fetchGrnCodeScannedItemFromDB] invoiceRefNum {}, grnItemEntities size {}", invoiceRefNum,
                    grnItemEntities.size());
            return grnItemEntities.size();
        }catch (Exception e){
            log.error("[fetchGrnCodeScannedItemFromDB] Error in fetching grn item count from invoice ref num {}, error {}",
                    invoiceRefNum, e.getMessage());
            throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    public InvoiceQtyAndPriceResponse getInvoiceDetails(String invoiceRefNum) {
        try {
            log.info("[getInvoiceDetails] Get Invoice details {}", invoiceRefNum);
            String cacheInvoiceQtyKey = CacheUtils.getGrnPurchaseInvoiceCountKey(grnConfig.getKeyPrefix(), invoiceRefNum);
            String cacheInvoicePriceKey = CacheUtils.getGrnPurchaseInvoicePriceKey(grnConfig.getKeyPrefix(), invoiceRefNum);
            InvoiceQtyAndPriceResponse response = new InvoiceQtyAndPriceResponse();
            response.setInvoiceRefNum(Long.valueOf(invoiceRefNum));
            log.info("[getInvoiceDetails] Get Invoice details {}, cacheInvoiceQtyKey {}, cacheInvoicePriceKey {}",
                    invoiceRefNum, cacheInvoiceQtyKey, cacheInvoicePriceKey);

            if (RedisHandler.hasKey(cacheInvoiceQtyKey) && RedisHandler.hasKey(cacheInvoicePriceKey)) {
                String cacheInvoiceQtyCount = RedisHandler.redisOps(RedisOps.GET, cacheInvoiceQtyKey).toString();
                String cacheInvoicePriceCount = RedisHandler.redisOps(RedisOps.GET, cacheInvoicePriceKey).toString();
                log.info("[getInvoiceDetails] Qty from redis for key {} : qty {}, price key {} : {}",
                        cacheInvoiceQtyKey, cacheInvoiceQtyCount, cacheInvoicePriceKey, cacheInvoicePriceCount);
                response.setInvoiceTotalCount(Integer.parseInt(cacheInvoiceQtyCount));
                response.setInvoiceTotalPrice(Double.parseDouble(cacheInvoicePriceCount));
            } else {
                PurchaseInvoiceEntity invoiceDetails = getPurchaseInvoiceCountFromDB(invoiceRefNum);
                int totalInvoiceQty =  invoiceDetails.getTotalInvoiceQty();
                Double totalInvoicePrice =  invoiceDetails.getTotalInvoiceAmount();
                log.info("[getInvoiceDetails] Qty from db for key {} : qty {}, price key {} : {}",
                        cacheInvoiceQtyKey, totalInvoiceQty, cacheInvoicePriceKey, totalInvoicePrice);
                RedisHandler.redisOps(RedisOps.SETVALUETTL, cacheInvoiceQtyKey, totalInvoiceQty,
                        itemScanCountKeyTTL, TimeUnit.HOURS);
                RedisHandler.redisOps(RedisOps.SETVALUETTL, cacheInvoicePriceKey, totalInvoicePrice,
                        itemScanCountKeyTTL, TimeUnit.HOURS);
                response.setInvoiceTotalCount(totalInvoiceQty);
                response.setInvoiceTotalPrice(totalInvoicePrice);
            }
            log.info("[getInvoiceDetails] response {}", response);
            return response;
        } catch (Exception ex) {
            log.error("[getInvoiceDetails] invoiceRefNumber {}, {}", invoiceRefNum, ex.getMessage());
            throw new ApplicationException("Unexpected Exception " +
                    ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }


    private PurchaseInvoiceEntity getPurchaseInvoiceCountFromDB(String invoiceRefNum) {
        try {
            PurchaseInvoiceEntity invoiceEntity = purchaseInvoiceEntityService.getInvoiceByReferenceNumber(
                    Integer.valueOf(invoiceRefNum), true);
            return invoiceEntity;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public Double getInvoiceItemPrice(String invoiceRefNum, String pId) {
        try {
            log.info("[getInvoiceItemPrice] Get Invoice item price details {}, {}", invoiceRefNum, pId);
            String invoiceItemPriceKey = CacheUtils.
                    getGrnPurchaseInvoiceItemPriceKey(grnConfig.getKeyPrefix(), invoiceRefNum, pId);
            log.info("[getInvoiceItemPrice] Get Invoice item price details {}, {}, invoiceItemPriceKey {}",
                    invoiceRefNum, pId, invoiceItemPriceKey);
            Double invoicePrice = 0.0;
            if (RedisHandler.hasKey(invoiceItemPriceKey)) {
                String price = RedisHandler.redisOps(RedisOps.GET, invoiceItemPriceKey).toString();
                log.info("[getInvoiceItemPrice] Get Invoice Item Price from Redis invoiceRefNum {}, pId {}, price {}",
                        invoiceRefNum, pId, price);
                invoicePrice = Double.parseDouble(price);
            } else{
                log.info("[getInvoiceItemPrice] Get Invoice Item Price from DB invoiceRefNum {}, pId {}",
                        invoiceRefNum, pId);
                PurchaseInvoiceItemEntity item = getPurchaseInvoiceItemFromDB(invoiceRefNum, pId);
                invoicePrice = item.getVendorUnitCostPrice() +
                        (double) (item.getVendorUnitCostPrice() * item.getCgstPer() + item.getVendorUnitCostPrice() * item.getIgstPer() +
                        item.getVendorUnitCostPrice() * item.getSgstPer() + item.getVendorUnitCostPrice() * item.getUgstPer()) /
                                (double) 100;
                RedisHandler.redisOps(RedisOps.SETVALUETTL, invoiceItemPriceKey, decimalFormat.format(invoicePrice),
                        itemScanCountKeyTTL, TimeUnit.HOURS);
            }
            log.info("[getInvoiceItemPrice] Get Invoice item price details invoiceRefNum {}, pId {}, invoicePrice {}",
                    invoiceRefNum, pId, invoicePrice);
            return invoicePrice;
        } catch (Exception ex) {
            log.error("[getInvoiceItemPrice] invoiceRefNumber {}, {}", invoiceRefNum, ex.getMessage());
            throw new ApplicationException("Unexpected Exception " +
                    ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private PurchaseInvoiceItemEntity getPurchaseInvoiceItemFromDB(String invoiceRefNum, String pId) {
        try {
            PurchaseInvoiceItemEntity invoiceItemEntity = purchaseInvoiceItemEntityService.
                    findByInvoiceRefNumberAndProductId(Integer.valueOf(invoiceRefNum), Integer.valueOf(pId));
            return invoiceItemEntity;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    public long incrementCLInvoiceItemQtyCount(String invoiceRefNumber) {
        try {
            log.info("[incrementCLInvoiceItemQtyCount] invoiceRefNumber {}", invoiceRefNumber);
            String cacheKey = CacheUtils.getCLInvoiceItemScanCountKey(grnConfig.getKeyPrefix(), invoiceRefNumber);
            if (!RedisHandler.hasKey(cacheKey)) {
                long countFromDB = getCLInvoiceCountFromDB(invoiceRefNumber);
                log.info("[incrementCLInvoiceItemQtyCount] CL counter from db for key {} : {}", cacheKey, countFromDB);
                RedisHandler.redisOps(RedisOps.SETIFABSENT, cacheKey, countFromDB, itemScanCountKeyTTL, TimeUnit.HOURS);
            }
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, cacheKey).toString());
        } catch (Exception ex) {
            log.error("[incrementCLInvoiceItemQtyCount] invoiceRefNumber {}, {}", invoiceRefNumber, ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public long getCLInvoiceCountFromDB(String invoiceRefNumber) {
        long count = 0;
        try {
            log.info("CL getCLInvoiceCountFromDB {}", invoiceRefNumber);
            count = grnItemEntityService.countByInvoiceRefNum(invoiceRefNumber);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return count;
    }

    public double incrementCLInvoiceItemPrice(String invoiceRefNumber, Double grnInvoiceItemPrice,
                                              String poNum, String grnCode) {
        try {
            log.info("[incrementCLInvoiceItemQtyCount] invoiceRefNumber {}, grnInvoiceItemPrice {}, poNum {}, grnCode {}",
                    invoiceRefNumber, grnInvoiceItemPrice, poNum, grnCode);
            String cacheKey = CacheUtils.getCLInvoiceItemPriceKey(grnConfig.getKeyPrefix(), invoiceRefNumber);
            log.info("[incrementCLInvoiceItemQtyCount] invoiceRefNumber {}, grnInvoiceItemPrice {}, poNum {}, grnCode {}, cacheKey {}",
                    invoiceRefNumber, grnInvoiceItemPrice, poNum, grnCode, cacheKey);
            Double invoiceItemPrice = 0.0;
            if (RedisHandler.hasKey(cacheKey)) {
                invoiceItemPrice = Double.parseDouble(RedisHandler.redisOps(RedisOps.GET, cacheKey).toString());
                log.info("[incrementCLInvoiceItemPrice] CL price from redis for key {} : {}", cacheKey, invoiceItemPrice);
            } else {
                invoiceItemPrice = getCLInvoicePriceFromDB(grnCode, invoiceRefNumber, poNum);
                log.info("[incrementCLInvoiceItemPrice] CL price from db for key {} : {}", cacheKey, invoiceItemPrice);
            }
            Double totalPrice= invoiceItemPrice + grnInvoiceItemPrice;
            RedisHandler.redisOps(RedisOps.SETVALUETTL, cacheKey, totalPrice,
                    itemScanCountKeyTTL, TimeUnit.HOURS);
            log.info("[incrementCLInvoiceItemPrice] CL new price for key {} : {}", cacheKey, totalPrice);
            return totalPrice;
        } catch (Exception ex) {
            log.error("[incrementCLInvoiceItemPrice] invoiceRefNumber {}, {}", invoiceRefNumber, ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public Double getCLInvoicePriceFromDB(String grnCode, String invoiceRefNumber, String poNum) {
        try{
            log.info("[getCLInvoicePriceFromDB] grnCode {}, invoiceRefNumber {}, poNum {}",
                    grnCode, invoiceRefNumber, poNum);
            Double price =0.0;

            List<PurchaseInvoiceItemEntity> invoiceItemEntity = purchaseInvoiceItemEntityService.
                    getByInvoiceRefNumber(Integer.valueOf(invoiceRefNumber), true);
            Map<Integer, String> pidPriceMapping = new HashMap<>();
            for(PurchaseInvoiceItemEntity item: invoiceItemEntity){
                double invoicePrice = item.getVendorUnitCostPrice() +
                        (double) (item.getVendorUnitCostPrice() * item.getCgstPer() + item.getVendorUnitCostPrice() * item.getIgstPer() +
                                item.getVendorUnitCostPrice() * item.getSgstPer() + item.getVendorUnitCostPrice() * item.getUgstPer()) /
                                (double) 100;
                pidPriceMapping.put(item.getId(), decimalFormat.format(invoicePrice));
            }
            List<GrnItemPoInvoiceCount> invoiceGrnPidScannedQtyList = grnItemDAO.getPoCountAndPriceForGrnPid(grnCode,
                    invoiceRefNumber, poNum);
            for(GrnItemPoInvoiceCount grnPid: invoiceGrnPidScannedQtyList){
                if(pidPriceMapping.containsKey(Integer.valueOf(grnPid.getPid()))){
                    price = price + grnPid.getScannedQty() *
                            Double.parseDouble(pidPriceMapping.get(Integer.valueOf(grnPid.getPid())));
                } else {
                    log.info("[getCLInvoicePriceFromDB] Invoice item does not exist for pid {}", grnPid.getPid());
                }
            }
            log.info("[getCLInvoicePriceFromDB] grnCode {}, invoiceRefNumber {}, poNum {}, price {}",
                    grnCode, invoiceRefNumber, poNum, price);
            return price;
        } catch (Exception ex){
            log.error("[getCLInvoicePriceFromDB] invoiceRefNumber {}, {}", invoiceRefNumber, ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public void decrementCLInvoiceItemQtyCount(String invoiceRefNumber) {
        try {
            log.info("[decrementCLInvoiceItemQtyCount] invoiceRefNumber {}", invoiceRefNumber);
            String cacheKey = CacheUtils.getCLInvoiceItemScanCountKey(grnConfig.getKeyPrefix(), invoiceRefNumber);
            if (RedisHandler.hasKey(cacheKey)) {
                RedisHandler.redisOps(RedisOps.DEL, cacheKey);
                log.info("[decrementCLInvoiceItemQtyCount] CL counter deleted from redis for key {}", cacheKey);
            }
        } catch (Exception ex) {
            log.error("[incrementCLInvoiceItemQtyCount] invoiceRefNumber {}, {}", invoiceRefNumber, ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public void decrementCLInvoiceItemPrice(String invoiceRefNumber, Double grnInvoiceItemPrice) {
        try {
            log.info("[decrementCLInvoiceItemPrice] invoiceRefNumber {}, grnInvoiceItemPrice {}",
                    invoiceRefNumber, grnInvoiceItemPrice);
            String cacheKey = CacheUtils.getCLInvoiceItemPriceKey(grnConfig.getKeyPrefix(), invoiceRefNumber);
            if (RedisHandler.hasKey(cacheKey)) {
                RedisHandler.redisOps(RedisOps.DEL, cacheKey);
                log.info("[decrementCLInvoiceItemPrice] CL price for key {} is deleted", cacheKey);
            }
        } catch (Exception ex) {
            log.error("[decrementCLInvoiceItemPrice] invoiceRefNumber {}, {}", invoiceRefNumber, ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
