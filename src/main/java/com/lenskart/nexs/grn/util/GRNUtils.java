package com.lenskart.nexs.grn.util;

import com.lenskart.nexs.common.entity.po.grn.GrnNumGenEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.dto.GRNMasterDTO;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.CreateGRNPIDTO;
import com.lenskart.nexs.grn.dto.request.GRNUpdateDTO;
import com.lenskart.nexs.grn.dto.request.UnicomCreateGRNDTO;
import com.lenskart.nexs.grn.dto.response.*;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.model.AuthToken;
import com.lenskart.nexs.util.AuthUtil;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import com.nexs.po.common.enums.PurchaseOrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class GRNUtils implements GRNConstants {

	@Logging
	public static GrnNumGenEntity getGRNSequence(String envPrefix, String keyPrefix, GrnNumGenEntity grnNumGenEntityCurrent) throws Exception {
		String facility = MDC.get(FACILITY_CODE);
		if(facility == null)
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code must be passed");
		//long grnNo = (long) RedisHandler.redisOps(RedisOps.INCR, keyPrefix + SEQ_KEY + facility );

		log.info("facility code: {}",facility);
		String grnNo = grnNumGenEntityCurrent.getGrnNumGen();
		log.info("grnNO: {}",grnNo);
		long incrNum = Long.parseLong(grnNo.split(HD)[2])+1;
		log.info("incrNum: {}",incrNum);
		String grnNumberKey = getGRNNumberKey(envPrefix, incrNum);
		log.info("grnNumberKey: {}",grnNumberKey);
		return generateEntityToStore(facility, grnNumberKey);
	}

	private static GrnNumGenEntity generateEntityToStore(String facility, String grnNumberKey) {
		GrnNumGenEntity grnNumGenEntity = new GrnNumGenEntity();
		grnNumGenEntity.setGrnNumGen(grnNumberKey);
		grnNumGenEntity.setFacilityCode(facility);
		grnNumGenEntity.setEnabled(true);
		grnNumGenEntity.setCreatedAt(new Date());
		grnNumGenEntity.setUpdatedAt(new Date());
		grnNumGenEntity.setCreatedBy(StringUtils.isEmpty(MDC.get(USER_ID))?"SYSTEM":MDC.get(USER_ID));
		grnNumGenEntity.setUpdatedBy(StringUtils.isEmpty(MDC.get(USER_ID))?"SYSTEM":MDC.get(USER_ID));
		return grnNumGenEntity;
	}

	@Logging
	public static String getGRNNumberKey(String envPrefix, long number) {
		SimpleDateFormat format = new SimpleDateFormat("yy-MM");
		String currentDate = format.format(new Date());
		String[] parts = currentDate.split("-");
		int year = Integer.parseInt(parts[0]);
		int month = Integer.parseInt(parts[1]);
		if (month > MARCH)
			year = year + 1;
		log.info("[getGRNNumberKey] envPrefix {}, facility code {}, year {}", envPrefix,
				MDC.get(FACILITY_CODE), year);
		String key = envPrefix + GRN_NUM_PREFIX + MDC.get(FACILITY_CODE) + HD + year + HD;
		log.info("[getGRNNumberKey] key {}", key);
		int len = 16 - key.length();
		if(len <= 0)
			throw new ResponseStatusException(HttpStatus.INSUFFICIENT_STORAGE, "Sequence number can not fit the len : " + len);
		return key + StringUtils.leftPad(String.valueOf(number), len, ZERO);
	}

	@Logging
	public static UpdateGRNResponseDTO getUpdateGRNResponseDTO(GRNMasterDTO grnMasterDTO) {
		UpdateGRNResponseDTO updateGRNResponseDTO = new UpdateGRNResponseDTO();
		updateGRNResponseDTO.setMeta(grnMasterDTO.getMeta());
		return updateGRNResponseDTO;
	}

	@Logging
	public static ResponseDTO<Result<Map<String, Object>>> getMasterResponseDTO(GRNMasterMetaDTO grnMasterMetaDTO, GRNMaster grnMaster, Object pids, String status,
			String message, String errorCode) {
		Result<Map<String, Object>> result = new Result<>();
		Map<String, Object> data = new HashMap<>();
		data.put("grn_code", grnMaster.getGrnCode());
		data.put("grn_status", status);
		data.put("invoice_date", grnMasterMetaDTO.getInvoice().getInvoiceDate());
		data.put("invoice_ref_num", grnMasterMetaDTO.getInvoice().getInvoiceRefNum());
		data.put("po_id", grnMasterMetaDTO.getInvoice().getPoId());
		data.put("grn_pids", pids);
		data.put("vendor", grnMasterMetaDTO.getInvoice().getVendor());
		result.setMeta(grnMasterMetaDTO);
		result.setData(data);
		ResponseDTO<Result<Map<String, Object>>> responseDTO = new ResponseDTO<>();
		responseDTO.setResult(result);
		responseDTO.setDisplayMessage(message);
		if (errorCode != null)
			responseDTO.setErrorCode(errorCode);
		return responseDTO;
	}

	@Logging
	public static CreateGRNResponseDTO getCreateResponseDTO(GRNMasterMetaDTO grnMasterMetaDTO, GRNMaster grnMaster, String status) {
		CreateGRNResponseDTO createGrnResponseDTO = new CreateGRNResponseDTO();
		createGrnResponseDTO.setGrnCode(grnMaster.getGrnCode());
		createGrnResponseDTO.setGrnStatus(status);
		createGrnResponseDTO.setInvoiceDate(grnMasterMetaDTO.getInvoice().getInvoiceDate());
		createGrnResponseDTO.setInvoiceRefNum(grnMasterMetaDTO.getInvoice().getInvoiceRefNum());
		createGrnResponseDTO.setVendorInvoiceNumber(grnMasterMetaDTO.getInvoice().getInvoiceId());
		createGrnResponseDTO.setPoId(grnMasterMetaDTO.getInvoice().getPoId());
		createGrnResponseDTO.setGrnPids(null);
		createGrnResponseDTO.setVendor(grnMasterMetaDTO.getInvoice().getVendor());
		createGrnResponseDTO.setMeta(grnMasterMetaDTO);
		return createGrnResponseDTO;
	}

//	@Logging
//	public static List<VendorSTDInfoDTO> getVendorInfoList(GRNMaster grnMaster) {
//		List<VendorSTDInfoDTO> vendorSTDInfoDTOList = new ArrayList<>();
//		for (PIDEstimatedQty currPID : grnMaster.getEstimatedTotalQuantity()) {
//			for (Product product : grnMaster.getInvoice().getPids()) {
//				if (currPID.getPid().equals(product.getPid())) {
//					VendorSTDInfoDTO vendorSTDInfoDTO = new VendorSTDInfoDTO(currPID.getPid(), grnMaster.getInvoice().getVendor(), product.getBrand(),
//							product.getCategoryId(), currPID.getEstimatedQuantity());
//					vendorSTDInfoDTOList.add(vendorSTDInfoDTO);
//				}
//			}
//		}
//		return vendorSTDInfoDTOList;
//	}

	@Logging
	public static GRNPIDMaster getGRNPIDMaster(CreateGRNPIDTO createGRNPIDTO, short manualOverride,
											   Map<String, Object> productAttributesMapping) {
		Invoice invoice = createGRNPIDTO.getMeta().getInvoice();
		PurchaseOrder po = createGRNPIDTO.getMeta().getPo();
		String brand = null , pidDescription = null;
		Integer categoryId = null;
		if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(createGRNPIDTO.getInvoiceLevel())){
			if (productAttributesMapping.get("classification") != null)
				categoryId = (int) productAttributesMapping.get("classification");
			if (productAttributesMapping.get("brand") != null)
				brand = (String) productAttributesMapping.get("brand");
			if (productAttributesMapping.get("desc") != null)
				pidDescription = (String) productAttributesMapping.get("desc");
		}else{
			Product product = CommonUtils.getProductDetailsFromInvoice(invoice.getPids(), createGRNPIDTO.getPid().getPid());
			brand = product.getBrand();
			categoryId = product.getCategoryId();
			pidDescription =  product.getPidDescription();
		}
		POProduct poProduct = CommonUtils.getPOProductDetailsFromPo(po.getPids(), createGRNPIDTO.getPid().getPid());
		GRNPIDMaster grnpidMaster = new GRNPIDMaster(createGRNPIDTO.getGrnCode(), createGRNPIDTO.getPid().getPid(),
				createGRNPIDTO.getMeta().getInvoice().getInvoiceId(), createGRNPIDTO.getMeta().getInvoice().getInvoiceRefNum(),
				invoice.getVendor(), brand ,categoryId , pidDescription,
				poProduct.getPrice(), poProduct.getPriceWithTaxes() / poProduct.getQuantity(),
				poProduct.getCgstRate()/ poProduct.getQuantity(), poProduct.getSgstRate()/ poProduct.getQuantity(), poProduct.getIgstRate()/ poProduct.getQuantity(), createGRNPIDTO.getPid().getEstimatedQuantity(),
				new ArrayList<>(Collections.singletonList(createGRNPIDTO.getPid().getEstimatedQuantity())), manualOverride,
				PENDING, MDC.get("USER_ID"), MDC.get("USER_ID"));
		return grnpidMaster;
	}

	@Logging
	public static UnicomCreateGRNDTO mapToUnicomGRNDTO(GRNMaster grnMaster) {
		UnicomCreateGRNDTO unicomCreateGRNDTO = new UnicomCreateGRNDTO();
		unicomCreateGRNDTO.setVendorInvoiceNumber(grnMaster.getInvoice().getInvoiceId());
		unicomCreateGRNDTO.setFacility(grnMaster.getFacility());
		unicomCreateGRNDTO.setInvoiceDate(grnMaster.getInvoice().getInvoiceDate());
		unicomCreateGRNDTO.setInvoiceReferenceNum(grnMaster.getInvoice().getInvoiceRefNum());
		unicomCreateGRNDTO.setPoId(grnMaster.getPoId());
		Map<String, Object> customFields = new HashMap<>();
		customFields.put(SEND_TO_PARTY, grnMaster.getInvoice().getSendToParty());
		customFields.put(HANDOVER_BY, grnMaster.getInvoice().getHandoverParty());
		customFields.put(B2B_INVOICE_DATE, grnMaster.getInvoice().getB2bInvoiceDate());
		customFields.put(BILL_OF_ENTRY_DATE, grnMaster.getInvoice().getBillOfEntryDate());
		customFields.put(BILL_OF_ENTRY_AMOUNT, grnMaster.getInvoice().getBillOfEntryAmount());
		
		unicomCreateGRNDTO.setCustomFields(customFields);
		return unicomCreateGRNDTO;
	}

	@Logging
	public static List<GRNDetailsDTO> getGRNList(List<GRNMaster> grns, Map<String, GRNDetailsDTO> grnDetails, Invoice invoice) {

		List<GRNDetailsDTO> grnList = new ArrayList<>();
		GRNDetailsDTO grnDetail;

		for (GRNMaster grnMaster : grns) {
			grnDetail = new GRNDetailsDTO();
			grnDetail.setGrnCode(grnMaster.getGrnCode());
			grnDetail.setCreatedOn(grnMaster.getCreatedAt());
			grnDetail.setGrnStatus(grnMaster.getGrnStatus());
			grnDetail.setInvoiceReferenceNum(grnMaster.getInvoiceId());
			grnDetail.setPoId(grnMaster.getPoId());
			grnDetail.setInvoiceDate(invoice.getInvoiceDate());
			grnDetail.setPoDate(invoice.getPoDate());
			grnDetail.setVendor(invoice.getVendor());
			grnDetail.setCreatedBy(grnMaster.getCreatedBy());

			if (!CollectionUtils.isEmpty(grnDetails)) {
				GRNDetailsDTO grnPidInfo = grnDetails.getOrDefault(grnMaster.getGrnCode(), null);

				if (grnPidInfo != null) {
					grnDetail.setTotalScanned(grnPidInfo.getTotalScanned());
					grnDetail.setTotalFailCount(grnPidInfo.getTotalFailCount());
					grnDetail.setGrnPids(grnPidInfo.getGrnPids());
				}
			}

			grnList.add(grnDetail);
		}

		return grnList;

	}

	@Logging
	public static Map<String, GRNDetailsDTO> getGRNPidMap(List<GRNPIDMaster> grnPids, Map<String, Map<String, Object>> grnPidCounts) {

		Map<String, GRNDetailsDTO> grnPidMap = null;
		int failCount = 0;
		int totalCount = 0;
		int passCount = 0;

		if (!CollectionUtils.isEmpty(grnPids)) {

			grnPidMap = new HashMap<>();
			GRNPidDetailsDTO pidDetails;
			for (GRNPIDMaster grnPid : grnPids) {
				failCount = 0;
				totalCount = 0;
				passCount = 0;

				pidDetails = new GRNPidDetailsDTO();
				pidDetails.setPid(grnPid.getPid());
				pidDetails.setBrand(grnPid.getBrand());
				pidDetails.setCategoryId(grnPid.getCategoryId());
//				pidDetails.setEstimatedQty(grnPid.getEstimatedQuantity());
				pidDetails.setStatus(grnPid.getPidStatus());
				pidDetails.setPrice(grnPid.getPrice());
				if (!CollectionUtils.isEmpty(grnPidCounts)) {
					Map<String, Object> pidCounts = grnPidCounts.getOrDefault(grnPid.getGrnCode(), null);
					Map<String, Object> pidCountMap = pidCounts != null ? (Map<String, Object>) pidCounts.getOrDefault(grnPid.getPid(), null) : null;
					failCount = pidCountMap != null ? Integer.parseInt(pidCountMap.get("fail_count").toString()) : 0;
					totalCount = pidCountMap != null ? Integer.parseInt(pidCountMap.get("total_count").toString()) : 0;
					passCount = totalCount - failCount;
					pidDetails.setQcFailedCount(failCount);
					pidDetails.setQcPassCount(passCount);
					pidDetails.setTotalScanned(totalCount);
				}

				if (grnPidMap.containsKey(grnPid.getGrnCode())) {
					int totalFailCount = grnPidMap.get(grnPid.getGrnCode()).getTotalFailCount();
					grnPidMap.get(grnPid.getGrnCode()).setTotalFailCount(totalFailCount + failCount);

					int totalScannedCount = grnPidMap.get(grnPid.getGrnCode()).getTotalScanned();
					grnPidMap.get(grnPid.getGrnCode()).setTotalScanned(totalScannedCount + totalCount);

					grnPidMap.get(grnPid.getGrnCode()).getGrnPids().add(pidDetails);
				} else {
					List<GRNPidDetailsDTO> pids = new ArrayList<>();
					GRNDetailsDTO grnDetails = new GRNDetailsDTO();
					grnDetails.setTotalFailCount(failCount);
					grnDetails.setTotalScanned(totalCount);
					pids.add(pidDetails);
					grnDetails.setGrnPids(pids);
					grnPidMap.put(grnPid.getGrnCode(), grnDetails);
				}
			}

		}

		return grnPidMap;
	}

	@Logging
	public static String getQcStatus(String barcode, String qcStatus) {
		if (barcode.equalsIgnoreCase(qcStatus)) {
			return QC_PASS;
		} else {
			return QC_FAIL;
		}
	}

	public static GRNSummaryDTO getSummary(GRNMaster grnMaster) {
		
		Invoice invoice = grnMaster.getInvoice();
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		
		String b2bInvoiceDate = invoice != null ? invoice.getB2bInvoiceDate() != null ? df.format(new Date(invoice.getB2bInvoiceDate())) : null : null;
		String billOfEntryDate = invoice != null ? invoice.getBillOfEntryDate() != null ? df.format(new Date(invoice.getBillOfEntryDate())) : null : null;

		GRNSummaryDTO grnSummaryDTO = new GRNSummaryDTO();
		grnSummaryDTO.setGrnCode(grnMaster.getGrnCode());
		grnSummaryDTO.setUnicomGrnCode(grnMaster.getUnicomGrnCode());
		grnSummaryDTO.setGrnStatus(grnMaster.getGrnStatus());
		grnSummaryDTO.setPoId(grnMaster.getPoId());
		grnSummaryDTO.setCurrency(invoice != null ? invoice.getCurrency() : null);
		grnSummaryDTO.setVendorName(invoice != null ? invoice.getVendorName() : null);
		grnSummaryDTO.setVendorInvoiceNum(invoice != null ? invoice.getInvoiceId() : null);
		grnSummaryDTO.setVendor(invoice != null ? invoice.getVendor() : null);
		grnSummaryDTO.setInvoiceDate(invoice != null ? invoice.getInvoiceDate() : null);
		grnSummaryDTO.setVendorInvoiceDate(getFormattedInvoiceDate(invoice));
		grnSummaryDTO.setCreatedAt(grnMaster.getCreatedAt());
		grnSummaryDTO.setCreatedBy(grnMaster.getCreatedBy());
		grnSummaryDTO.setInvoiceId(grnMaster.getInvoiceId());
		
		grnSummaryDTO.setB2bInvoiceDate(b2bInvoiceDate);
		grnSummaryDTO.setBillOfEntryAmount(invoice != null ? invoice.getBillOfEntryAmount() : null);
		grnSummaryDTO.setBillOfEntryDate(billOfEntryDate);
		grnSummaryDTO.setBillOfEntryNumber(invoice != null ? invoice.getBillOfEntryNumber() : null);
		grnSummaryDTO.setHandoverParty(invoice != null ? invoice.getHandoverParty() : null);
		grnSummaryDTO.setSendToParty(invoice != null ? invoice.getSendToParty() : null);
		return grnSummaryDTO;
	}

	public static Map<String, PIDSummaryDTO> getPIDSummary(List<GRNPIDMaster> pids, Map<String, Map<String, Object>> grnPidCounts, Invoice invoice,
			GRNSummaryDTO grnSummaryDTO, Map<String, Map<String, Object>> pidMisc, Map<String, Timestamp> failedMap) {
		Map<String, PIDSummaryDTO> pidSummaryDTOMap = null;
		int totalFailed = 0;
		int totalScanned = 0;
		double totalFailedAmount = 0.0;
		double totalAmount = 0.0;
		double totalPriceWithTax = 0.0;
		double totalFailedPriceWithTax = 0.0;

		if (!CollectionUtils.isEmpty(pids)) {
			pidSummaryDTOMap = new HashMap<>();
			PIDSummaryDTO pidSummaryDTO = null;
			for (GRNPIDMaster pid : pids) {
				int pidFailCount = 0;
				int pidTotalCount = 0;
				int pidPassCount = 0;

				pidSummaryDTO = new PIDSummaryDTO();
				pidSummaryDTO.setPid(pid.getPid());
//				pidSummaryDTO.setEstimatedQuantity(pid.getEstimatedQuantity() == null ? 0 : pid.getEstimatedQuantity());
				pidSummaryDTO.setStatus(pid.getPidStatus());
				if (!CollectionUtils.isEmpty(grnPidCounts)) {
					Map<String, Object> pidCounts = grnPidCounts.getOrDefault(pid.getGrnCode(), null);
					Map<String, Object> pidCountMap = pidCounts != null ? (Map<String, Object>) pidCounts.getOrDefault(pid.getPid(), null) : null;
					pidFailCount = pidCountMap != null ? Integer.parseInt(pidCountMap.get("fail_count").toString()) : 0;
					pidTotalCount = pidCountMap != null ? Integer.parseInt(pidCountMap.get("total_count").toString()) : 0;
					pidPassCount = pidTotalCount - pidFailCount;
					pidSummaryDTO.setTotalFailed(pidFailCount);
					pidSummaryDTO.setTotalPassed(pidPassCount);
					pidSummaryDTO.setPriceWithTax(pid.getTaxRate());
					totalScanned = totalScanned + pidTotalCount;
					totalFailed = totalFailed + pidFailCount;
					totalAmount = totalAmount + pidTotalCount * pid.getPrice();
					totalPriceWithTax = totalPriceWithTax + pidTotalCount * pid.getTaxRate();
					totalFailedAmount = totalFailedAmount + pidFailCount * pid.getPrice();
					totalFailedPriceWithTax = totalFailedPriceWithTax + pidFailCount * pid.getTaxRate();
				}

				if (!CollectionUtils.isEmpty(pidMisc)) {
					Map<String, Object> map = pidMisc.getOrDefault(pid.getPid(), null);
					pidSummaryDTO.setLotNo(map != null ? (String) map.get("lot_no") : "NA");
//					pidSummaryDTO.setSamplingPercent(map != null ? Integer.parseInt(map.get("sampling_percent").toString()) : 0);
					pidSummaryDTO.setExpiryDate(map != null ? (Timestamp) map.get("expiry_date") : null);
				}

				if (!CollectionUtils.isEmpty(failedMap))
					pidSummaryDTO.setFailedOn(failedMap.get(pid.getPid()));

				if (invoice != null) {
					Product product = CommonUtils.getProductDetailsFromInvoice(invoice.getPids(), pid.getPid());
					if(product != null) {
						pidSummaryDTO.setPrice(pid.getPrice());
						pidSummaryDTO.setInvoiceQuantity(product.getQuantity());
						pidSummaryDTO.setDescription(product.getPidDescription());
						pidSummaryDTO.setVendorSku(product.getVendorSku());
					}
				}

				pidSummaryDTOMap.put(pid.getPid(), pidSummaryDTO);
			}
		}
		grnSummaryDTO.setTotalScanned(totalScanned);
		grnSummaryDTO.setTotalPassed(totalScanned - totalFailed);
		grnSummaryDTO.setTotalFailed(totalFailed);
		grnSummaryDTO.setTotalReceivedAmount(invoice.getTotalReceivedAmount());
		grnSummaryDTO.setTotalRejectedAmount(invoice.getTotalRejectedAmount());
		grnSummaryDTO.setTotalPriceWithTax(totalPriceWithTax);
		grnSummaryDTO.setTotalInvoiceQty(invoice.getTotalInvoiceQty());
		grnSummaryDTO.setOrderAcceptedQuantity(invoice.getOrderAcceptedQuantity());
		grnSummaryDTO.setOrderRejectedQuantity(invoice.getOrderRejectedQuantity());
		return pidSummaryDTOMap;
	}
	
	@Logging
	public static GRNPidDetailsDTO getGRNPIDInfo(GRNPIDMaster grnPid, QcConfig qcConfig, Map<String, Object> grnPidCounts, Map<String, VendorSTDResponseDTO> vendorConfig) {
		
		GRNPidDetailsDTO grnPIDInfo = new GRNPidDetailsDTO();
		grnPIDInfo.setBrand(grnPid.getBrand());
		grnPIDInfo.setCategoryId(grnPid.getCategoryId());
//		grnPIDInfo.setEstimatedQty(grnPid.getEstimatedQuantity());
		
		grnPIDInfo.setGrnCode(grnPid.getGrnCode());
		grnPIDInfo.setInvoiceRefNumber(grnPid.getInvoiceReferenceNum());
		grnPIDInfo.setPid(grnPid.getPid());
		grnPIDInfo.setPrice(grnPid.getPrice());
		grnPIDInfo.setStatus(grnPid.getPidStatus());
		grnPIDInfo.setTaxRate(grnPid.getTaxRate());
		grnPIDInfo.setVendor(grnPid.getVendor());
		grnPIDInfo.setPidDesc(grnPid.getPidDescription());
		
		Map<String, Object> pidCountMap = grnPidCounts != null ? (Map<String, Object>) grnPidCounts.getOrDefault(grnPid.getPid(), null) : null;
		Long minToScan = 0L;
		if(pidCountMap != null) {
			int scannedCount =
					Integer.parseInt(DBUtils.getOrDefault(pidCountMap.getOrDefault("total_count", 0), 0).toString());
			int failCount =
					Integer.parseInt(DBUtils.getOrDefault(pidCountMap.getOrDefault("fail_count", 0), 0).toString());
			int passCount = scannedCount - failCount;
//			minToScan = grnPIDInfo.getEstimatedQty();
			grnPIDInfo.setQcFailedCount(failCount);
			grnPIDInfo.setQcPassCount(passCount);
//			grnPIDInfo.setSamplingPercent((int) pidCountMap.getOrDefault("sampling_percent", 0));
			grnPIDInfo.setTotalScanned(scannedCount);
//			if (grnPIDInfo.getSamplingPercent() == 0 || grnPIDInfo.getSamplingPercent() == null) {
//				minToScan = 0L;
//			} else {
//				minToScan = (long) Math.ceil((minToScan * grnPIDInfo.getSamplingPercent()) / 100.0) - scannedCount;
//			}

		} else {
//			minToScan = (long) Math.ceil(qcConfig.getSamplingPercent() * grnPIDInfo.getEstimatedQty() / 100.0);
//			grnPIDInfo.setSamplingPercent(qcConfig.getSamplingPercent());
		}
		grnPIDInfo.setMinToScan(minToScan > 0 ? minToScan : 0);
		
		VendorSTDResponseDTO vendorSTDConfig = vendorConfig != null ? vendorConfig.getOrDefault(grnPid.getPid(), null) : null;
		if(vendorSTDConfig != null) {
			grnPIDInfo.setExpiryDateThreshold(vendorSTDConfig.getExpiryDateThreshold());
			grnPIDInfo.setExpiryFormat(vendorSTDConfig.getExpiryFormat());
			grnPIDInfo.setExpiryOffset(vendorSTDConfig.getExpiryOffset());
			grnPIDInfo.setIsBoxRequired(vendorSTDConfig.getIsBoxRequired());
			grnPIDInfo.setLotLength(vendorSTDConfig.getLotLength());
			grnPIDInfo.setLotOffset(vendorSTDConfig.getLotOffset());
		}
		
		return grnPIDInfo;
	}
	
	@Logging
	public static GRNAddPidResponseDTO getGRNAddPidResponseDTO(GRNMasterMetaDTO grnMasterMetaDTO, GRNMaster grnMaster, GRNPidDetailsDTO grnPidInfo, String status) {
		
		GRNAddPidResponseDTO response = new GRNAddPidResponseDTO();
		response.setGrnCode(grnMaster.getGrnCode());
		response.setGrnPidInfo(grnPidInfo);
		response.setGrnStatus(status);
		response.setInvoiceDate(grnMasterMetaDTO.getInvoice().getInvoiceDate());
		response.setInvoiceRefNum(grnMasterMetaDTO.getInvoice().getInvoiceRefNum());
		response.setMeta(grnMasterMetaDTO);
		response.setPoId(grnMasterMetaDTO.getInvoice().getPoId());
		response.setVendor(grnMasterMetaDTO.getInvoice().getVendor());
		
		return response;
	}
	
	@Logging
	public static GetGRNResponseDTO getGRNResponseDTO(GRNMasterMetaDTO grnMasterMetaDTO, GRNMaster grnMaster, List<GRNPidStatusDTO> pids, String status) {
		
		GetGRNResponseDTO response = new GetGRNResponseDTO();
		response.setGrnCode(grnMaster.getGrnCode());
		response.setVendorInvoiceNumber(grnMaster.getInvoice().getInvoiceId());
		response.setGrnPids(pids);
		response.setGrnStatus(status);
		response.setInvoiceDate(grnMasterMetaDTO.getInvoice().getInvoiceDate());
		response.setInvoiceRefNum(grnMasterMetaDTO.getInvoice().getInvoiceRefNum());
		response.setMeta(grnMasterMetaDTO);
		response.setPoId(grnMasterMetaDTO.getInvoice().getPoId());
		response.setVendor(grnMasterMetaDTO.getInvoice().getVendor());
		response.setType(grnMaster.getGrnType());
		response.setLegalOwner(grnMasterMetaDTO.getInvoice().getLegalOwner());
		response.setInvoiceLevel(grnMaster.getPo().getInvoiceLevel());
		log.info("[getGRNResponseDTO] GetGRNResponseDTO {}", response);
		return response;

	}

	@Logging
	public static GRNMaster convertDate(GRNMaster grnMaster) {
		DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		if(grnMaster != null && grnMaster.getInvoice() != null){
			grnMaster.getInvoice().setInvoiceDate(formatter.format(new Date(grnMaster.getInvoice().getInvoice_date())));
			grnMaster.getInvoice().setPoDate(formatter.format(new Date(grnMaster.getInvoice().getApprovedAt())));
		}
		return grnMaster;
	}

	@Logging
	public static void convertDate(GRNUpdateDTO grnUpdateDTO) {
		DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		if(grnUpdateDTO != null && grnUpdateDTO.getInvoice() != null){
			grnUpdateDTO.getInvoice().setInvoiceDate(formatter.format(new Date(grnUpdateDTO.getInvoice().getInvoice_date())));
			grnUpdateDTO.getInvoice().setPoDate(formatter.format(new Date(grnUpdateDTO.getInvoice().getApprovedAt())));
		}
	}
	
	public static List<GRNSearchResponseDTO> getGRNSearchResponse(List<GRNMasterDetails> grnList, Map<String,Map<String,Object>> grnScanCountMap) {

		List<GRNSearchResponseDTO> grnSearchResponse = new ArrayList<>();

		for (GRNMasterDetails grn : grnList) {
			
			if(GRN_STATUS_CLOSED.equalsIgnoreCase(grn.getGrnStatus()) && !grnScanCountMap.containsKey(grn.getGrnCode())) {
				continue;
			}
			GRNSearchResponseDTO grnDetails = new GRNSearchResponseDTO();
			grnDetails.setGrnCode(grn.getGrnCode());
			grnDetails.setGrnStatus(grn.getGrnStatus());
			grnDetails.setPoId(grn.getPoId());
			grnDetails.setInvoiceRefNum(grn.getInvoiceId());
			grnDetails.setVendor(grn.getInvoice().getVendor());
			grnDetails.setVendorName(grn.getInvoice().getVendorName());
			grnDetails.setVendorInvoiceNumber(grn.getInvoice().getInvoiceId());
			grnDetails.setCreatedBy(grn.getCreatedBy());
			grnDetails.setCreatedOn(grn.getCreatedAt());
			grnDetails.setTotalQty(0);
			grnDetails.setQcComplete(0);
			grnDetails.setAssignedTo(grn.getAssignedTo());

			if (grnScanCountMap.containsKey(grn.getGrnCode())) {
				grnDetails.setTotalQty((Integer) (grnScanCountMap.get(grn.getGrnCode()).get(TOTAL_SCANNED)));
				grnDetails.setQcComplete((Integer) (grnScanCountMap.get(grn.getGrnCode()).get(TOTAL_PASSED)));
			}

			grnSearchResponse.add(grnDetails);
		}


		return grnSearchResponse;
	}

    public static List<Box> getBoxes(List<BoxMapping> boxMappings, String facilityCode) {
		List<Box> boxes = new ArrayList<>();
		for(BoxMapping boxMapping : boxMappings) {
			Box box = new Box();
			box.setStatus(AVAILABLE);
			box.setEnabled(true);
			box.setFacilityCode(facilityCode);
			box.setBarcode(boxMapping.getBarcode());
			box.setCreatedBy(MDC.get("USER_ID"));
			box.setUpdatedBy(MDC.get("USER_ID"));
			boxes.add(box);
		}
		return boxes;
    }

	public static List<BoxMapping> getMappingsWithUpdatedBy(List<BoxMapping> emptyBoxMappings) {
		for(BoxMapping boxMapping : emptyBoxMappings)
			boxMapping.setCreatedBy(MDC.get("USER_ID"));
		return emptyBoxMappings;
	}

    public static GRNProductResponse getGRNProducts(GRNProductResponse response) {
		if(response.getInvoice() == null)
			return response;

		response.setVendorName(response.getInvoice().getVendorName());
		response.setInvoiceRefNum(response.getInvoice().getInvoiceRefNum());
		response.setCurrency(response.getInvoice().getCurrency());
		List<GRNProduct> products = new ArrayList<>();
		for(Product product : response.getInvoice().getPids()){
			if(response.getPids().containsKey(product.getPid())){
				GRNProduct grnProduct = response.getPids().get(product.getPid());
				grnProduct.setPending(product.getQuantity() - grnProduct.getReceived());
				grnProduct.setVendorSku(product.getVendorSku());
				grnProduct.setCategoryId(product.getCategoryId());
				grnProduct.setDescription(product.getPidDescription());
				if (response.getGrnStatus().equals(GRN_STATUS_CLOSED)) {
					if (grnProduct.getReceived() > 0)
						products.add(grnProduct);
				} else
					products.add(grnProduct);
			}
		}
		response.setProducts(products);
		return response;
    }

    public static GRNBlockedProduct getBlockedProductResponse(List<PidSearchResponse> result) {
		GRNBlockedProduct grnBlockedProduct = new GRNBlockedProduct();
		grnBlockedProduct.setGrnCode(result.get(0).getGrnCode());
		grnBlockedProduct.setGrnStatus(result.get(0).getGrnStatus());
		grnBlockedProduct.setCreatedOn(result.get(0).getCreatedOn());
		grnBlockedProduct.setVendorName(result.get(0).getVendorName());
		grnBlockedProduct.setPoId(result.get(0).getPoId());
		grnBlockedProduct.setProducts(result);
		return grnBlockedProduct;
    }

    @Logging
    public static boolean canThisPersonCloseGRN(HttpServletRequest request, String userId, String secretKey, String appName, String lkauthHeaderTokenName, boolean authMeValidation, String lkauthMeUrl) {
        log.info("Checking if this person can close the GRN: Person:{}", userId);
		log.info("authSecretKey : {} ---- lkauthHeaderTokenName : {} ---- authMeValidation : {} ---- lkauthMeUrl : {}",
				secretKey, lkauthHeaderTokenName, authMeValidation, lkauthMeUrl);
		AuthToken authToken = AuthUtil.fetchAuthTokenWithPermissionsFromRequest(request, lkauthHeaderTokenName, lkauthMeUrl, secretKey, authMeValidation);
        log.info("AuthToken of the current user is: {}", authToken);
        if (userId.equals(authToken.getUser().getEmpCode()))
            return true;
        log.info("Checking if the user has supervisor permission..");
        if (authToken.getPermissions() != null && authToken.getPermissions().getPermissionsMap() != null) {
            Map<String, Map<String, Set<String>>> permissionsMap = authToken.getPermissions().getPermissionsMap();
            if (permissionsMap.containsKey(appName) && isGRNCloseRolePresent(permissionsMap.get(appName)))
                return true;
            return false;
        }
        log.info("User doesn't have access to close this GRN");
        return false;
    }

    private static boolean isGRNCloseRolePresent(Map<String, Set<String>> facilityPermissionsMap) {
        if (facilityPermissionsMap == null)
            return false;
        for (Map.Entry<String, Set<String>> entry : facilityPermissionsMap.entrySet()) {
            if (entry.getValue().contains(GRNConstants.CAN_CLOSE_ANY_GRN))
                return true;
        }
        return false;
    }

    public static boolean canThisPersonCloseAllGRN(HttpServletRequest request, String secretKey, String appName, String lkauthHeaderTokenName, boolean authMeValidation, String lkauthMeUrl) {
		log.info("authSecretKey : {} ---- lkauthHeaderTokenName : {} ---- authMeValidation : {} ---- lkauthMeUrl : {}",
				secretKey, lkauthHeaderTokenName, authMeValidation, lkauthMeUrl);
		AuthToken authToken = AuthUtil.fetchAuthTokenWithPermissionsFromRequest(request, lkauthHeaderTokenName, lkauthMeUrl, secretKey, authMeValidation);
		log.info("AuthToken of the current user is: {}", authToken);
		log.info("Checking if the user has supervisor permission..");
		if (authToken.getPermissions() != null && authToken.getPermissions().getPermissionsMap() != null) {
			Map<String, Map<String, Set<String>>> permissionsMap = authToken.getPermissions().getPermissionsMap();
			if (permissionsMap.containsKey(appName) && isGRNCloseRolePresent(permissionsMap.get(appName)))
				return true;
			return false;
		}
		log.info("User doesn't have access to close this GRN");
		return false;
	}

	private static String getFormattedInvoiceDate(Invoice invoice) {
		if (invoice != null && invoice.getInvoiceDate() != null) {
			DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			return formatter.format(new Date(invoice.getInvoice_date()));
		}
		return null;
	}

}
