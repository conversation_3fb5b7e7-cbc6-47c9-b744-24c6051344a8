package com.lenskart.nexs.grn.connector.impl;

import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.grn.connector.WMSConnector;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

@Service
public class WMSConnectorImpl implements WMSConnector {

    @Autowired
    @Qualifier("wmsRestTemplate")
    private RestTemplate restTemplate;

    @CustomLogger
    private static Logger log;

    @Value("${wms.base.url:http://nexs-wms}")
    private String wmsHost;

    @Value("${mark.barcode.null.endpoint:/nexs/wms/api/v1/nullify/barcode}")
    private String markBarcodeNullEndpoint;

    @Override
    @Retryable(value = Exception.class, maxAttemptsExpression = "3",
            backoff = @Backoff(delayExpression = "1000"))
    public void markBarcodeNull(List<String> barcodeList) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        String url = wmsHost + markBarcodeNullEndpoint;
        HttpEntity<String> httpEntity = new HttpEntity(barcodeList, httpHeaders);
        log.info("[{}, markBarcodeNull], updated barcode list {} as null, url {}", this.getClass().getSimpleName(), barcodeList, url);
        ResponseEntity<BaseResponseModel> responseEntity = restTemplate.exchange(url,
                HttpMethod.PUT, httpEntity, BaseResponseModel.class, new Object[]{null});
        log.info("[{}, markBarcodeNull] The response entity for  is {}}", this.getClass().getSimpleName(), responseEntity);
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            BaseResponseModel baseResponseModel = responseEntity.getBody();
            boolean isBarcodeMarkedAsNull = (boolean) baseResponseModel.getData();
            log.info("[{}, markBarcodeNull], response from wms for making barcode as null :{}", this.getClass().getSimpleName(), isBarcodeMarkedAsNull);
            if (!isBarcodeMarkedAsNull) {
                throw new Exception("False response from wms for mark barcode null, barcodeList {}" + barcodeList);
            }
        } else {
            log.error("[{}, markBarcodeNull] failed to mark barcode null {} and received the response {}", this.getClass().getSimpleName(), barcodeList, responseEntity);
            throw new Exception("[" + this.getClass().getSimpleName() + ", markBarcodeNull] failed to uwItems for request " + barcodeList);
        }
    }
}