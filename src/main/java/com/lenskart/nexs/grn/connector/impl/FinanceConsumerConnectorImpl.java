package com.lenskart.nexs.grn.connector.impl;

import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.grn.connector.FinanceConsumerConnector;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Log4j2
@Service
public class FinanceConsumerConnectorImpl implements FinanceConsumerConnector {

    @Override
    public boolean sendGrnEventToFinanceService(String url, HttpMethod method, Object body) {
        log.info("[FinanceConsumerConnectorImpl] [sendGrnEventToFinanceService] calling api : {} with body {}",
                 url,
                 body);
        try {
            RestTemplate template = RestUtils.getRestTemplate(Duration.ofMillis(2000), Duration.ofMillis(2000));
            HttpHeaders header = new HttpHeaders();
            header.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> entity = new HttpEntity<>(body, header);
            ResponseEntity responseEntity = template.exchange(url, method,
                                                              entity,
                                                              Object.class);
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                log.info("sendGrnEventToFinanceService Successful ", responseEntity.getBody());
                return true;
            } else {
                log.error("[processPo] Unsuccessful", responseEntity.getBody());
                return false;
            }
        } catch (RuntimeException e) {
            log.error(
                    "[FinanceConsumerConnectorImpl] [sendGrnEventToFinanceService] RuntimeException occurred while making an API call:{} , Error: {} ",
                    url,
                    e.getMessage());
            return false;
        }
    }
}
