package com.lenskart.nexs.grn.connector.impl;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.connector.UnicomConnector;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GrnResponse;
import com.lenskart.nexs.grn.util.RetryUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UnicomConnectorImpl implements UnicomConnector {

    @CustomLogger
    private static Logger log;

    @Autowired
    private GRNConfig grnConfig;

    @Override
    public GrnResponse getUnicomeGrn(String facility, String grnCode) {
        log.info("[getUnicomeGrn] Fetching grn details for facility {} and grn code {}", facility, grnCode);
        try {
            String url = grnConfig.getUnicomBaseUrl() + grnConfig.getFetchGrnUrl() + facility + "/" + grnCode;
            log.info("[getUnicomeGrn] Fetching grn details for facility {} and grn code {}, url {}",
                    facility, grnCode, url);
            GrnResponse grnResponse = RetryUtils.getData(url, null, null, GrnResponse.class);
            log.info("[getUnicomeGrn] Fetching grn details for facility {} and grn code {}, grnResponse {}",
                    facility, grnCode, grnResponse);
            log.info("[getUnicomGrn] GrnResponse received {} and grnCode {} and facility {}", grnResponse, grnCode, facility);
            return grnResponse;
        } catch (Exception e) {
            log.error("Exception occurred while fetching grn from unicom for " + grnCode + "Exception " + e);
            throw new ApplicationException("Exception from unicom " + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
