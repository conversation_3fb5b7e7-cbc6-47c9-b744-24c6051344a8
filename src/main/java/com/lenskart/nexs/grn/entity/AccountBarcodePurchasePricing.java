package com.lenskart.nexs.grn.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class AccountBarcodePurchasePricing {

    @JsonProperty("barcode")
    private String barcode;

    @JsonProperty("vendor_name")
    private String vendorName;

    @JsonProperty("legal_owner")
    private String legalOwner;

    @JsonProperty("nexs_grn_code")
    private String nexsGrnCode;

    @JsonProperty("unicom_grn_code")
    private String unicomGrnCode;

    @JsonProperty("grn_date_dual")
    private LocalDate grnDateDual;

    @JsonProperty("unit_price_without_tax")
    private BigDecimal unitPriceWithoutTax;

    @JsonProperty("currency")
    private String currency;

    @JsonProperty("product_id")
    private Integer productId;

    @JsonProperty("invoice_ref_num")
    private String invoiceRefNum;

    @JsonProperty("grn_facility")
    private String grnFacility;

    @JsonProperty("po_id")
    private String poId;

    public AccountBarcodePurchasePricing(
            String barcode, String vendorName, String legalOwner,
            String nexsGrnCode, String unicomGrnCode, BigDecimal unitPriceWithoutTax,
            String currency, Integer productId, String invoiceRefNum, String grnFacility, String poId) {
        this.barcode = barcode;
        this.vendorName = vendorName;
        this.legalOwner = legalOwner;
        this.nexsGrnCode = nexsGrnCode;
        this.unicomGrnCode = unicomGrnCode;
        this.unitPriceWithoutTax = unitPriceWithoutTax;
        this.currency = currency;
        this.productId = productId;
        this.invoiceRefNum = invoiceRefNum;
        this.grnFacility = grnFacility;
        this.poId = poId;
    }
}
