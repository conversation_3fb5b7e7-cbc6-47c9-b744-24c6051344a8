package com.lenskart.nexs.grn.entity;

import com.lenskart.nexs.common.base.baseEntity.BaseEntity;
import com.nexs.po.common.enums.GrnImsSyncStatusEnum;
import com.nexs.po.common.enums.GrnPutawaySyncStatusEnum;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Table(
        name = "grn_items_old12feb2024"
)
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GrnItemsArchivedEntity extends BaseEntity {

    @Id
    @Column(
            name = "barcode",
            nullable = false,
            length = 20
    )
    private String barcode;
    @Column(
            name = "grn_code",
            nullable = false,
            length = 20
    )
    private String grnCode;
    @Column(
            name = "status",
            nullable = false,
            length = 20
    )
    private String status;
    @Column(
            name = "pid",
            nullable = false,
            length = 45
    )
    private String pid;
    @Column(
            name = "po_id",
            nullable = false,
            length = 20
    )
    private String poId;
    @Column(
            name = "invoice_ref_num",
            nullable = false,
            length = 20
    )
    private String invoiceRefNum;
    @Column(
            name = "vendor_code",
            nullable = false,
            length = 45
    )
    private String vendorCode;
    @Temporal(TemporalType.DATE)
    @Column(
            name = "expiry_date"
    )
    private Date expiryDate;
    @Column(
            name = "lot_no",
            length = 45
    )
    private String lotNo;
    @Column(
            name = "estimated_qty",
            nullable = false
    )
    private Long estimatedQty;
    @Column(
            name = "qc_pass_box_barcode",
            length = 20
    )
    private String qcPassBoxBarcode;
    @Column(
            name = "qc_status",
            length = 20
    )
    private String qcStatus = null;
    @Column(
            name = "qc_fail_code",
            length = 50
    )
    private String qcFailCode;
    @Column(
            name = "qc_reason",
            length = 200
    )
    private String qcReason;
    @Column(
            name = "qc_master_sampling",
            nullable = false
    )
    private Integer qcMasterSampling;
    @Column(
            name = "qc_gradient_sampling",
            nullable = false
    )
    private Integer qcGradientSampling;
    @Column(
            name = "failure_threshold_qc_master",
            nullable = false
    )
    private Integer failureThresholdQcMaster;
    @Column(
            name = "failure_threshold_qc_gradient",
            nullable = false
    )
    private Integer failureThresholdQcGradient;
    @Column(
            name = "facility",
            nullable = false,
            length = 50
    )
    private String facility;
    @Column(
            name = "channel_status",
            nullable = false
    )
    private Integer channelStatus;
    @Column(
            name = "ims_sync_status"
    )
    @Enumerated(EnumType.ORDINAL)
    private GrnImsSyncStatusEnum imsSyncStatus;
    @Column(
            name = "putaway_sync_status"
    )
    @Enumerated(EnumType.ORDINAL)
    private GrnPutawaySyncStatusEnum putawaySyncStatus;
    @Column(
            name = "putaway_code"
    )
    private Integer putawayCode;
}
