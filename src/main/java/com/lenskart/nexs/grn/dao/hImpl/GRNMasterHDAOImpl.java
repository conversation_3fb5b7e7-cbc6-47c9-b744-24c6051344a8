package com.lenskart.nexs.grn.dao.hImpl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.dbutil.CommonDbUtils;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnMasterEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnPidMasterEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.TemplateEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnPidMasterEntity;
import com.lenskart.nexs.common.entity.po.grn.TemplateEntity;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.exception.ValidationException;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.GRNSearchFields;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.GRNMasterDAO;
import com.lenskart.nexs.grn.dao.UserActivityDAO;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.GRNSearchDTO;
import com.lenskart.nexs.grn.dto.request.GRNUpdateDTO;
import com.lenskart.nexs.grn.dto.response.GRNDetailsDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.ExceptionConstants;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.service.PoInvoiceService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.sql.Connection;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Qualifier(QualifierConstants.JPA_GRN_MASTER_DAO)
@Transactional(rollbackFor = Exception.class)
public class GRNMasterHDAOImpl implements GRNMasterDAO, Queries, GRNConstants, GRNSearchFields {

    @CustomLogger
    private Logger log;

    @Autowired
    private GrnMasterEntityServiceImpl grnMasterEntityService;

    @Autowired
    @Qualifier(QualifierConstants.JPA_USER_ACTIVITY_DAO)
    private UserActivityDAO userActivityDAO;

    @Autowired
    private GRNConfig grnConfig;

    @Autowired
    private GrnPidMasterEntityServiceImpl grnPidMasterEntityService;

    @Autowired
    private GrnItemEntityServiceImpl grnItemEntityService;

    @Autowired
    private TemplateEntityServiceImpl  templateEntityService;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private PoInvoiceService poInvoiceService;

    @Logging
    @Override
    public boolean createGRN(GRNMaster grnMaster) throws Exception {
        log.info("Creating grn for grnMaster: {}", grnMaster);
        grnMasterEntityService.saveOrUpdate(createGRNMaster(grnMaster));
        log.info("grnMaster saved : {}", grnMaster);
        if(grnMaster.getGrnType().equals(TRANSFER_TYPE)){
            userActivityDAO.save(new UserActivity(TRANSFER_USER, CREATE, grnMaster.getFacility(),
                    grnMaster.getGrnCode(), TRANSFER_USER));
        } else {
            userActivityDAO.save(new UserActivity(MDC.get("USER_ID"), CREATE, grnMaster.getFacility(),
                    grnMaster.getGrnCode(), MDC.get("USER_ID")));
        }
        return true;
    }

    private GrnMasterEntity createGRNMaster(GRNMaster grnMaster) {
        log.info("[createGRNMaster] grnMaster {}", grnMaster);
        GrnMasterEntity grnMasterEntity = new GrnMasterEntity();
        grnMasterEntity.setGrnCode(grnMaster.getGrnCode());
        grnMasterEntity.setUnicomGrnCode(grnMaster.getUnicomGrnCode());
        grnMasterEntity.setPoId(grnMaster.getPoId());
        grnMasterEntity.setInvoiceId(grnMaster.getInvoiceId());
        grnMasterEntity.setGrnStatus(GRN_STATUS_CREATED);
        grnMasterEntity.setGrnSyncStatus(0);
        grnMasterEntity.setGrnSyncedToUnicom(0);
        grnMasterEntity.setGrnSyncedToNav(0);
        grnMasterEntity.setCreatedBy(grnMaster.getCreatedBy());
        grnMasterEntity.setUpdatedBy(grnMaster.getUpdatedBy());
        grnMasterEntity.setFacility(grnMaster.getFacility());
        grnMasterEntity.setGrnType(grnMaster.getGrnType());
//        grnMasterEntity.setBatchNo(grnMaster.getBatchNo());
        grnMasterEntity.setCurrencyConvRate(grnMaster.getCurrencyCovRate());
        log.info("[createGRNMaster] grnMasterEntity {}", grnMasterEntity);
        return grnMasterEntity;
    }

    @Logging
    @Override
    public boolean createGRNMaster(GRNMaster grnMaster, Connection connection) throws Exception {
        return true;
    }

    @Logging
    @Override
    public int updateGRN(GRNMaster grnMaster) {
        GrnMasterEntity grnMasterEntity =
                grnMasterEntityService.findByGrnCode(grnMaster.getGrnCode());
        if(grnMasterEntity == null){
            log.error("GRN not found {}", grnMaster.getGrnCode());
            throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
        updateGRNMaster(grnMaster, grnMasterEntity);
        grnMasterEntityService.saveOrUpdate(grnMasterEntity);
        userActivityDAO.save(new UserActivity(MDC.get("USER_ID"), UPDATE, grnMaster.getFacility(),
                grnMaster.getGrnCode(), MDC.get("USER_ID")));
        return 1;
    }

    private void updateGRNMaster(GRNMaster grnMaster, GrnMasterEntity grnMasterEntity) {
        grnMasterEntity.setGrnStatus(grnMaster.getGrnStatus());
        grnMasterEntity.setUpdatedBy(grnMaster.getUpdatedBy());
        grnMasterEntity.setUpdatedAt(grnMaster.getUpdatedAt());
        grnMasterEntity.setFacility(grnMaster.getFacility());
    }

    @Logging
    @Override
    public int updateGRNMaster(GRNMaster grnMaster, Connection connection) {
        return 0;
    }

    @Logging
    @Override
    public int editGRN(GRNUpdateDTO grnUpdateDTO) throws Exception {
        List<String> grnList = getGRNList(grnUpdateDTO.getInvoice().getInvoiceRefNum());
        if (CollectionUtils.isEmpty(grnList)) {
            throw new ApplicationException("No grn in the system for invoice : " +
                    grnUpdateDTO.getInvoice().getInvoiceRefNum(), GRNExceptionStatus.GRN_NOT_FOUND);
        }
        List<GrnMasterEntity> grnMasterEntity =
                grnMasterEntityService.findByInvoiceId(grnUpdateDTO.getInvoice().getInvoiceRefNum());
        int updateCount = editGRNMaster(grnUpdateDTO, grnMasterEntity);
        if (updateCount > 0) {
            for (String grn : grnList) {
                userActivityDAO.save(new UserActivity(MDC.get("USER_ID"), UPDATE, grnUpdateDTO.getFacilityCode(),
                        grn, MDC.get("USER_ID")));
            }
            List<GrnPidMasterEntity> grnPidMasterList =
                    grnPidMasterEntityService.findByInvoiceRefNum(grnUpdateDTO.getInvoice().getInvoiceRefNum());
            int updatePidMasterCount = updateGRNPidMaster(grnUpdateDTO, grnPidMasterList);
            if (updatePidMasterCount > 0) {
                List<GrnItemEntity> grnItemEntityList =
                        grnItemEntityService.findByInvoiceId(grnUpdateDTO.getInvoice().getInvoiceRefNum());
                updateGRNItem(grnUpdateDTO, grnItemEntityList);
            }
        }
        return updateCount;
    }

    private int editGRNMaster(GRNUpdateDTO grnUpdateDTO, List<GrnMasterEntity> grnMasterList) {
        grnMasterList.forEach(grnMaster -> {
            grnMaster.setPoId(grnUpdateDTO.getPo().getPoId());
            grnMaster.setUpdatedBy(grnUpdateDTO.getUpdatedBy());
            grnMaster.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        });
        return 1;
    }

    private List<String> getGRNList(String invoiceRefNum) {
        List<String> collect =
                grnMasterEntityService.findByInvoiceId(invoiceRefNum).stream().map(GrnMasterEntity::getGrnCode).collect(Collectors.toList());
        return collect;
    }

    private int updateGRNItem(GRNUpdateDTO grnUpdateDTO, List<GrnItemEntity> grnItemEntities) {
        grnItemEntities.forEach(grnItem -> {
            grnItem.setPoId(grnUpdateDTO.getPo().getPoId());
            grnItem.setVendorCode(grnUpdateDTO.getInvoice().getVendor());
            grnItem.setUpdatedBy(grnUpdateDTO.getUpdatedBy());
            grnItem.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        });
        return 1;
    }

    private int updateGRNItem(GRNUpdateDTO grnUpdateDTO, Connection con) {
        return 1;
    }

    private int updateGRNPidMaster(GRNUpdateDTO grnUpdateDTO, List<GrnPidMasterEntity> grnPidMasterList) {
        grnPidMasterList.forEach(grnPidMaster -> {
            grnPidMaster.setInvoiceId(grnUpdateDTO.getInvoice().getInvoiceId());
            grnPidMaster.setVendor(grnUpdateDTO.getInvoice().getVendor());
            grnPidMaster.setUpdatedBy(grnUpdateDTO.getUpdatedBy());
            grnPidMaster.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        });
        return 1;
    }

    private int updateGRNPidMaster(GRNUpdateDTO grnUpdateDTO, Connection con) {
        return 1;
    }

    @Logging
    public List<String> getGRNList(String invoiceRefNum, Connection con) {
        return null;
    }

    @Logging
    @Override
    public int editGRNMaster(GRNUpdateDTO grnUpdateDTO, Connection connection) throws Exception {
        return 0;
    }

    @Logging
    @Override
    public GRNMaster getGRNMaster(String grnCode, String userId) throws Exception {
        try {
            Query nativeQuery = entityManager.createNativeQuery(GET_GRN_MASTER,Tuple.class);
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            GRNMaster grnMaster = null;
            try {
                List resultList = nativeQuery.getResultList();
                log.info("getGRNMaster resultList:{}", resultList);
                Tuple tuple = (Tuple) resultList.get(0);
                log.info("getGRNMaster tuple:{}", tuple);
                //CommonDbUtils.convertToMap(tuple, GRNDetailsDTO.class);
                grnMaster = resultSetToMaster(tuple);
            } catch (Exception ex) {
                throw new ApplicationException("resultSetToMaster Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return grnMaster;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
//        return getGRNMaster(grnCode);
    }

    public GRNMaster resultSetToMaster(Tuple tuple) throws Exception, JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        GRNMaster grnMaster = new GRNMaster();
        boolean recordFound = false;
        if (Objects.nonNull(tuple)) {
            recordFound = true;
            grnMaster.setGrnCode(tuple.get("grn_code", String.class));
            grnMaster.setInvoice(poInvoiceService.getInvoiceDetails(tuple.get("invoice_id", String.class)));
            grnMaster.setPo(poInvoiceService.getPurchaseOrderDetails(tuple.get("po_id", String.class)));
            grnMaster.setPoId(tuple.get("po_id", String.class));
            grnMaster.setInvoiceId(tuple.get("invoice_id", String.class));
            grnMaster.setUnicomGrnCode(tuple.get("unicom_grn_code", String.class));
            grnMaster.setGrnStatus(tuple.get("grn_status", String.class));
            grnMaster.setFacility(tuple.get("facility", String.class));
            grnMaster.setCreatedBy(tuple.get("created_by", String.class));
            grnMaster.setGrnType(tuple.get("grn_type", String.class));
        }
        return recordFound ? grnMaster : null;
    }

    @Logging
    @Override
    public GRNMaster getGRNMasterSummary(String grnCode, String userId) throws Exception {
        try {
            Query nativeQuery = entityManager.createNativeQuery(GET_GRN_SUMMARY,Tuple.class);

            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            GRNMaster grnMaster = null;
            try {
                List<Tuple> tuples = nativeQuery.getResultList();
                grnMaster = resultSetToMasterSummary(tuples);
            } catch (Exception ex) {
                throw new ApplicationException("resultSetToMasterSummary Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return grnMaster;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        //return getGRNMaster(grnCode);
    }

    public GRNMaster resultSetToMasterSummary(List<Tuple> tuples) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        GRNMaster grnMaster = new GRNMaster();
        boolean recordFound = false;
        for (Tuple tuple:tuples) {
            if (Objects.nonNull(tuple)) {
                recordFound = true;
                grnMaster.setGrnCode(tuple.get("grn_code", String.class));
                grnMaster.setUnicomGrnCode(tuple.get("unicom_grn_code", String.class));
                grnMaster.setGrnStatus(tuple.get("grn_status", String.class));
                grnMaster.setPoId(tuple.get("po_id", String.class));
                grnMaster.setCreatedAt(tuple.get("created_at", Timestamp.class));
                grnMaster.setCreatedBy(tuple.get("created_by", String.class));
                grnMaster.setInvoiceId(tuple.get("invoice_id", String.class));
                grnMaster.setInvoice(poInvoiceService.getInvoiceDetails(tuple.get("invoice_id", String.class)));
            }
        }
        return recordFound ? grnMaster : null;
    }

    @Logging
    @Override
    @Transactional
    public int closeGRN(String grnCode, String userId, String facilityCode) {
        try {
            int updateCount = closeGRNMaster(grnCode, userId);
            if (updateCount > 0)
                userActivityDAO.save(new UserActivity(userId, CLOSE, facilityCode, grnCode, userId));
        } catch (Exception ex) {
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return 1;
    }

    private int closeGRNMaster(String grnCode, String userId) {
        GrnMasterEntity grnMasterEntity =
                grnMasterEntityService.findByGrnCode(grnCode);
        if(grnMasterEntity == null){
            log.error("GRN not found {}", grnCode);
            throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
        grnMasterEntity.setGrnStatus(GRN_STATUS_CLOSED);
        grnMasterEntity.setUpdatedBy(userId);
        grnMasterEntity.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        grnMasterEntityService.saveOrUpdate(grnMasterEntity);
        return 1;
    }

    @Logging
    @Override
    public int closeGRNMaster(String grnCode, String userId, Connection connection) {
        return 0;
    }

//    @Logging
//    @Override
//    public boolean updateEstimatedQty(String grnCode, List<PIDEstimatedQty> estimatedQty,
//                                      List<PIDEstimatedQtyHistory> estimatedQtyList) throws Exception {
//        Optional<GrnMasterEntity> grnMasterEntities = grnMasterEntityService.getDao().findById(grnCode);
//        GrnMasterEntity grnMasterEntity =
//                grnMasterEntities.orElseThrow(() -> new ApplicationException(ExceptionConstants.GRN_NOT_FOUND,
//                        GRNExceptionStatus.GRN_INTERNAL_EXCEPTION));
//        return true;
//    }

    @Logging
    @Override
    public GRNMaster getGRNMaster(String grnCode) throws Exception {
        try {
            GrnMasterEntity grnMasterEntity =
                    grnMasterEntityService.findByGrnCode(grnCode);
            if(grnMasterEntity == null){
                log.error("GRN not found {}", grnCode);
                throw new ValidationException(ExceptionConstants.GRN_NOT_FOUND);
            }
            GRNMaster grnMaster = new GRNMaster();
            grnMaster = convertToGrnMaster(grnMasterEntity);
            return grnMaster;
        } catch (Exception ex) {
            throw new ValidationException("SQL Exception : " + ex.getMessage());
        }
    }

    private GRNMaster convertToGrnMaster(GrnMasterEntity grnMasterEntity) {
        GRNMaster grnMaster = new GRNMaster();
        try {
            BeanUtils.copyProperties(grnMasterEntity, grnMaster);
            log.info("[convertToGrnMaster] grnMasterEntity {}", grnMasterEntity);
            if (grnMasterEntity.getInvoiceId() != null && !grnMasterEntity.getInvoiceId().isEmpty())
            	grnMaster.setInvoice(poInvoiceService.getInvoiceDetails(grnMasterEntity.getInvoiceId()));
            if (grnMasterEntity.getPoId() != null && !grnMasterEntity.getPoId().isEmpty())
            	grnMaster.setPo(poInvoiceService.getPurchaseOrderDetails(grnMasterEntity.getPoId()));
            grnMaster.setGrnCode(grnMasterEntity.getGrnCode());
            grnMaster.setCreatedAt(new Timestamp(grnMasterEntity.getCreatedAt().getTime()));
            grnMaster.setUpdatedAt(new Timestamp(grnMasterEntity.getUpdatedAt().getTime()));
            log.info("[convertToGrnMaster] grnMaster {}", grnMaster);
        } catch (Exception e) {
            log.error("[convertToGrnMaster] getGRNMaster data error", e);
        }
        return grnMaster;
    }
    
    private GRNMaster convertTupleToGrnMaster(Tuple tuple) {
        GRNMaster grnMaster = new GRNMaster();
        try {
            grnMaster.setGrnCode(tuple.get("grn_code", String.class));
            grnMaster.setGrnStatus(tuple.get("grn_status", String.class));
            grnMaster.setInvoiceId(tuple.get("invoice_id", String.class));
            grnMaster.setPoId(tuple.get("po_id", String.class));
            grnMaster.setCreatedBy(tuple.get("created_by", String.class));
            grnMaster.setCreatedAt(tuple.get("created_at", Timestamp.class));
        } catch (Exception e) {
            log.error("convertTupleToGrnMaster data error", e);
        }
        return grnMaster;
    }

    @Logging
    @Override
    public GRNMaster getGRNMasterNP(String grnCode, String userId) throws Exception {
        try {
            Query nativeQuery = entityManager.createNativeQuery(GET_ONLY_GRN_MASTER,Tuple.class);
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            nativeQuery.setParameter(i++, grnCode);
            GRNMaster grnMaster = null;
            try {
                Tuple tuple = (Tuple) nativeQuery.getSingleResult();
                grnMaster = resultSetToMasterNP(tuple);
            } catch (Exception ex) {
                log.error("getGRNMasterNP Exception : " + ex.getMessage(), ex);
                throw new ApplicationException("getGRNMasterNP Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return grnMaster;
        } catch (Exception ex) {
            log.error("getGRNMasterNP Exception : " + ex.getMessage(), ex);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public GRNMaster resultSetToMasterNP(Tuple tuple) throws Exception, JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        GRNMaster grnMaster = new GRNMaster();
        boolean recordFound = false;
        if (Objects.nonNull(tuple)) {
            recordFound = true;
            grnMaster.setGrnCode(tuple.get("grn_code", String.class));
            grnMaster.setInvoice(poInvoiceService.getInvoiceDetails(tuple.get("invoice_id", String.class)));
            grnMaster.setPo(poInvoiceService.getPurchaseOrderDetails(tuple.get("po_id", String.class)));
            grnMaster.setPoId(tuple.get("po_id", String.class));
            grnMaster.setInvoiceId(tuple.get("invoice_id", String.class));
            grnMaster.setUnicomGrnCode(tuple.get("unicom_grn_code", String.class));
            grnMaster.setGrnStatus(tuple.get("grn_status", String.class));
        }
        return recordFound ? grnMaster : null;
    }

    @Logging
    @Override
    public List<GRNMaster> getGRNByInvoiceAndUser(String invoiceId, String user, boolean isSupervisor) throws Exception {
        List<GRNMaster> grns = new ArrayList<>();
        try {
            List<Tuple> grnMasterEntities = grnMasterEntityService.findByInvoiceIdForSummary(invoiceId);
            grnMasterEntities.forEach(grnMasterEntity -> grns.add(convertTupleToGrnMaster(grnMasterEntity)));
        } catch (Exception ex) {
            throw new ApplicationException("getGRNByInvoiceAndUser Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return grns;
    }

    @Logging
    @Override
    public List<GRNDetailsDTO> getGRNLISTByInvoiceAndUser(String invoiceId, String user) {
        List<GRNDetailsDTO> grns = new ArrayList<>();
        try {
            List<Tuple> tupleList =
                    grnMasterEntityService.getGrnListByInvoiceIdAndGrnStatusNot(invoiceId, GRN_STATUS_CLOSED);
            tupleList.forEach(tuple -> {
                grns.add(CommonDbUtils.convertToMap(tuple, GRNDetailsDTO.class));
            });
        } catch (Exception ex) {
            throw new ApplicationException("getGRNLISTByInvoiceAndUser Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return grns;
    }

    @Logging
    @Override
    public List<GRNDetailsDTO> getGRNByInvoiceId(String invoiceId) {
        List<GRNDetailsDTO> grns = new ArrayList<>();
        try {
            List<Tuple> tupleList =
                    grnMasterEntityService.getGrnByInvoiceIdAndGrnStatusNot(invoiceId, GRN_STATUS_CLOSED);
            tupleList.forEach(tuple -> {
                grns.add(CommonDbUtils.convertToMap(tuple, GRNDetailsDTO.class));
            });
        } catch (Exception ex) {
            throw new ApplicationException("getGRNLISTByInvoiceAndUser Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return grns;
    }

    private GRNDetailsDTO convertToGrnDetailsDTO(GrnMasterEntity grnMaster) {
        GRNDetailsDTO grnDetailsDTO = new GRNDetailsDTO();
        grnDetailsDTO.setInvoiceReferenceNum(grnMaster.getInvoiceId());
        BeanUtils.copyProperties(grnMaster, grnDetailsDTO);
        return grnDetailsDTO;
    }

    @Logging
    @Override
    public int getNotClosedGRNCount(String invoiceId) {
        try {
            List<GrnMasterEntity> grnMasterEntities = grnMasterEntityService.findByInvoiceIdAndGrnStatusNot(invoiceId,
                    GRN_STATUS_CLOSED);
            return grnMasterEntities.size();
        } catch (Exception ex) {
            throw new ApplicationException("getNotClosedGRNCount Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }


    @Logging
    @Override
    public List<GRNMasterDetails> searchGRN(GRNSearchDTO grnSearchReq, boolean isExportReq) throws JsonMappingException, JsonProcessingException {

        List<GRNMasterDetails> grns = new ArrayList<>();
        StringBuilder query = new StringBuilder();
        query.append(SEARCH_GRN);
        String criteria = buildGRNSearchCriteria(grnSearchReq);
        if (StringUtils.isNotBlank(criteria)) {
            query.append(" and " + criteria);
        }

        query.append(" group by gm.grn_code order by created_at DESC");

        Integer pageSize = grnConfig.getPageSize();
        if (isExportReq) {
            pageSize = grnConfig.getExportCSVSize();
        }
        Integer offset = grnSearchReq.getPage() * pageSize;
        query.append(" limit " + pageSize + " offset " + offset);
        try {
            grns = entityManager.createNativeQuery(query.toString(),
                    GRNMasterDetails.class).getResultList();
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return grns;
    }

    @Logging
    @Override
    public int countGRNListing(GRNSearchDTO grnSearchReq) {
        StringBuilder query = new StringBuilder();
        query.append(COUNT_GRN_LISTING);
        String criteria = buildGRNSearchCriteria(grnSearchReq);
        if (StringUtils.isNotBlank(criteria)) {
            query.append(" where " + criteria);
        }
        try {
            List<Object[]> objects = entityManager.createNativeQuery(query.toString()).getResultList();
            return (int) objects.get(0)[0];
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }

    }

    @Logging
    @Override
    public Map<String, Boolean> checkClosedGRNByInvAndPid(String invoiceRefNum, List<String> pids) {

        Map<String, Boolean> result = new HashMap<>();
        StringBuilder query = new StringBuilder();
        query.append(CHECK_CLOSED_GRN_BY_INV_PID);
        for (String pid : pids) {
            if (StringUtils.isNotBlank(pid)) {
                query.append("?,");
            }
        }
        if (query.length() > 0) {
            query.deleteCharAt(query.lastIndexOf(","));
        }
        query.append(") group by gp.pid");
        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString());
            int i = 1;
            nativeQuery.setParameter(i++, invoiceRefNum);
            for (String pid : pids) {
                if (StringUtils.isNotBlank(pid)) {
                    nativeQuery.setParameter(i++, pid);
                }
            }
            result.put("isGRNOpen", false);
            List<Object[]> objects = nativeQuery.getResultList();
            try {
                if (!CollectionUtils.isEmpty(objects))
                    result.put("isGRNOpen", true);
                for (Object[] rs: objects) {
                    result.put(String.valueOf(rs[0]), true);
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return result;
    }

    @Override
    public String getTemplate(String templateId, int version) {
        try {
            TemplateEntity templateEntity =
                    templateEntityService.findByIdTemplateIdAndIdVersion(templateId, version);
            return Objects.nonNull(templateEntity) ? templateEntity.getTemplate() : "";
        } catch (Exception ex) {
            throw new ApplicationException("Exception Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private String buildGRNSearchCriteria(GRNSearchDTO grnSearchReq) {

        StringBuilder build = new StringBuilder();

        if (StringUtils.isNotBlank(grnSearchReq.getGrnCode())) {
            build.append("gm." + GRN_CODE + " = " + "\"" + grnSearchReq.getGrnCode() + "\"");
            return build.toString();
        }

        build.append("gm." + FACILITY + " = " + "\"" + grnSearchReq.getFacility() + "\"");

        if (StringUtils.isNotBlank(grnSearchReq.getGrnStatus())) {
            build.append(" and ");
            build.append("gm." + GRN_STATUS + " = " + "\"" + grnSearchReq.getGrnStatus() + "\"");
        }

        if (StringUtils.isNotBlank(grnSearchReq.getPo())) {
            build.append(" and ");
            build.append("gm." + PO + " = " + "\"" + grnSearchReq.getPo() + "\"");
        }

        if (StringUtils.isNotBlank(grnSearchReq.getInvoiceRefNumber())) {
            build.append(" and ");
            build.append("gm." + INVOICE_REF_NUM + " = " + "\"" + grnSearchReq.getInvoiceRefNumber() + "\"");
        }

        if (StringUtils.isNotBlank(grnSearchReq.getVendor())) {
            build.append(" and ");
            build.append("gp." + VENDOR + " = " + "\"" + grnSearchReq.getVendor() + "\"");
        }

        if (StringUtils.isNotBlank(grnSearchReq.getVendorInvoice())) {
            build.append(" and ");
            build.append("gp." + INVOICE_ID + " = " + "\"" + grnSearchReq.getVendorInvoice() + "\"");
        }

        if (StringUtils.isNotBlank(grnSearchReq.getCreatedBy())) {
            build.append(" and ");
            build.append("gm." + CREATED_BY + " = " + "\"" + grnSearchReq.getCreatedBy() + "\"");
        }

        return build.toString();
    }

    @Override
    public List<GRNMaster> getClosedGRNs(String invoiceRefNum, String facility) {
        try {
            List<GrnMasterEntity> grnMasterEntities =
                    grnMasterEntityService.findByInvoiceIdAndGrnStatusAndFacility(invoiceRefNum, GRN_STATUS_CLOSED,
                            facility);
            List<GRNMaster> list = new ArrayList<>();
                grnMasterEntities.forEach(grnMaster -> list.add(convertToGrnMaster(grnMaster)));
            return list;
        } catch (Exception ex) {
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public List<GrnMasterEntity> getOpenGRNListByInvoice(String invoiceRefNum, String facility) {
        try {
            List<GrnMasterEntity> grnMasterEntities =
                    grnMasterEntityService.findByInvoiceIdAndGrnStatusNotAndFacility(invoiceRefNum, GRN_STATUS_CLOSED,
                            facility);
            return grnMasterEntities;
        } catch (Exception ex) {
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public List<String> getGRNByPOAndUser(String poNum) {
        try {
            List<GrnMasterEntity> grnMasterEntities =
                    grnMasterEntityService.findByPoId(poNum);
            return grnMasterEntities.stream().map(GrnMasterEntity::getGrnCode).collect(Collectors.toList());
        } catch (Exception ex) {
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public String getGrnByInvoice(String invoiceRefNumber) {
        try {
            List<GrnMasterEntity> grnMasterEntities =
                    grnMasterEntityService.findByInvoiceId(invoiceRefNumber);
            List<String> list = grnMasterEntities.stream().map(GrnMasterEntity::getGrnCode).collect(Collectors.toList());
            return String.join(",", list);
        } catch (Exception ex) {
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public Invoice getInvoiceById(String invoiceId) {
        List<GrnMasterEntity> grnMasterList = grnMasterEntityService.findByInvoiceId(invoiceId);
        if(!CollectionUtils.isEmpty(grnMasterList)){
            GRNMaster grnMaster = convertToGrnMaster(grnMasterList.get(0));
            return grnMaster.getInvoice();
        }
        return null;
    }

    @Override
    public List<UnicomUnsyncedGrnCodeDetails> getUnsyncedGrn(String fetchQuery) {
        log.info("[getUnsyncedGrn] Fetching unsynced grns to unicom");
        try {
            List<UnicomUnsyncedGrnCodeDetails> details = new ArrayList<>();
            Query nativeQuery = entityManager.createNativeQuery(fetchQuery, Tuple.class);
            List<Tuple> resultList = nativeQuery.getResultList();
            log.info("[getUnsyncedGrn] Fetched unsynced grns to unicom resultList: {}", resultList);

            try {
                for (Tuple tuple : resultList) {
                    UnicomUnsyncedGrnCodeDetails detail = new UnicomUnsyncedGrnCodeDetails();
                    detail.setGrnCode(tuple.get("grn_code", String.class));
                    detail.setGrnType(tuple.get("grn_type", String.class));
                    detail.setFacility(tuple.get("facility", String.class));
                    log.info("[getUnsyncedGrn] Fetched unsynced grns to unicom: {}", detail);
                    details.add(detail);
                }
                log.info("[getUnsyncedGrn] Fetched unsynced grns to unicom details: {}", details);
                return details;
            } catch (Exception ex) {
                log.error("[getUnsyncedGrn] SQL Exception : Error while fetching unsynced grns to unicom: {}", ex.getMessage());
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            log.error("[getUnsyncedGrn] SQL Exception : Error while fetching unsynced grns to unicom: {}", ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

}
