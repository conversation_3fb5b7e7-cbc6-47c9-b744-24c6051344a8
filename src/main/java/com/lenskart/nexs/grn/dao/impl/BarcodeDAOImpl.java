package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.dao.BarcodeDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.Barcode;

import lombok.extern.slf4j.Slf4j;

@Primary
@Component(QualifierConstants.JDBC_BARCODE_DAO)
@Slf4j
@Deprecated
public class BarcodeDAOImpl implements BarcodeDAO, Queries {

    @Autowired
    private DBConfig dbConfig;

    @Override
    @Logging
    public List<Barcode> getBarcode(boolean nexs) {
        String query = nexs ? GET_NEXS_BARCODE : GET_NON_NEXS_BARCODE;
        try (Connection con = dbConfig.getDataSourceInventory().getConnection();
             PreparedStatement pst = con.prepareStatement(query);)
        {
            List<Barcode> barcodeList = new ArrayList<>();
            try (ResultSet rs = pst.executeQuery();) {
                while(rs.next()) {
                    Barcode barcode = new Barcode(rs.getString("from_series"), rs.getString("to_series"));
                    barcodeList.add(barcode);
                }
                return barcodeList;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public int getBarcodeCount(String barcode) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_BARCODE_COUNT);)
        {
            int i = 1;
            pst.setString(1, barcode);
            try (ResultSet rs = pst.executeQuery();) {
                if(rs.next())
                    return rs.getInt("COUNT(1)");
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return 0;
    }

    @Override
    public List<Barcode> getBarcodeSeries() {
        try (Connection con = dbConfig.getDataSourceInventory().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_BARCODE_SERIES);)
        {
            List<Barcode> barcodeList = new ArrayList<>();
            try (ResultSet rs = pst.executeQuery();) {
                while(rs.next()) {
                    Barcode barcode = new Barcode(rs.getString("from_series"),
                            rs.getString("to_series"), rs.getInt("is_box"));
                    barcodeList.add(barcode);
                }
                return barcodeList;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
