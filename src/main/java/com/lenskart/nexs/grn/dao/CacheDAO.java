package com.lenskart.nexs.grn.dao;

import com.lenskart.nexs.grn.dto.response.ASNResponseDTO;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.model.QcConfig;

public interface CacheDAO {

//    public QcConfig getQcMaster(GRNItem grnItem, boolean invoice);

    public void loadConfig(GRNItem grnItem, boolean invoice);

//    public QcConfig getInvoiceQcConfig(String invoiceRefNum, String pid, String grnCode);

//    public QcConfig getGrnQcConfig(GRNItem grnItem);

//    public boolean setNextGradientLevel(GRNItem grnItem);

    public void setConfigByEstimatedQty(GRNItem grnItem, QcConfig qcConfig, int totalFailed, int totalScanned);

	void initializeKeys(String grnCode, String pid);

//    public QcConfig getConfig(GRNItem grnItem, boolean invoice);

//    public void setInvoiceConfig(String invoiceRefNum, String pid, QcConfig oldInvoiceConfig);

//    public void setGrnConfig(String grnCode, String pid, QcConfig oldGrnConfig);

    public ASNResponseDTO getASN(String poId, String vendorInvoiceNum, String barcode);

	void setBoxRequired(String grnCode, String pid);

	boolean isBoxRequired(String grnCode, String pid);

	public void wait(String invoiceRefNum, String pid, String grnCode, String methodName);

	public void signal(String invoiceRefNum, String pid, String grnCode, String methodName);

	public String getLockOwnerGRN(String invoiceRefNum, String pid);

	public boolean hasLockKey(String invoiceRefNum, String pid);

    void initializeKeys(GRNItem grnItem, int totalFailed, int totalScanned);
}
