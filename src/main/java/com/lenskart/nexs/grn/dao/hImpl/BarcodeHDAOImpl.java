package com.lenskart.nexs.grn.dao.hImpl;

import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.BarcodeDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.Barcode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Service(QualifierConstants.JPA_BARCODE_DAO)
@Transactional(rollbackFor = Exception.class)
public class BarcodeHDAOImpl implements BarcodeDAO, Queries {

    @Autowired
    private DBConfig dbConfig;

    @Autowired
    private GrnItemEntityServiceImpl grnItemEntityService;

    @Override
    @Logging
    public List<Barcode> getBarcode(boolean nexs) {
        String query = nexs ? GET_NEXS_BARCODE : GET_NON_NEXS_BARCODE;
        try (Connection con = dbConfig.getDataSourceInventory().getConnection();
             PreparedStatement pst = con.prepareStatement(query);)
        {
            List<Barcode> barcodeList = new ArrayList<>();
            try (ResultSet rs = pst.executeQuery();) {
                while(rs.next()) {
                    Barcode barcode = new Barcode(rs.getString("from_series"), rs.getString("to_series"));
                    barcodeList.add(barcode);
                }
                return barcodeList;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public int getBarcodeCount(String barcode) {
        try {
            return grnItemEntityService.countById(barcode);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public List<Barcode> getBarcodeSeries() {
        try (Connection con = dbConfig.getDataSourceInventory().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_BARCODE_SERIES);)
        {
            List<Barcode> barcodeList = new ArrayList<>();
            try (ResultSet rs = pst.executeQuery();) {
                while(rs.next()) {
                    Barcode barcode = new Barcode(rs.getString("from_series"),
                            rs.getString("to_series"), rs.getInt("is_box"));
                    barcodeList.add(barcode);
                }
                return barcodeList;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
