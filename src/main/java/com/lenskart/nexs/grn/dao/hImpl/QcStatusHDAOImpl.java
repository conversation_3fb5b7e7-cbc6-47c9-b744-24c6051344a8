package com.lenskart.nexs.grn.dao.hImpl;

import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.QcStatusDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.GRNPidListDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.PidItemCounter;
import com.lenskart.nexs.grn.util.CacheUtils;
import com.lenskart.nexs.service.RedisHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service(QualifierConstants.JPA_QC_STATUS_DAO)
@Transactional(rollbackFor = Exception.class)
public class QcStatusHDAOImpl implements QcStatusDAO, GRNConstants {
    @Autowired
    private GRNConfig grnConfig;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private GrnItemEntityServiceImpl grnItemEntityService;


    @Value("${nexs.grn.item.scan.count.ttl}")
    private long itemScanCountKeyTTL;

    @Override
    public boolean hasReferenceKey(String refId, String pid) {
        return RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(), refId, pid));
    }

    @Override
    public boolean hasGrnKey(String grnCode, String pid) {
        return RedisHandler.hasKey(CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), grnCode, pid));
    }

    @Override
    @Logging
    public long incrementScanCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementScanCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long incrementItemQtyCount(String itemId, String pid) {
        return incrementItemQtyCount(itemId, pid, false);
    }

    @Override
    @Logging
    public long incrementItemQtyCount(String itemId, String pid, boolean poCount) {
        try {
            String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), itemId, pid);
            if (!RedisHandler.hasKey(cacheKey)) {
                long countFromDB = getCountFromDB(itemId, pid, poCount);
                log.info("counter from db for key {} : {}", cacheKey, countFromDB++);
                RedisHandler.redisOps(RedisOps.SETIFABSENT, cacheKey, countFromDB, itemScanCountKeyTTL, TimeUnit.HOURS);
            }
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, cacheKey).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementItemQtyCount(String itemId, String pid) {
        return decrementItemQtyCount(itemId, pid, false);
    }

    @Override
    @Logging
    public long decrementItemQtyCount(String itemId, String pid, boolean poCount) {
        try {
            String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), itemId, pid);
            if (RedisHandler.hasKey(cacheKey)) {
                RedisHandler.redisOps(RedisOps.DEL, cacheKey);
                log.info("counter deleted from redis for key {} : {}", cacheKey);
            }
            return getCountFromDB(itemId, pid, poCount);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void resetInvoiceQtyCount(PidItemCounter invoicePidCounter, Map<String, Long> counterMap) {
        try {
            String invoiceScanCountKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(),
                    invoicePidCounter.getInvoiceRefNum(), invoicePidCounter.getPid());
            RedisHandler.redisOps(RedisOps.SETVALUETTL, invoiceScanCountKey, invoicePidCounter.getCount(), itemScanCountKeyTTL, TimeUnit.HOURS);
            counterMap.put(invoiceScanCountKey, invoicePidCounter.getCount());
            log.info("Counter updated for invoice {} and pid {} to : {}", invoicePidCounter.getInvoiceRefNum(),
                    invoicePidCounter.getPid(), invoicePidCounter.getCount());
        } catch (Exception ex) {
            throw new ApplicationException("Error resetting redis counter " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public void deleteRedisCountKeyForInvoiceAndPO(String invoiceRefNum, String poId, String pid) {
        try {
            log.info("Deleting invoice and po redis counter for invoice {}, po_id {} and pid {}", invoiceRefNum, poId, pid);
            RedisHandler.redisOps(RedisOps.DEL, CacheUtils.getItemScanCountKey( grnConfig.getKeyPrefix(), invoiceRefNum, pid));
            RedisHandler.redisOps(RedisOps.DEL, CacheUtils.getItemScanCountKey( grnConfig.getKeyPrefix(), poId, pid));
            log.info("Deleted invoice and po redis counter for invoice {},po {} and pid {}", invoiceRefNum, poId, pid);
        } catch (Exception ex) {
            log.error("Error deleting redis counter for invoice {}, po_id {} and pid {}, error: {}", invoiceRefNum, poId, pid, ex.getMessage());
            throw new ApplicationException("Error deleting redis counter " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private long getCountFromDB(String itemId, String pid, boolean poCount) {
        long count = 0;
        try {
            if (poCount)
                count = grnItemEntityService.countByPidAndPoId(pid, itemId);
            else
                count = grnItemEntityService.countByPidAndInvoiceRefNum(pid, itemId);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return count;
    }

    @Override
    @Logging
    public long incrementFailCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementFailCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getTotalFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void setTotalFailed(String grnCode, String pid, long totalFailed) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid), totalFailed);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getTotalScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public boolean isChannelGreen(String grnCode, String pid) {
        return PASSED.equals(getGrnStatus(grnCode, pid));
    }

    @Override
    @Logging
    public boolean isQcStatusFailed(String grnCode, String pid) {
        return FAILED.equals(getGrnStatus(grnCode, pid));
    }

    @Override
    @Logging
    public void setGrnStatus(String grnCode, String pid, String status) {
        if(!RedisHandler.hasKey(CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Status key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid), status);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public String getGrnStatus(String grnCode, String pid) {
        try {
            if(RedisHandler.hasKey(CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)))
                return RedisHandler.redisOps(RedisOps.GET, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString();
            else
                return getStatusFromDB(grnCode, pid);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private String getStatusFromDB(String grnCode, String pid) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByGrnCodeAndPid(grnCode, pid);
            if(grnItemEntity == null)
                return PASSED;
            return grnItemEntity.getStatus();
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void manualOverrideGRN(String grnCode, String pid) {
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getGRNManualOverrideKey(grnConfig.getKeyPrefix(), grnCode, pid), true);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public boolean isGRNManualOverriden(String grnCode, String pid) {
        try {
            return RedisHandler.hasKey(CacheUtils.getGRNManualOverrideKey(grnConfig.getKeyPrefix(), grnCode, pid));
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public long getInvoiceQtyCount(String invoiceRefNum, String pid) {
        String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid);
        try {
            if(RedisHandler.hasKey(cacheKey)) {
                return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, cacheKey).toString());
            } else {
                return getCountFromDB(invoiceRefNum, pid, false);
            }
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public long incrementAllScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementAllScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long incrementAllFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementAllFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void setAllFailed(String grnCode, String pid, long failed) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid), failed);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getAllFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void setAllEstimatedQty(String grnCode, String pid, long allEstimatedQty) {
        if(!RedisHandler.hasKey(CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid), allEstimatedQty);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getAllEstimatedQty(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public long getAllScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public void setManualOverrideForList(List<GRNPidListDTO> grnPidList) {

        Map<String,String> map = new HashMap<>();
        for(GRNPidListDTO grnPid : grnPidList) {
            map.put(CacheUtils.getGRNManualOverrideKey(grnConfig.getKeyPrefix(), grnPid.getGrnCode(), grnPid.getPid()), "true");
        }

        try {
            redisTemplate.opsForValue().multiSet(map);
        } catch(Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
