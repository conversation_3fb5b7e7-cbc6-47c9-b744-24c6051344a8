package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.BoxDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;

@Primary
@Component(QualifierConstants.JDBC_BOX_DAO)
@Deprecated
public class BoxDAOImpl implements BoxDAO, Queries, GRNConstants {

	@Autowired
	private DBConfig dbConfig;

	@Logging
	@Override
	public boolean checkBoxExists(String grnCode, String pid, String boxBarcode) {

		try (Connection con = dbConfig.getDataSource().getConnection(); PreparedStatement pst = con.prepareStatement(SELECT_BOX_BARCODE)) {

			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, pid);
			pst.setString(i++, boxBarcode);

			try (ResultSet rs = pst.executeQuery()) {
				return rs.next();
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
}
