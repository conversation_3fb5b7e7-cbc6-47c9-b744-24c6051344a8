package com.lenskart.nexs.grn.dao.hImpl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnPidMasterEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.GrnPidMasterEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnPidMasterEntityId;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.GRNPIDDAO;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.GRNPidListDTO;
import com.lenskart.nexs.grn.dto.request.GRNPidSearchDTO;
import com.lenskart.nexs.grn.dto.response.GRNProduct;
import com.lenskart.nexs.grn.dto.response.GRNProductResponse;
import com.lenskart.nexs.grn.dto.response.PidSearchResponse;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNPIDMaster;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.Product;
import com.lenskart.nexs.grn.service.PoInvoiceService;
import com.lenskart.nexs.grn.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Service(QualifierConstants.JPA_GRN_PID_DAO)
@Transactional(rollbackFor = Exception.class)
public class GrnPidHDAOImpl implements GRNPIDDAO, Queries, GRNConstants {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private GRNConfig grnConfig;

    @Autowired
    private GrnPidMasterEntityServiceImpl grnPidMasterEntityService;

    @Autowired
    private PoInvoiceService poInvoiceService;

    @Override
    @Logging
    public boolean setManualOverride(String grnCode, String pid, Boolean isEnabled) {
        GrnPidMasterEntity grnPidMasterEntity = grnPidMasterEntityService.findByIdGrnCodeAndIdPid(grnCode, pid);
        try {
            short manualOverrideFlag = 2;
            if(isEnabled) {
                manualOverrideFlag = 1;
            }
            grnPidMasterEntity.setManualOverride(Integer.valueOf(manualOverrideFlag));
            return grnPidMasterEntityService.getDao().save(grnPidMasterEntity) !=null ? true : false;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

//    @Override
//    @Logging
//    public boolean updatePidEstimatedQty(String grnCode, String pid, Long newEstimatedQty, List<Long> estimatedQtyHistory) {
//        GrnPidMasterEntity grnPidMasterEntity = grnPidMasterEntityService.findByIdGrnCodeAndIdPid(grnCode, pid);
//        try {
//            grnPidMasterEntity.setEstimatedTotalQuantity(newEstimatedQty);
//            grnPidMasterEntity.setEstimatedQuantityList(CommonUtils.objectMapper.writeValueAsString(estimatedQtyHistory));
//            grnPidMasterEntity.setGrnPidStatus(PENDING);
//            return grnPidMasterEntityService.getDao().save(grnPidMasterEntity) != null ? true : false;
//        } catch (Exception ex) {
//            throw new ApplicationException("Exception while updating estimated quantity : " + ex.getMessage(),
//                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//    }

    @Logging
    @Override
    public boolean createGRNPIDMaster(GRNPIDMaster grnpidMaster) throws JsonProcessingException {
        try {
            GrnPidMasterEntity grnPidMasterEntity = buildGrnPidMaster(grnpidMaster);
            return grnPidMasterEntityService.getDao().save(grnPidMasterEntity) != null ? true : false;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private GrnPidMasterEntity buildGrnPidMaster(GRNPIDMaster grnpidMaster) throws JsonProcessingException {
        GrnPidMasterEntity grnPidMasterEntity = new GrnPidMasterEntity();
        GrnPidMasterEntityId id = new GrnPidMasterEntityId();
        id.setGrnCode(grnpidMaster.getGrnCode());
        id.setPid(grnpidMaster.getPid());
        grnPidMasterEntity.setId(id);
        grnPidMasterEntity.setInvoiceId(grnpidMaster.getInvoiceId());
        grnPidMasterEntity.setInvoiceRefNum(grnpidMaster.getInvoiceReferenceNum());
        grnPidMasterEntity.setVendor(grnpidMaster.getVendor());
        grnPidMasterEntity.setBrand(grnpidMaster.getBrand());
        grnPidMasterEntity.setCategoryId(grnpidMaster.getCategoryId());
        grnPidMasterEntity.setPidDescription(grnpidMaster.getPidDescription());
        grnPidMasterEntity.setPrice(BigDecimal.valueOf(grnpidMaster.getPrice()));
        grnPidMasterEntity.setTaxRate(BigDecimal.valueOf(grnpidMaster.getTaxRate()));
        grnPidMasterEntity.setCgstRate(BigDecimal.valueOf(grnpidMaster.getCgstRate()));
        grnPidMasterEntity.setSgstRate(BigDecimal.valueOf(grnpidMaster.getSgstRate()));
        grnPidMasterEntity.setIgstRate(BigDecimal.valueOf(grnpidMaster.getIgstRate()));
        grnPidMasterEntity.setEstimatedTotalQuantity(grnpidMaster.getEstimatedQuantity());
        grnPidMasterEntity.setEstimatedQuantityList(CommonUtils.objectMapper.writeValueAsString(grnpidMaster.getEstimatedQuantityList()));
        grnPidMasterEntity.setGrnPidStatus(grnpidMaster.getPidStatus());
        grnPidMasterEntity.setCreatedBy(grnpidMaster.getCreatedBy());
        grnPidMasterEntity.setUpdatedBy(grnpidMaster.getUpdatedBy());
        grnPidMasterEntity.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        grnPidMasterEntity.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        grnPidMasterEntity.setManualOverride(Integer.valueOf(grnpidMaster.getManualOverride()));
        return grnPidMasterEntity;
    }

    @Logging
    @Override
    public int countManualOverride(String invoice_ref_num, String pid) {
        try {
            return grnPidMasterEntityService.findByInvoiceRefNumAndIdPidAndManualOverride(invoice_ref_num,pid,1).size();
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public GRNPIDMaster getGRNPIDMaster(String grnCode, String pid) {
        try {
            GrnPidMasterEntity grnPidMasterEntity = grnPidMasterEntityService.findByIdGrnCodeAndIdPid(grnCode, pid);
            GRNPIDMaster grnPIDMaster = convertToGRNPidMaster(grnPidMasterEntity);
            return grnPIDMaster;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    private GRNPIDMaster convertToGRNPidMaster(GrnPidMasterEntity grnPidMasterEntity) {
        GRNPIDMaster grnPIDMaster = new GRNPIDMaster();
        try {
            BeanUtils.copyProperties(grnPidMasterEntity, grnPIDMaster);
            grnPIDMaster.setGrnCode(grnPidMasterEntity.getId().getGrnCode());
            grnPIDMaster.setPid(grnPidMasterEntity.getId().getPid());
            grnPIDMaster.setPidStatus(grnPidMasterEntity.getGrnPidStatus());
            List<Long> longList = CommonUtils.objectMapper.readValue(grnPidMasterEntity.getEstimatedQuantityList(),
                    new TypeReference<List<Long>>() {
                    });
            grnPIDMaster.setEstimatedQuantityList(longList);
            grnPIDMaster.setEstimatedQuantity(CollectionUtils.isEmpty(longList) ? 0 : longList.get(0));
            grnPIDMaster.setInvoiceReferenceNum(grnPidMasterEntity.getInvoiceRefNum());
        } catch (Exception ex) {
            log.error("Exception while converting: " + ex.getMessage(), ex);
        }
        return grnPIDMaster;
    }

    @Logging
    @Override
    public void updateGRNPIDMasterStatus(String status, String grnCode, String pid) {
        try {
            GrnPidMasterEntity grnPidMasterEntity = grnPidMasterEntityService.findByIdGrnCodeAndIdPid(grnCode, pid);
            int i = 1;
            grnPidMasterEntity.setGrnPidStatus(status);
            grnPidMasterEntityService.getDao().save(grnPidMasterEntity);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public List<GRNPIDMaster> getGRNPIDS(String grnCode) {
        try {
            List<GrnPidMasterEntity> grnPidMasterEntities = grnPidMasterEntityService.findByIdGrnCode(grnCode);
            List<GRNPIDMaster> grnpidMasterList = new ArrayList<>();
            for (GrnPidMasterEntity grnPidMaster : grnPidMasterEntities) {
                GRNPIDMaster pid = new GRNPIDMaster(grnPidMaster.getId().getGrnCode(),
                        grnPidMaster.getId().getPid(),
                        grnPidMaster.getInvoiceRefNum(), grnPidMaster.getGrnPidStatus(),
                        grnPidMaster.getManualOverride().shortValue());
                grnpidMasterList.add(pid);
            }
            return grnpidMasterList;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public List<GRNPIDMaster> getGRNPIDS(List<String> grnList) {
        try {
            List<GrnPidMasterEntity> grnPidMasterEntities = grnPidMasterEntityService.findByIdGrnCodeIn(grnList);
            List<GRNPIDMaster> grnpidMasterList = new ArrayList<>();
            for (GrnPidMasterEntity grnPidMaster : grnPidMasterEntities) {
                GRNPIDMaster pid = new GRNPIDMaster(grnPidMaster.getId().getGrnCode(),
                        grnPidMaster.getId().getPid(),
                        grnPidMaster.getInvoiceRefNum(), grnPidMaster.getGrnPidStatus(),
                        grnPidMaster.getManualOverride().shortValue());
                grnpidMasterList.add(pid);
            }
            return grnpidMasterList;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public List<GRNPIDMaster> getGRNPIDDetails(List<String> grnCodes) throws Exception {
        try {
            List<GRNPIDMaster> grnPIDMasters = new ArrayList<>();
            List<GrnPidMasterEntity> grnPidMasterEntities =
                    grnPidMasterEntityService.findByIdGrnCodeInOrderByCreatedAtDesc(grnCodes);
            grnPidMasterEntities.forEach(grnPidMasterEntity -> {
                GRNPIDMaster grnPIDMaster = convertToGRNPidMaster(grnPidMasterEntity);
                grnPIDMasters.add(grnPIDMaster);
            });
            return grnPIDMasters;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Boolean> checkFailedGRNPID(String invoiceRefNum, String pid) {
        Map<String, Boolean> result = new HashMap<>();
        result.put("isFailed", false);
        result.put("manualOverride", false);
        try {
            List<GrnPidMasterEntity> grnPidMasterEntities =
                    grnPidMasterEntityService.findByInvoiceRefNumAndIdPidAndGrnPidStatus(invoiceRefNum, pid, FAILED);
            if (!CollectionUtils.isEmpty(grnPidMasterEntities)) {
                result.put("isFailed", true);
                grnPidMasterEntities.stream().filter(itme -> itme.getManualOverride() == 1).findAny().ifPresent(item -> {
                    result.put("manualOverride", true);
                });
            }
            return result;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public List<PidSearchResponse> grnPidSearch(GRNPidSearchDTO grnPidSearchReq) {

        List<PidSearchResponse> result = new ArrayList<>();

        StringBuilder query = new StringBuilder();
        query.append(GRN_PID_SEARCH);
        String criteria = buildPidSearchCriteria(grnPidSearchReq);
        if(StringUtils.isNotBlank(criteria)) {
            query.append(" and "+criteria);
        }

        query.append(" group by gp.grn_code, gp.pid");
        query.append(" order by gp.created_at DESC");
        Integer offset = grnPidSearchReq.getPage() * grnConfig.getPageSize();
        query.append(" limit " + grnConfig.getPageSize() + " offset " + offset);

        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString(), Tuple.class);
            List<Tuple> resultList = nativeQuery.getResultList();
            try{
                for(Tuple tuple: resultList) {
                    PidSearchResponse pidSearchResponse = resultSetToPidSearchResponse(tuple);
                    result.add(pidSearchResponse);
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return result;
    }


    public PidSearchResponse resultSetToPidSearchResponse(Tuple tuple) throws Exception {

        PidSearchResponse pidSearchResponse = new PidSearchResponse();
        ObjectMapper mapper = CommonUtils.objectMapper;
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        pidSearchResponse.setGrnCode(tuple.get("grn_code").toString());
        pidSearchResponse.setGrnStatus(tuple.get("grn_status").toString());
        pidSearchResponse.setCreatedOn(tuple.get("created_at").toString());
        pidSearchResponse.setVendorName(tuple.get("vendor").toString());
        pidSearchResponse.setPoId(tuple.get("po_id").toString());
        pidSearchResponse.setPid(tuple.get("pid").toString());
        pidSearchResponse.setCreatedBy(tuple.get("created_by").toString());
//		pidSearchResponse.setEstimatedQty((int)tuple.get("estimated_total_quantity"));
//		pidSearchResponse.setSamplingPercent((int)tuple.get("sampling"));
        pidSearchResponse.setFailurePercent((int)tuple.get("failure_percentage"));
        pidSearchResponse.setQcPass((int)tuple.get("pass_count"));
        pidSearchResponse.setQcFail((int)tuple.get("total_count") - (int)tuple.get("pass_count"));

        Date updatedAt = tuple.get("updated_at",Date.class);
        Date currDate = new Date(System.currentTimeMillis());
        long failedSince = ((currDate.getTime() - updatedAt.getTime()) / (1000 * 60 * 60 * 24)) % 365;

        pidSearchResponse.setFailedSince(failedSince < 1 ? "0 Days" : Long.toString(failedSince) + " Days");
        log.info("Fetching invoice details from purchase invoice");
        Invoice invoice = poInvoiceService.getInvoiceDetails(tuple.get("invoice_id").toString());
        Product product = CommonUtils.getProductDetailsFromInvoice(invoice.getPids(), tuple.get("pid").toString());

        pidSearchResponse.setInvoiceQty(product != null ? product.getQuantity() : 0L);
        pidSearchResponse.setCategoryId(product != null ? product.getCategoryId() : null);
        return pidSearchResponse;
    }

    @Logging
    @Override
    public boolean updateManualOverrideFlag(List<GRNPidListDTO> grnPidList, boolean isAllowed) {
        StringBuilder build = new StringBuilder();
        build.append(UPDATE_MANUAL_OVERRIDE_FLAG);
        build.append(" (");
        for (GRNPidListDTO grnPid : grnPidList) {
            build.append("(" + "\"" + grnPid.getGrnCode() + "\",\"" + grnPid.getPid() + "\")");
            build.append(",");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
        build.append(")");
        try {
            Query query = entityManager.createQuery(build.toString());
            int i = 1;
            short manualOverrideFlag = 2;
            if (isAllowed) {
                manualOverrideFlag = 1;
            }
            query.setParameter(i++, manualOverrideFlag);
            int rowsUpdated = query.executeUpdate();
            return rowsUpdated > 0 ? true : false;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }

    }

    private String buildPidSearchCriteria(GRNPidSearchDTO grnPidSearchReq) {

        StringBuilder build = new StringBuilder();

        build.append("gm.facility = " + "\"" + grnPidSearchReq.getFacility() + "\"");

        if(StringUtils.isNotBlank(grnPidSearchReq.getGrnCode())) {
            build.append(" and ");
            build.append("gp.grn_code = " + "\"" + grnPidSearchReq.getGrnCode() + "\"");
        }

        if(StringUtils.isNotBlank(grnPidSearchReq.getPid())) {
            build.append(" and ");
            build.append("gp.pid = " + "\"" + grnPidSearchReq.getPid() + "\"");
        }

        if(StringUtils.isNotBlank(grnPidSearchReq.getInvoiceRefNumber())) {
            build.append(" and ");
            build.append("gp.invoice_ref_num = " + "\"" + grnPidSearchReq.getInvoiceRefNumber() + "\"");
        }

        if(StringUtils.isNotBlank(grnPidSearchReq.getVendor())) {
            build.append(" and ");
            build.append("gp.vendor = " + "\"" + grnPidSearchReq.getVendor() + "\"");
        }

        if(StringUtils.isNotBlank(grnPidSearchReq.getCreatedBy())) {
            build.append(" and ");
            build.append("gp.created_by = " + "\"" + grnPidSearchReq.getCreatedBy() + "\"");
        }


        return build.toString();
    }

    @Logging
    @Override
    public GRNProductResponse getGRNProducts(String grnCode, int page, int pageSize) {

        GRNProductResponse response = null;
        Map<String, GRNProduct> map = new HashMap<>();
        StringBuilder query = new StringBuilder();
        query.append(GET_GRN_PRODUCT);
        query.append(" group by gm.grn_code, gp.pid");
        query.append(" order by gp.created_at DESC");
        Integer offset = page * pageSize;
        query.append(" limit " + pageSize + " offset " + offset);

        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString(),Tuple.class);
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            nativeQuery.setParameter(i++, grnCode);
            List<Tuple> resultList = nativeQuery.getResultList();
            try {
                for(Tuple tuple: resultList) {
                    if(response == null) {
                        boolean iqc = false;
                        Invoice invoice = poInvoiceService.getInvoiceDetails(tuple.get("invoice_id").toString());
                        response = new GRNProductResponse(tuple.get("grn_code").toString(),
                                tuple.get("grn_status").toString(), tuple.get("created_at").toString(),
                                tuple.get("po_id").toString(), tuple.get("assigned_to").toString(),
                                invoice);
                        Product product = invoice.getPids().stream().filter(invoiceItem ->
                                invoiceItem.getIqcSamplingPercent() != 0.0).findFirst().orElse(null);
                        if (product != null)
                            iqc = true;
                        response.setIqc(iqc);
                    }
                    GRNProduct grnProduct = DBUtils.resultSetToGRNProduct(tuple);
                    if(grnProduct.getPid() != null && !grnProduct.getPid().isEmpty())
                        map.put(grnProduct.getPid(), grnProduct);
                }

                if(response != null) {
                    response.setPids(map);
                }

            } catch (Exception ex) {
                log.error("getGRNProducts Exception:" + ex.getMessage(), ex);
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            log.error("getGRNProducts All Exception:" + ex.getMessage(), ex);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return response;
    }

    @Logging
    @Override
    public int countPids(GRNPidSearchDTO grnPidSearchReq, boolean countBlockedOrAlls) {
        StringBuilder query = new StringBuilder();
        query.append(COUNT_PIDS);
        String criteria = buildPidSearchCriteria(grnPidSearchReq);
        if (StringUtils.isNotBlank(criteria)) {
            query.append(" and " + criteria);
        }

        if (countBlockedOrAlls) {
            query.append(" and gp.grn_pid_status = 'FAILED' and gp.manual_override = 0");
        }

        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString(), Tuple.class);
            List<Tuple> resultList = nativeQuery.getResultList();
            try {
                return resultList.get(0).get("pid_count", BigInteger.class).intValue();
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }

        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }

    }
}
