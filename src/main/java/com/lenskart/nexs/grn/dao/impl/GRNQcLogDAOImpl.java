package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.GRNQcLogDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNQcLog;

@Primary
@Component(QualifierConstants.JDBC_GRN_QC_LOG_DAO)
@Deprecated
public class GRNQcLogDAOImpl implements GRNQcLogDAO, Queries, GRNConstants {

	@Autowired
	private DBConfig dbConfig;

	@Logging
	@Override
	public void saveLog(GRNQcLog log) {

		try (Connection con = dbConfig.getDataSource().getConnection(); PreparedStatement pst = con.prepareStatement(INSERT_GRN_QC_LOG)) {

			int i = 1;
			pst.setString(i++, log.getGrnCode());
			pst.setString(i++, log.getPid());
			pst.setString(i++, log.getBarcode());
			pst.setString(i++, log.getAction());
			pst.setString(i++, log.getReason());
			pst.setString(i++, MDC.get("USER_ID"));

			pst.execute();

		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}

	}

	@Logging
	@Override
	public List<Map<String, Object>> getGRNQcLogByGRNAndPid(String grnCode, String pid) {

		List<Map<String, Object>> result = null;
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(GET_GRN_QC_LOG_BY_GRN_PID, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);) {

			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, pid);

			try (ResultSet rs = pst.executeQuery();) {
				result = new ArrayList<>();
				while(rs.next()) {
					Map<String, Object> map = new HashMap<>();
					map.put("barcode", rs.getString("barcode"));
					map.put("qc_reason", rs.getString("reason"));
					result.add(map);
				}
				
				return result;
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
}
