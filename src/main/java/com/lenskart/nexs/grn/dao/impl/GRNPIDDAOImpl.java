package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lenskart.nexs.common.dbutil.CommonDbUtils;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dto.response.GRNProduct;
import com.lenskart.nexs.grn.dto.response.GRNProductResponse;
import com.lenskart.nexs.grn.model.Invoice;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.GRNPIDDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.GRNPidListDTO;
import com.lenskart.nexs.grn.dto.request.GRNPidSearchDTO;
import com.lenskart.nexs.grn.dto.response.PidSearchResponse;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNPIDMaster;

@Primary
@Component(QualifierConstants.JDBC_GRN_PID_DAO)
@Deprecated
public class GRNPIDDAOImpl implements GRNPIDDAO, Queries, GRNConstants {

	@Autowired
    private DBConfig dbConfig;
	
	@Autowired
	private GRNConfig grnConfig;
	
	@Override
    @Logging
    public boolean setManualOverride(String grnCode, String pid, Boolean isEnabled) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(SET_MANUAL_OVERRIDE)
        ) {
        	int i = 1;
        	short manualOverrideFlag = 2;
        	if(isEnabled) {
        		manualOverrideFlag = 1;
        	}
        	pst.setShort(i++, manualOverrideFlag);
        	pst.setString(i++, grnCode);
        	pst.setString(i++, pid);
        	int rowsUpdated = pst.executeUpdate();
        	return rowsUpdated > 0 ? true : false;
        	
        } catch (SQLException ex) {
        	throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
	}
	
//	@Override
//    @Logging
//    public boolean updatePidEstimatedQty(String grnCode, String pid, Long newEstimatedQty, List<Long> estimatedQtyHistory) {
//        try (Connection con = dbConfig.getDataSource().getConnection();
//             PreparedStatement pst = con.prepareStatement(UPDATE_GRN_PID_ESTIMATED_QTY)
//        ) {
//        	int i = 1;
//        	ObjectMapper mapper = new ObjectMapper();
//        	pst.setLong(i++, newEstimatedQty);
//        	pst.setString(i++, mapper.writeValueAsString(estimatedQtyHistory));
//        	pst.setString(i++, grnCode);
//        	pst.setString(i++, pid);
//        	int rowsUpdated = pst.executeUpdate();
//        	return rowsUpdated > 0 ? true : false;
//
//        } catch (SQLException ex) {
//        	throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        } catch (Exception ex) {
//        	throw new ApplicationException("Exception while updating estimated quantity : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//	}

	@Logging
    @Override
    public boolean createGRNPIDMaster(GRNPIDMaster grnpidMaster) throws JsonProcessingException {
        try (Connection con = dbConfig.getDataSource().getConnection();
                PreparedStatement pst = con.prepareStatement(CREATE_GRN_PID_MASTER)){
            int i = 1;
            ObjectMapper mapper = new ObjectMapper();
            pst.setString(i++, grnpidMaster.getGrnCode());
            pst.setString(i++, grnpidMaster.getPid());
            pst.setString(i++, grnpidMaster.getInvoiceId());
            pst.setString(i++, grnpidMaster.getInvoiceReferenceNum());
            pst.setString(i++, grnpidMaster.getVendor());
            pst.setString(i++, grnpidMaster.getBrand());
            pst.setInt(i++, grnpidMaster.getCategoryId());
            pst.setString(i++, grnpidMaster.getPidDescription());
            pst.setDouble(i++, grnpidMaster.getPrice());
            pst.setDouble(i++, grnpidMaster.getTaxRate());
            pst.setDouble(i++, grnpidMaster.getCgstRate());
            pst.setDouble(i++, grnpidMaster.getSgstRate());
            pst.setDouble(i++, grnpidMaster.getIgstRate());
            pst.setLong(i++, grnpidMaster.getEstimatedQuantity());
            pst.setString(i++, mapper.writeValueAsString(grnpidMaster.getEstimatedQuantityList()));
            pst.setString(i++, PENDING);
            pst.setString(i++, grnpidMaster.getCreatedBy());
            pst.setString(i++, grnpidMaster.getUpdatedBy());
            pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
            pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
            pst.setShort(i++, grnpidMaster.getManualOverride());
            pst.execute();
            return true;
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public int countManualOverride(String invoice_ref_num, String pid) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_MANUAL_OVERRIDE)
        ) {
            int i = 1;
            pst.setString(i++, invoice_ref_num);
            pst.setString(i++, pid);
            try(ResultSet rs = pst.executeQuery()) {
                if(rs.next())
                    return rs.getInt("COUNT(1)");
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return 0;
    }
    
    @Logging
    @Override
    public GRNPIDMaster getGRNPIDMaster(String grnCode, String pid) {
    	
    	try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(GET_GRN_PID_MASTER, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);)
		{
			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, pid);
			GRNPIDMaster grnPIDMaster = null;
			Map<String, Class> jsonFieldTypes = new HashMap<>();
			jsonFieldTypes.put("estimated_quantity_list", List.class);
			try (ResultSet rs = pst.executeQuery();) {
				if (rs.first()) {
					grnPIDMaster = (GRNPIDMaster) CommonDbUtils.resultsetToPojo(jsonFieldTypes, rs, GRNPIDMaster.class);
				}
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grnPIDMaster;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
    }
    
    @Logging
    @Override
    public void updateGRNPIDMasterStatus(String status, String grnCode, String pid) {
    	
    	try (Connection con = dbConfig.getDataSource().getConnection();
                PreparedStatement pst = con.prepareStatement(UPDATE_GRN_PID_MASTER_STATUS)
           ) {
           	int i = 1;
        	pst.setString(i++, status);
           	pst.setString(i++, grnCode);
           	pst.setString(i++, pid);
           	pst.execute();
           	
    	} catch (SQLException ex) {
    	    throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
    	}
    }

    @Logging
    @Override
    public List<GRNPIDMaster> getGRNPIDS(String grnCode) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_GRN_PID_MASTER_BY_GRN);)
        {
            int i = 1;
            pst.setString(i++, grnCode);
            List<GRNPIDMaster> grnpidMasterList = new ArrayList<>();
            try (ResultSet rs = pst.executeQuery();) {
                while (rs.next()) {
                    GRNPIDMaster pid = new GRNPIDMaster(rs.getString("grn_code"), rs.getString("pid"),
                            rs.getString("invoice_ref_num"), rs.getString("grn_pid_status"),
							rs.getShort("manual_override"));
                    grnpidMasterList.add(pid);
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return grnpidMasterList;
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

	@Override
	public List<GRNPIDMaster> getGRNPIDS(List<String> grnList) {

		StringBuilder build = new StringBuilder();
		build.append(GET_GRN_PID_MASTER_BY_GRN_LIST);

		for (int n = 0; n < grnList.size(); n++)
			build.append("?,");
		if (build.length() > 0)
			build.deleteCharAt(build.lastIndexOf(","));

		build.append(")");

		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(build.toString());)
		{
			int i = 1;
			for(String grn : grnList)
				pst.setString(i++, grn);
			List<GRNPIDMaster> grnpidMasterList = new ArrayList<>();
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next()) {
					GRNPIDMaster pid = new GRNPIDMaster(rs.getString("grn_code"), rs.getString("pid"),
							rs.getString("invoice_ref_num"), rs.getString("grn_pid_status"),
							rs.getShort("manual_override"));
					grnpidMasterList.add(pid);
				}
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grnpidMasterList;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
    @Override
    public List<GRNPIDMaster> getGRNPIDDetails(List<String> grnCodes) throws Exception {
    	
    	List<GRNPIDMaster> grnPidDetails = null;
    	
    	StringBuilder build = new StringBuilder();
        build.append(GET_GRN_PID_DETAILS);
        for (int n = 0; n < grnCodes.size(); n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
        
        build.append(") order by created_at desc");
    	try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);)
		{
    
    		int i = 1;
    		for(String grn: grnCodes) {
    			pst.setString(i++, grn);
    		}
    		
    		grnPidDetails = new ArrayList<>();
    		Map<String, Class> jsonFieldsTypesMap = new HashMap<>();
			jsonFieldsTypesMap.put("estimated_quantity_list", List.class);
    		try (ResultSet rs = pst.executeQuery();) {
    			
				while (rs.next()) {
					GRNPIDMaster grnPidMaster = (GRNPIDMaster) CommonDbUtils.resultsetToPojo(jsonFieldsTypesMap,rs,GRNPIDMaster.class);
					grnPidDetails.add(grnPidMaster);
				}
				
				return grnPidDetails;
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
    		
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
    }
    
	@Logging
	@Override
	public Map<String, Boolean> checkFailedGRNPID(String invoiceRefNum, String pid) {
		Map<String, Boolean> result = new HashMap<>();
		result.put("isFailed", false);
		result.put("manualOverride", false);
		try (Connection con = dbConfig.getDataSource().getConnection(); PreparedStatement pst = con.prepareStatement(GET_FAILED_STATUS_GRN_PIDS, ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE);) {

			int i = 1;
			pst.setString(i++, invoiceRefNum);
			pst.setString(i++, pid);
			pst.setString(i++, FAILED);

			try (ResultSet rs = pst.executeQuery();) {
				if (rs.first()) {
					result.put("isFailed", true);
					do {

						if (rs.getShort("manual_override") == 1) {
							result.put("manualOverride", true);
							break;
						}
					} while (rs.next());
				}
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return result;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
	
	@Logging
	@Override
	public List<PidSearchResponse> grnPidSearch(GRNPidSearchDTO grnPidSearchReq) {
		
		List<PidSearchResponse> result = new ArrayList<>();
		
		StringBuilder query = new StringBuilder();
		query.append(GRN_PID_SEARCH);
		String criteria = buildPidSearchCriteria(grnPidSearchReq);
		if(StringUtils.isNotBlank(criteria)) {
			query.append(" and "+criteria);
		}
		
		query.append(" group by gp.grn_code, gp.pid");
		query.append(" order by gp.created_at DESC");
		Integer offset = grnPidSearchReq.getPage() * grnConfig.getPageSize();
		query.append(" limit " + grnConfig.getPageSize() + " offset " + offset);
		
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(query.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);) {
			
			
			try (ResultSet rs = pst.executeQuery();) {
				
				while(rs.next()) {
					PidSearchResponse pidSearchResponse = DBUtils.resultSetToPidSearchResponse(rs);
					result.add(pidSearchResponse);
				}
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			
		} catch (Exception ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}

		return result;
	}
	
	@Logging
	@Override
	public boolean updateManualOverrideFlag(List<GRNPidListDTO> grnPidList, boolean isAllowed) {

		StringBuilder build = new StringBuilder();
		build.append(UPDATE_MANUAL_OVERRIDE_FLAG);
		build.append(" (");
		for (GRNPidListDTO grnPid : grnPidList) {
			build.append("(" + "\"" + grnPid.getGrnCode() + "\",\"" + grnPid.getPid() + "\")");
			build.append(",");
		}

		if (build.length() > 0) {
			build.deleteCharAt(build.lastIndexOf(","));
		}

		build.append(")");
		try (Connection con = dbConfig.getDataSource().getConnection(); PreparedStatement pst = con.prepareStatement(build.toString())) {
			int i = 1;
			short manualOverrideFlag = 2;
			if (isAllowed) {
				manualOverrideFlag = 1;
			}
			pst.setShort(i++, manualOverrideFlag);
			int rowsUpdated = pst.executeUpdate();
			return rowsUpdated > 0 ? true : false;

		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}

	}

	private String buildPidSearchCriteria(GRNPidSearchDTO grnPidSearchReq) {
		
		StringBuilder build = new StringBuilder();
		
		build.append("gm.facility = " + "\"" + grnPidSearchReq.getFacility() + "\"");
		
		if(StringUtils.isNotBlank(grnPidSearchReq.getGrnCode())) {
			build.append(" and ");
			build.append("gp.grn_code = " + "\"" + grnPidSearchReq.getGrnCode() + "\"");
		}
		
		if(StringUtils.isNotBlank(grnPidSearchReq.getPid())) {
			build.append(" and ");
			build.append("gp.pid = " + "\"" + grnPidSearchReq.getPid() + "\"");
		}
		
		if(StringUtils.isNotBlank(grnPidSearchReq.getInvoiceRefNumber())) {
			build.append(" and ");
			build.append("gp.invoice_ref_num = " + "\"" + grnPidSearchReq.getInvoiceRefNumber() + "\"");
		}
		
		if(StringUtils.isNotBlank(grnPidSearchReq.getVendor())) {
			build.append(" and ");
			build.append("gp.vendor = " + "\"" + grnPidSearchReq.getVendor() + "\"");
		}
		
		if(StringUtils.isNotBlank(grnPidSearchReq.getCreatedBy())) {
			build.append(" and ");
			build.append("gp.created_by = " + "\"" + grnPidSearchReq.getCreatedBy() + "\"");
		}
		
		
		return build.toString();
	}

	@Logging
	@Override
	public GRNProductResponse getGRNProducts(String grnCode, int page, int pageSize) {

		GRNProductResponse response = null;
		Map<String, GRNProduct> map = new HashMap<>();
		ObjectMapper mapper = new ObjectMapper();

		StringBuilder query = new StringBuilder();
		query.append(GET_GRN_PRODUCT);
		query.append(" group by gm.grn_code, gp.pid");
		query.append(" order by gp.created_at DESC");
		Integer offset = page * pageSize;
		query.append(" limit " + pageSize + " offset " + offset);

		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(query.toString());) {

			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, grnCode);
			try (ResultSet rs = pst.executeQuery();) {
				while(rs.next()) {
					if(response == null) {
						response = new GRNProductResponse(rs.getString("grn_code"),
								rs.getString("grn_status"), rs.getString("created_at"),
								rs.getString("po_id"), rs.getString("assigned_to"),
								mapper.readValue(rs.getString("invoice"), Invoice.class));
					}
					GRNProduct grnProduct = DBUtils.resultSetToGRNProduct(rs);
					if(grnProduct.getPid() != null && !grnProduct.getPid().isEmpty())
						map.put(grnProduct.getPid(), grnProduct);
				}
				
				if(response != null) {
					response.setPids(map);
				}
				
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (Exception ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
		return response;
	}
	
	@Logging
	@Override
	public int countPids(GRNPidSearchDTO grnPidSearchReq, boolean countBlockedOrAlls) {
		
		StringBuilder query = new StringBuilder();
		query.append(COUNT_PIDS);
		String criteria = buildPidSearchCriteria(grnPidSearchReq);
		if(StringUtils.isNotBlank(criteria)) {
			query.append(" and "+criteria);
		}
		
		if(countBlockedOrAlls) {
			query.append(" and gp.grn_pid_status = 'FAILED' and gp.manual_override = 0");
		}
		
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(query.toString());) {
			
			try (ResultSet rs = pst.executeQuery();) {
				rs.next();
				return rs.getInt("pid_count");
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			
		} catch (Exception ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
			
	}
}
