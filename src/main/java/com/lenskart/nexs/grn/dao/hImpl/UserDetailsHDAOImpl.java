package com.lenskart.nexs.grn.dao.hImpl;

import com.lenskart.nexs.common.entity.po.grn.UserDetailEntity;
import com.lenskart.nexs.common.entity.repositories.grn.UserDetailsRepository;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.UserDetailsDAO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.ExceptionConstants;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.UserDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service(QualifierConstants.JPA_USER_DETAILS_DAO)
@Transactional(rollbackFor = Exception.class)
public class UserDetailsHDAOImpl implements UserDetailsDAO {

    @Autowired
    private UserDetailsRepository userDetailsRepository;

    @Override
    public UserDetails getUserDetailsByEmpCode(String empCode) {
        return null;
    }

    @Override
    public UserDetailEntity getUserDetailsByEmpCodeDb(String empCode) {
        UserDetailEntity userDetailEntity =
                userDetailsRepository.findById(empCode).orElseThrow(() -> new ApplicationException(ExceptionConstants.GRN_NOT_FOUND,
                        GRNExceptionStatus.GRN_INTERNAL_EXCEPTION));
        return userDetailEntity;
    }

    @Override
    public void addUserDetails(UserDetails userDetails) {
        UserDetailEntity userDetailEntity = new UserDetailEntity();
        userDetailEntity.setId(userDetails.getEmpCode());
        userDetailEntity.setName(userDetails.getName());
        userDetailEntity.setPhoneCode(userDetails.getPhoneCode());
        userDetailEntity.setPhoneNumber(userDetails.getPhoneNumber());
        userDetailEntity.setEmail(userDetails.getEmail());
        userDetailsRepository.save(userDetailEntity);
    }

    @Override
    public void updateUserDetails(UserDetails userDetails) {
        UserDetailEntity userDetailEntity = userDetailsRepository.getOne(userDetails.getEmpCode());
        userDetailEntity.setName(userDetails.getName());
        userDetailEntity.setPhoneCode(userDetails.getPhoneCode());
        userDetailEntity.setPhoneNumber(userDetails.getPhoneNumber());
        userDetailEntity.setEmail(userDetails.getEmail());
        userDetailsRepository.save(userDetailEntity);
    }
}
