package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.QcStatusDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.GRNPidListDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.PidItemCounter;
import com.lenskart.nexs.grn.util.CacheUtils;
import com.lenskart.nexs.service.RedisHandler;

import lombok.extern.slf4j.Slf4j;

@Primary
@Slf4j
@Component(QualifierConstants.JDBC_QC_STATUS_DAO)
@Deprecated
public class QcStatusDAOImpl implements QcStatusDAO, GRNConstants {

    @Autowired
    private GRNConfig grnConfig;

    @Autowired
    private DBConfig dbConfig;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${nexs.grn.item.scan.count.ttl}")
    private long itemScanCountKeyTTL;

    @Override
    public boolean hasReferenceKey(String refId, String pid) {
        return RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(), refId, pid));
    }

    @Override
    public boolean hasGrnKey(String grnCode, String pid) {
        return RedisHandler.hasKey(CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), grnCode, pid));
    }

    @Override
    @Logging
    public long incrementScanCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementScanCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long incrementItemQtyCount(String itemId, String pid) {
        return incrementItemQtyCount(itemId, pid, false);
    }

    @Override
    @Logging
    public long incrementItemQtyCount(String itemId, String pid, boolean poCount) {
        try {
        	String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), itemId, pid);
			log.info("[incrementItemQtyCount] Cache key {}", cacheKey);
			if (!RedisHandler.hasKey(cacheKey)) {
				long counterFromDB = getCountFromDB(itemId, pid, poCount);
	    		if (!RedisHandler.hasKey(cacheKey)) {
	    			RedisHandler.redisOps(RedisOps.SETVALUETTL, cacheKey, counterFromDB, itemScanCountKeyTTL, TimeUnit.HOURS);
	    			log.error("counter from db for key {} : {}", cacheKey, counterFromDB);
	    		}
			}
			long counter = Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, cacheKey).toString());
			log.info("[incrementItemQtyCount] Counter from redis for key {} : {}", cacheKey, counter);
			return counter;
        } catch (Exception ex) {
            log.error("[incrementItemQtyCount] Unexpected Exception " + ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementItemQtyCount(String itemId, String pid) {
        return decrementItemQtyCount(itemId, pid, false);
    }

    @Override
    @Logging
    public long decrementItemQtyCount(String itemId, String pid, boolean poCount) {
    	try {
        	String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), itemId, pid);
            log.info("[decrementItemQtyCount] Cache key {}", cacheKey);
            if (RedisHandler.hasKey(cacheKey)) {
				long counter = Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, cacheKey).toString());
				log.info("[decrementItemQtyCount] Counter from redis for key {} : {}", cacheKey, counter);
				return counter;
			} else {
        		long countFromDB = getCountFromDB(itemId, pid, poCount);
        		log.info("[decrementItemQtyCount] Counter from db for key {} : {}", cacheKey, countFromDB--);
        		RedisHandler.redisOps(RedisOps.SETVALUETTL, cacheKey, countFromDB, itemScanCountKeyTTL, TimeUnit.HOURS);
				return countFromDB;
			}
        } catch (Exception ex) {
            log.error("[decrementItemQtyCount] Unexpected Exception " + ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

	@Override
    @Logging
	public void resetInvoiceQtyCount(PidItemCounter invoicePidCounter, Map<String, Long> counterMap) {
		try {
			String invoiceScanCountKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(),
					invoicePidCounter.getInvoiceRefNum(), invoicePidCounter.getPid());
			RedisHandler.redisOps(RedisOps.SETVALUETTL, invoiceScanCountKey, invoicePidCounter.getCount(), itemScanCountKeyTTL, TimeUnit.HOURS);
			counterMap.put(invoiceScanCountKey, invoicePidCounter.getCount());
			log.info("Counter updated for invoice {} and pid {} to : {}", invoicePidCounter.getInvoiceRefNum(),
					invoicePidCounter.getPid(), invoicePidCounter.getCount());
		} catch (Exception ex) {
			throw new ApplicationException("Error resetting redis counter " + ex.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

    @Override
    public void deleteRedisCountKeyForInvoiceAndPO(String invoiceRefNum, String poId, String pid) {
        try {
            log.info("Deleting invoice and po redis counter for invoice {}, po_id {} and pid {}", invoiceRefNum, poId, pid);
            String invoiceRefNumCacheKey = CacheUtils.getItemScanCountKey( grnConfig.getKeyPrefix(), invoiceRefNum, pid);
            String poIdCacheKey = CacheUtils.getItemScanCountKey( grnConfig.getKeyPrefix(), poId, pid);
            if (RedisHandler.hasKey(invoiceRefNumCacheKey))
                RedisHandler.redisOps(RedisOps.DEL, invoiceRefNumCacheKey);
            if (RedisHandler.hasKey(poIdCacheKey))
                RedisHandler.redisOps(RedisOps.DEL, poIdCacheKey);
            log.info("Deleted invoice and po redis counter for invoice {},po {} and pid {}", invoiceRefNum, poId, pid);
        } catch (Exception ex) {
            log.error("Error deleting redis counter for invoice {}, po_id {} and pid {}, error: {}", invoiceRefNum, poId, pid, ex.getMessage());
            throw new ApplicationException("Error deleting redis counter " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

	private long getCountFromDB(String itemId, String pid, boolean poCount) {
		String query = Queries.GET_COUNT_FOR_INVOICE;
		if (poCount)
			query = Queries.GET_COUNT_FOR_PO;
		try (Connection con = dbConfig.getDataSource().getConnection();
		     PreparedStatement pst = con.prepareStatement(query, ResultSet.TYPE_SCROLL_INSENSITIVE,
		             ResultSet.CONCUR_UPDATABLE))
		{
		    int i = 1;
		    pst.setString(i++, pid);
		    pst.setString(i++, itemId);
		    try (ResultSet rs = pst.executeQuery()) {
		        if (rs.first()) {
		            return rs.getInt("items_count");
		        }
		        return 0;
		    } catch (SQLException ex) {
		        throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		    }
		} catch (SQLException ex) {
		    throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

    @Override
    @Logging
    public long incrementFailCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementFailCount(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getTotalFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void setTotalFailed(String grnCode, String pid, long totalFailed) {
        if(!RedisHandler.hasKey(CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid), totalFailed);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getTotalScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public boolean isChannelGreen(String grnCode, String pid) {
        return PASSED.equals(getGrnStatus(grnCode, pid));
    }

    @Override
    @Logging
    public boolean isQcStatusFailed(String grnCode, String pid) {
        return FAILED.equals(getGrnStatus(grnCode, pid));
    }

    @Override
    @Logging
    public void setGrnStatus(String grnCode, String pid, String status) {
        if(!RedisHandler.hasKey(CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Status key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid), status);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public String getGrnStatus(String grnCode, String pid) {
        try {
            if(RedisHandler.hasKey(CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)))
                return RedisHandler.redisOps(RedisOps.GET, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString();
            else
                return getStatusFromDB(grnCode, pid);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private String getStatusFromDB(String grnCode, String pid) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(Queries.GET_GRN_STATUS, ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            int i = 1;
            pst.setString(i++, grnCode);
            pst.setString(i++, pid);
            try (ResultSet rs = pst.executeQuery();) {
                if (rs.first()) {
                    return rs.getString("status");
                }
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return null;
    }

    @Override
    @Logging
    public void manualOverrideGRN(String grnCode, String pid) {
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getGRNManualOverrideKey(grnConfig.getKeyPrefix(), grnCode, pid), true);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public boolean isGRNManualOverriden(String grnCode, String pid) {
        try {
            return RedisHandler.hasKey(CacheUtils.getGRNManualOverrideKey(grnConfig.getKeyPrefix(), grnCode, pid));
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public long getInvoiceQtyCount(String invoiceRefNum, String pid) {
        String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid);
        try {
            if(RedisHandler.hasKey(cacheKey)) {
                return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, cacheKey).toString());
            } else {
                return getCountFromDB(invoiceRefNum, pid, false);
            }
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public long incrementAllScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementAllScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long incrementAllFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.INCR, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long decrementAllFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Scan Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.DECR, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void setAllFailed(String grnCode, String pid, long failed) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid), failed);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getAllFailed(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void setAllEstimatedQty(String grnCode, String pid, long allEstimatedQty) {
        if(!RedisHandler.hasKey(CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid), allEstimatedQty);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public long getAllEstimatedQty(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public long getAllScanned(String grnCode, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Fail Key is NotFound");
        try {
            return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(), grnCode, pid)).toString());
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public void setManualOverrideForList(List<GRNPidListDTO> grnPidList) {

    	Map<String,String> map = new HashMap<>();
    	for(GRNPidListDTO grnPid : grnPidList) {
    		map.put(CacheUtils.getGRNManualOverrideKey(grnConfig.getKeyPrefix(), grnPid.getGrnCode(), grnPid.getPid()), "true");
    	}

    	try {
    		redisTemplate.opsForValue().multiSet(map);
    	} catch(Exception ex) {
    		throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
    	}
    }
}