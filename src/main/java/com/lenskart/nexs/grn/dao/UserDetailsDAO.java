package com.lenskart.nexs.grn.dao;

import com.lenskart.nexs.common.entity.po.grn.UserDetailEntity;
import com.lenskart.nexs.grn.model.UserDetails;

public interface UserDetailsDAO {

    UserDetailEntity getUserDetailsByEmpCodeDb(String empCode);

    UserDetails getUserDetailsByEmpCode(String empCode);

    void addUserDetails(UserDetails userDetails);

    void updateUserDetails(UserDetails userDetails);
}
