package com.lenskart.nexs.grn.dao.impl;


import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.dto.response.ASNResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.model.QCGradient;
import com.lenskart.nexs.grn.model.QCMaster;
import com.lenskart.nexs.grn.model.QcConfig;
import com.lenskart.nexs.grn.util.CacheUtils;
import com.lenskart.nexs.service.RedisHandler;
import org.springframework.web.server.ResponseStatusException;

@Component
@Slf4j
@Deprecated
public class CacheDAOImpl implements CacheDAO, GRNConstants {

    @Autowired
    private GRNConfig grnConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

//    @Override
//    @Logging
//    public QcConfig getQcMaster(GRNItem grnItem, boolean invoice) {
//        String key = CacheUtils.getMasterKey(grnConfig.getKeyPrefix(), grnItem);
//        TypeReference<Set<QCMaster>> typeRef = new TypeReference<Set<QCMaster>>() {};
//        ObjectMapper mapper = new ObjectMapper();
//        try {
//            Set<QCMaster> set = mapper.readValue(RedisHandler.redisOps(RedisOps.RANGEBYSCOREWITHLIMIT, key,
//                    new Double(grnItem.getInvoiceQuantity()), Double.MAX_VALUE, 0L, 1L).toString(), typeRef);
//            if(set.isEmpty()) {
//                set = mapper.readValue(RedisHandler.redisOps(RedisOps.REVRANGEBYSCOREWITHLIMIT, key,
//                        new Double(0), Double.MAX_VALUE, 0L, 1L).toString(), typeRef);
//                if(set.isEmpty())
//                    return null;
//            }
//            return invoice ? CacheUtils.getQcConfig(set, grnItem.getInvoiceQuantity()) :
//                        CacheUtils.getQcConfig(set, grnItem.getGrnEstimatedQuantity());
//        } catch (Exception ex) {
//            log.error("Unexpected Exception occurred : ", ex);
//            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//    }

//    @Logging
//    public QCGradient getQcGradient(GRNItem grnItem, QcConfig qcConfig) {
//        String key = CacheUtils.getGradientKey(grnConfig.getKeyPrefix(), grnItem.getCategoryId());
//        TypeReference<Set<QCGradient>> typeRef = new TypeReference<Set<QCGradient>>() {};
//        ObjectMapper mapper = new ObjectMapper();
//        try {
//            Set<QCGradient> set = mapper.readValue(RedisHandler.redisOps(RedisOps.RANGEBYSCOREWITHLIMIT, key,
//                    new Double(qcConfig.getSamplingPercent() + 1), Double.MAX_VALUE, 0L, 1L).toString(), typeRef);
//            if(set.isEmpty())
//                return null;
//            else
//                return set.iterator().next();
//        } catch (Exception ex) {
//            log.error("Unexpected Exception occurred : ", ex);
//            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//    }

    @Override
    @Logging
    public void loadConfig(GRNItem grnItem, boolean invoice) {
//        QcConfig qc = getConfig(grnItem, invoice);
//        if(qc == null)
//            throw new ApplicationException("QcConfig NotFound for this category", GRNExceptionStatus.GRN_NOT_FOUND);
        try {
            ObjectMapper mapper = new ObjectMapper();
            if(invoice)
//                RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(),
//                        grnItem.getInvoiceRefNum(), grnItem.getPid()), mapper.writeValueAsString(qc));
//            qc.setQuantity(grnItem.getGrnEstimatedQuantity());
//            long samplingQty = (long)Math.ceil(qc.getSamplingPercent() * grnItem.getGrnEstimatedQuantity() / 100.0);
//            qc.setSamplingQuantity(samplingQty);
//            long failureQty = (long)Math.ceil(qc.getSamplingQuantity() * qc.getFailurePercent() / 100.0);
//            qc.setFailureQuantity(failureQty);
//            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(),
//                    grnItem.getGrnCode(), grnItem.getPid()), mapper.writeValueAsString(qc));
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), PENDING);
//            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(),
//                    grnItem.getGrnCode(), grnItem.getPid()), grnItem.getGrnEstimatedQuantity());
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
        } catch (Exception ex) {
            log.error("Unexpected Exception occurred : ", ex);
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

//    @Logging
//    public QcConfig getConfig(GRNItem grnItem, boolean invoice) {
//        GRNItem grnItem1 = new GRNItem(grnItem);
//        QcConfig qc = getQcMaster(grnItem1, invoice);
//        if(qc == null) {
//            grnItem1.setPid("");
//            qc = getQcMaster(grnItem1, invoice);
//            if (qc == null) {
//                grnItem1.setVendorCode("");
//                qc = getQcMaster(grnItem1, invoice);
//                if (qc == null) {
//                    grnItem1.setBrand("");
//                    qc = getQcMaster(grnItem1, invoice);
//                }
//            }
//        }
//        return qc;
//    }

//    @Override
//    @Logging
//    public QcConfig getInvoiceQcConfig(String invoiceRefNum, String pid, String grnCode) {
//        if(!RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid)))
//            throw new ApplicationException("Invoice Config Key NotFound", GRNExceptionStatus.GRN_NOT_FOUND);
//        try {
//            wait(invoiceRefNum, pid, grnCode, GET_INVOICE_CONF); // acquiring lock
//            ObjectMapper mapper = new ObjectMapper();
//            QcConfig qcConfig = mapper.readValue(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getItemConfigKey(
//                    grnConfig.getKeyPrefix(), invoiceRefNum, pid)).toString(), QcConfig.class);
//            signal(invoiceRefNum, pid, grnCode, GET_INVOICE_CONF); // releasing lock
//            return qcConfig;
//        } catch (Exception ex) {
//            log.error("Unexpected Exception occurred : ", ex);
//            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//    }

//    @Override
//    @Logging
//    public QcConfig getGrnQcConfig(GRNItem grnItem) {
//        if(!RedisHandler.hasKey(CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), grnItem.getGrnCode(), grnItem.getPid())))
//            throw new ApplicationException("GRN Config Key NotFound", GRNExceptionStatus.GRN_NOT_FOUND);
//        try {
//            ObjectMapper mapper = new ObjectMapper();
//            return mapper.readValue(RedisHandler.redisOps(RedisOps.GET, CacheUtils.getGrnConfigKey(
//                    grnConfig.getKeyPrefix(), grnItem.getGrnCode(), grnItem.getPid())).toString(), QcConfig.class);
//        } catch (Exception ex) {
//            log.error("Unexpected Exception occurred : ", ex);
//            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//    }

//    @Override
//    @Logging
//    public boolean setNextGradientLevel(GRNItem grnItem) {
//        QcConfig qcConfig = getGrnQcConfig(grnItem);
//        QCGradient qcGradient = getQcGradient(grnItem, qcConfig);
//        if(qcGradient == null)
//            return false;
//        else{
//            ObjectMapper mapper = new ObjectMapper();
//            QcConfig invoiceQc = getInvoiceQcConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode());
//            QcConfig qcConfigNew = new QcConfig();
//            wait(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SET_NEXT_GRADIENT); // acquiring lock (releasing it after successful db operation or recovery)
//            if(invoiceQc.getGradientOrder() >= qcGradient.getOrder()){
//                qcConfigNew = invoiceQc;
//            } else {
//                qcConfigNew.setGradientOrder(qcGradient.getOrder());
//                qcConfigNew.setQuantity(grnItem.getInvoiceQuantity());
//                qcConfigNew.setSamplingPercent(qcGradient.getSamplingPercent());
//                qcConfigNew.setFailurePercent(qcGradient.getFailurePercent());
//                long samplingQty = (long)Math.ceil(grnItem.getInvoiceQuantity() * qcGradient.getSamplingPercent() / 100.0);
//                qcConfigNew.setSamplingQuantity(samplingQty);
//                long failureQty = (long)Math.ceil(qcConfigNew.getSamplingQuantity() * qcGradient.getFailurePercent() / 100.0);
//                qcConfigNew.setFailureQuantity(failureQty);
//            }
//            try {
//                RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(),
//                        grnItem.getInvoiceRefNum(), grnItem.getPid()), mapper.writeValueAsString(qcConfigNew));
//                qcConfigNew.setQuantity(qcConfig.getQuantity());
//                long samplingQty = (long)Math.ceil(qcConfigNew.getQuantity() * qcGradient.getSamplingPercent() / 100.0);
//                qcConfigNew.setSamplingQuantity(samplingQty);
//                long failureQty = (long)Math.ceil(qcConfigNew.getSamplingQuantity() * qcGradient.getFailurePercent() / 100.0);
//                qcConfigNew.setFailureQuantity(failureQty);
//                RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(),
//                        grnItem.getGrnCode(), grnItem.getPid()), mapper.writeValueAsString(qcConfigNew));
//            } catch (Exception ex) {
//                log.error("Unexpected Exception occurred : ", ex);
//                if(hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
//                        grnItem.getGrnCode().equals(getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
//                    signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SET_NEXT_GRADIENT_EX);
//                throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//            }
//        }
//        return true;
//    }

    @Override
    @Logging
    public void setConfigByEstimatedQty(GRNItem grnItem, QcConfig qc, int totalFailed, int totalScanned) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), mapper.writeValueAsString(qc));
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), String.valueOf(totalScanned));
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), String.valueOf(totalFailed));
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), PENDING);
//            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(),
//                    grnItem.getGrnCode(), grnItem.getPid()), grnItem.getGrnEstimatedQuantity());
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
        } catch (Exception ex) {
            log.error("Unexpected Exception occurred : ", ex);
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }


    public void initializeKeys(GRNItem grnItem, int totalFailed, int totalScanned) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), String.valueOf(totalScanned));
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), String.valueOf(totalFailed));
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), PENDING);
//            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllEstimatedQtyKey(grnConfig.getKeyPrefix(),
//                    grnItem.getGrnCode(), grnItem.getPid()), grnItem.getGrnEstimatedQuantity());
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllScannedKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getAllFailedKey(grnConfig.getKeyPrefix(),
                    grnItem.getGrnCode(), grnItem.getPid()), "0");
        } catch (Exception ex) {
            log.error("Unexpected Exception occurred : ", ex);
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    
    @Override
    @Logging
    public void initializeKeys(String grnCode, String pid) {
        try {
            ObjectMapper mapper = new ObjectMapper();
//            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), grnCode, pid),
//                    mapper.writeValueAsString(qc));
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), grnCode, pid),
                    "0");
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getFailCountKey(grnConfig.getKeyPrefix(), grnCode, pid),
                    "0");
            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getStatusKey(grnConfig.getKeyPrefix(), grnCode, pid),
                    PENDING);
        } catch (Exception ex) {
            log.error("Unexpected Exception occurred : ", ex);
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

//    @Logging
//    @Override
//    public void setInvoiceConfig(String invoiceRefNum, String pid, QcConfig oldInvoiceConfig) {
//        if(!RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid)))
//            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Invoice Config Key NotFound");
//        try {
//            ObjectMapper mapper = new ObjectMapper();
//            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(),
//                    invoiceRefNum, pid), mapper.writeValueAsString(oldInvoiceConfig));
//        } catch (Exception ex) {
//            log.error("Unexpected Exception occurred : ", ex);
//            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//    }

//    @Logging
//    @Override
//    public void setGrnConfig(String grnCode, String pid, QcConfig oldGrnConfig) {
//        if(!RedisHandler.hasKey(CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), grnCode, pid)))
//            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "GRN Config Key NotFound");
//        try {
//            ObjectMapper mapper = new ObjectMapper();
//            RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), grnCode, pid),
//                    mapper.writeValueAsString(oldGrnConfig));
//        } catch (Exception ex) {
//            log.error("Unexpected Exception occurred : ", ex);
//            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }
//    }

    @Logging
    @Override
    public ASNResponseDTO getASN(String poId, String vendorInvoiceNum, String barcode) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Object object = stringRedisTemplate.opsForHash().get(CacheUtils.getASNKey(grnConfig.getKeyPrefix(),
                    poId, vendorInvoiceNum), barcode);
            if(object == null)
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No information found for the barcode : " + barcode);
            return mapper.readValue(object.toString(), ASNResponseDTO.class);
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected Exception occurred : ", ex);
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    
    @Logging
    @Override
    public void setBoxRequired(String grnCode, String pid) {
    	
    	 try {
             RedisHandler.redisOps(RedisOps.SETVALUE, CacheUtils.getBoxRequiredKey(grnConfig.getKeyPrefix(), grnCode, pid), true);
         } catch (Exception ex) {
             throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
         }
    }
    
    @Logging
    @Override
    public boolean isBoxRequired(String grnCode, String pid) {
    	
    	 try {
    		 return RedisHandler.hasKey(CacheUtils.getBoxRequiredKey(grnConfig.getKeyPrefix(), grnCode, pid));
         } catch (Exception ex) {
             throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
         }
    }

    @Logging
    @Override
    public void wait(String invoiceRefNum, String pid, String grnCode, String methodName) {
        String invoiceLockKey = CacheUtils.getInvoiceLockKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid);
        try {
            Boolean flag = stringRedisTemplate.opsForValue().setIfAbsent(invoiceLockKey, grnCode, grnConfig.getLockTTL(), TimeUnit.MILLISECONDS);
            while(flag == null || !flag) {
                flag = stringRedisTemplate.opsForValue().setIfAbsent(invoiceLockKey, grnCode, grnConfig.getLockTTL(), TimeUnit.MILLISECONDS);
            }
            log.info("lock acquired by grn " + grnCode + " on " + methodName + methodName);
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public void signal(String invoiceRefNum, String pid, String grnCode, String methodName) {
        String invoiceLockKey = CacheUtils.getInvoiceLockKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid);
        try {
            RedisHandler.redisOps(RedisOps.DEL, invoiceLockKey);
            log.info("lock released by grn " + grnCode + " on " + methodName + "method");
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public String getLockOwnerGRN(String invoiceRefNum, String pid) {
        if(!RedisHandler.hasKey(CacheUtils.getInvoiceLockKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid)))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Invoice lock Key NotFound");
        try {
            return RedisHandler.redisOps(RedisOps.GET, CacheUtils.getInvoiceLockKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid)).toString();
        } catch (Exception ex) {
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public boolean hasLockKey(String invoiceRefNum, String pid) {
        return RedisHandler.hasKey(CacheUtils.getInvoiceLockKey(grnConfig.getKeyPrefix(), invoiceRefNum, pid));
    }
}
