package com.lenskart.nexs.grn.dao;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.grn.dto.request.GRNSearchDTO;
import com.lenskart.nexs.grn.dto.request.GRNUpdateDTO;
import com.lenskart.nexs.grn.dto.response.GRNDetailsDTO;
import com.lenskart.nexs.grn.model.GRNMaster;
import com.lenskart.nexs.grn.model.GRNMasterDetails;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.UnicomUnsyncedGrnCodeDetails;

public interface GRNMasterDAO {
    public boolean createGRN(GRNMaster grnMaster) throws Exception;

    public boolean createGRNMaster(GRNMaster grnMaster, Connection connection) throws Exception;

    public int updateGRN(GRNMaster grnMaster);

    public int updateGRNMaster(GRNMaster grnMaster, Connection connection);

    public int editGRN(GRNUpdateDTO grnUpdateDTO) throws Exception;

    public int editGRNMaster(GRNUpdateDTO grnUpdateDTO, Connection connection) throws Exception;

    public GRNMaster getGRNMaster(String grnCode, String userId) throws Exception;

    public int closeGRN(String grnCode, String userId, String facilityCode);

    public int closeGRNMaster(String grnCode, String userId, Connection connection);

//	public boolean updateEstimatedQty(String grnCode, List<PIDEstimatedQty> estimatedQty, List<PIDEstimatedQtyHistory> estimatedQtyList) throws Exception;

	public GRNMaster getGRNMaster(String grnCode) throws Exception;

    public GRNMaster getGRNMasterNP(String grnCode, String userId) throws Exception;

	List<GRNMaster> getGRNByInvoiceAndUser(String invoiceId, String user, boolean isSupervisor) throws Exception;

    public GRNMaster getGRNMasterSummary(String grnCode, String userId) throws Exception;

	List<GRNDetailsDTO> getGRNLISTByInvoiceAndUser(String invoiceId, String user) throws JsonProcessingException;

	int getNotClosedGRNCount(String invoiceId);

	List<GRNMasterDetails> searchGRN(GRNSearchDTO grnSearchReq, boolean isExportReq) throws JsonProcessingException;

	int countGRNListing(GRNSearchDTO grnSearchReq);

	Map<String, Boolean> checkClosedGRNByInvAndPid(String invoiceRefNum, List<String> pids);

    public String getTemplate(String templateId, int version);

    public List<GRNMaster> getClosedGRNs(String invoiceRefNum, String facility);

    public List<GrnMasterEntity> getOpenGRNListByInvoice(String invoiceRefNum, String facility);

    public List<String> getGRNByPOAndUser(String poNum);

    public String getGrnByInvoice(String invoiceId);

    public List<UnicomUnsyncedGrnCodeDetails> getUnsyncedGrn(String fetchQuery);

	Invoice getInvoiceById(String invoiceId);

	List<GRNDetailsDTO> getGRNByInvoiceId(String invoiceId);

}
