package com.lenskart.nexs.grn.dao;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.GRNPidBarcodeDetailsDTO;
import com.lenskart.nexs.grn.model.*;

import javax.persistence.Tuple;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public interface GRNItemDAO {
	
    public GrnItemEntity save(GRNItem grnItem, String oldStatus);

    public GRNItem getGrnItem(String barcode, String poId);

    public GrnItemEntity getGrnItemEntity(String barcode, String poId);

	public void saveGrnItemEntity(GrnItemEntity grnItemEntity);

	public void updateQcStatus(String barcode, String qcStatus, String poId);

	void updateGRNItem(UpdateGRNItemDTO updateGRNItemDTO, String oldStatus, long totalFailed, long failed, GRNItem grnItem);

	public GRNItem getGrnItemDetails(String barcode, String poId);

	public int deleteItem(String barcode, String poId);

	public Map<String, Integer> getTotalScanned(String grnCode, String[] pids);

	public Map<String, Integer> getTotalFailed(String grnCode, String[] pids);

	public Map<String, Integer> getCountByPIDStatus(String grnCode, List<String> pids, String qcFail);

	public Map<String, List<Map<String, Object>>> getQcPassBoxWithItems(String grnCode, String pid, boolean isBoxBarcodeRequired);

	public List<Map<String, Object>> getQcFailBoxWithItems(String grnCode, String pid);

	Map<String, Object> getGRNItemScannedCount(String grnCode, List<String> grnPids);

	public Map<String, Map<String, Object>> getMisc(String grnCode, List<String> grnPids);

	public Map<String, Timestamp> getFailedMap(String grnCode, List<String> grnPids);

	public int getBoxCount(String grnCode);

	List<Map<String, Object>> getTotalScanByInvoice(String invoiceId, String pid);

	Map<String, Object> getPIDTotalScanByInvoice(String invoiceId, String pid);

	Map<String, List<Map<String, Object>>> getQcPassItems(String grnCode, String pid);

	public List<BoxMapping> getEmptyBoxMappings(String grnCode);

	Map<String, Map<String, Object>> getGRNScanDetails(List<String> grns);

	public boolean releaseEmptyBoxes(List<BoxMapping> boxMappings, List<Box> boxes);

	List<GRNPidBarcodeDetailsDTO> getGrnPidBarcodeDetails(String grnCode);

    public Map<String, Object> getGRNScanCountByPID(String grnCode);

	List<PidItemCounter> getPidItemCount(List<String> invoiceRefNumbers, List<String> poIds);

	List<GrnItemPoInvoiceCount> getInvoiceCountForGrnPid(String grnCode, String invoiceRefNum, List<String> pids);

	List<GrnItemPoInvoiceCount> getPoCountForGrnPid(String grnCode, String poNum, List<String> pids);

	Map<String, Map<String, Object>> getGRNItemsScannedCount(List<GRNPIDMaster> grnPids);

	List<GrnPOInvoicePidData> getPidPOInvoiceForGrn(String grnCode);

	GrnItemEntity saveGrnItemDetails(GRNItem grnItem) throws Exception;

	GrnItemEntity convertGrnItemToGrnItemEntity(GRNItem grnItem);

	List<GrnItemPoInvoiceCount> getPoCountAndPriceForGrnPid(String grnCode,
															String invoiceRefNum,
															String poNum);
	public List<Tuple> updateQcStatusByGrnCode(String grncode, String qcStatus);

	public List<Tuple> getIqcGrnDetails(String grncode, String pid, List<String> boxcode);

	public String getVendorIdFromGrnCode(String grncode);

	List<Tuple> getGrnItemUsingBoxOrBarcode(String barcode, String grnCode, String pid);

	void updateQcStatusUsingGrnCode(String grnCode, String iqc_in_progress);

	GRNItem getTopGrnItemDetailForBarcodeAndFacility(String barcode, String facility);

	GRNItem getBarcodeDetails(String barcode, String facilityCode);

	List<String> getPutawayCodesByGrnCode(String grnCode);

	void updatePutawaySyncStatusByGrnCode(String grnCode);

	List<GrnItemEntity> getGrnItemsList(String grnCode);
}
