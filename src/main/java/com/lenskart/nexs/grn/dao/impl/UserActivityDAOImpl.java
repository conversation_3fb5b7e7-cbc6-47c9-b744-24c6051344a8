package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.UserActivityDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.UserActivity;

@Slf4j
@Primary
@Component(QualifierConstants.JDBC_USER_ACTIVITY_DAO)
@Deprecated
public class UserActivityDAOImpl implements UserActivityDAO, Queries, GRNConstants {

	@Autowired
    private DBConfig dbConfig;
	
	@Logging
	@Override
	public void assignGRN(UserActivity userActivity) {
		
		try (Connection con = dbConfig.getDataSource().getConnection();
	             PreparedStatement pst = con.prepareStatement(USER_ACTIVITY_ASSIGN_GRN)
	        ) {
			
			int i = 1;
			pst.setString(i++, userActivity.getUserId());
			pst.setString(i++, userActivity.getAction());
			pst.setString(i++, userActivity.getFacility());
			pst.setString(i++, userActivity.getGrnCode());
			pst.setString(i++, userActivity.getAssignedTo());
			pst.execute();
			
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

    @Override
    @Logging
    public void save(UserActivity userActivity) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(INSERT_INTO_USER_ACTIVITY)
        ) {
            int i = 1;
            pst.setString(i++, userActivity.getUserId());
            pst.setString(i++, userActivity.getAction());
            pst.setString(i++, userActivity.getFacility());
            pst.setString(i++, userActivity.getGrnCode());
            pst.setString(i++, userActivity.getAssignedTo());
            pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
            pst.execute();
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_BAD_GRAMMAR);
        }
    }

    @Logging
    @Override
    public void save(UserActivity userActivity, Connection connection) {
        log.info("Creating userActivity in user_activity table: {}",userActivity);
        try (PreparedStatement pst = connection.prepareStatement(INSERT_INTO_USER_ACTIVITY)
        ) {
            int i = 1;
            pst.setString(i++, userActivity.getUserId());
            pst.setString(i++, userActivity.getAction());
            pst.setString(i++, userActivity.getFacility());
            pst.setString(i++, userActivity.getGrnCode());
            pst.setString(i++, userActivity.getAssignedTo());
            pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
            pst.execute();
            log.info("Created userActivity in user_activity table: {}",userActivity);
        } catch (SQLException ex) {
            log.error("SQL Exception : while creating userActivity in user_activity table: {} - {}",userActivity, ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_BAD_GRAMMAR);
        }
    }
}
