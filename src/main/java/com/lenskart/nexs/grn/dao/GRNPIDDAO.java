package com.lenskart.nexs.grn.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.nexs.grn.dto.request.GRNPidListDTO;
import com.lenskart.nexs.grn.dto.request.GRNPidSearchDTO;
import com.lenskart.nexs.grn.dto.response.GRNProductResponse;
import com.lenskart.nexs.grn.dto.response.PidSearchResponse;
import com.lenskart.nexs.grn.model.GRNPIDMaster;

public interface GRNPIDDAO {

//	public boolean updatePidEstimatedQty(String grnCode, String pid, Long newEstimatedQty, List<Long> estimatedQtyHistory);

    public boolean createGRNPIDMaster(GRNPIDMaster grnpidMaster) throws JsonProcessingException;

    public int countManualOverride(String invoiceId, String pid) throws SQLException;

	public GRNPIDMaster getGRNPIDMaster(String grnCode, String pid);

	public void updateGRNPIDMasterStatus(String status, String grnCode, String pid);

    public List<GRNPIDMaster> getGRNPIDS(String grnCode);

	public List<GRNPIDMaster> getGRNPIDS(List<String> grnList);

	List<GRNPIDMaster> getGRNPIDDetails(List<String> grnCodes) throws Exception;

	Map<String, Boolean> checkFailedGRNPID(String invoiceRefNum, String pid);

	List<PidSearchResponse> grnPidSearch(GRNPidSearchDTO grnPidSearchReq);

	boolean setManualOverride(String grnCode, String pid, Boolean isEnabled);

	boolean updateManualOverrideFlag(List<GRNPidListDTO> grnPidList, boolean isAllowed);

	public GRNProductResponse getGRNProducts(String grnCode, int page, int pageSize);

	int countPids(GRNPidSearchDTO grnPidSearchReq, boolean countBlockedOrAlls);
}
