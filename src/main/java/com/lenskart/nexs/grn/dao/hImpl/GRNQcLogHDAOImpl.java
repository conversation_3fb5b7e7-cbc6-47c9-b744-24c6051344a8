package com.lenskart.nexs.grn.dao.hImpl;

import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnQcLogEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.GrnQcLogEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.GRNQcLogDAO;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNQcLog;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service(QualifierConstants.JPA_GRN_QC_LOG_DAO)
@Transactional(rollbackFor = Exception.class)
public class GRNQcLogHDAOImpl implements GRNQcLogDAO, Queries, GRNConstants {

    @Autowired
    private GrnQcLogEntityServiceImpl grnQcLogEntityService;

    @Logging
    @Override
    public void saveLog(GRNQcLog log) {
        try {
            GrnQcLogEntity grnQcLogEntity = new GrnQcLogEntity();
            int i = 1;
            grnQcLogEntity.setGrnCode(log.getGrnCode());
            grnQcLogEntity.setPid(log.getPid());
            grnQcLogEntity.setBarcode(log.getBarcode());
            grnQcLogEntity.setAction(log.getAction());
            grnQcLogEntity.setReason(log.getReason());
            grnQcLogEntity.setCreatedBy(MDC.get("USER_ID"));

            grnQcLogEntityService.saveOrUpdate(grnQcLogEntity);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }

    }

    @Logging
    @Override
    public List<Map<String, Object>> getGRNQcLogByGRNAndPid(String grnCode, String pid) {
        List<Map<String, Object>> result = null;
        try {
            List<GrnQcLogEntity> grnQcLogEntities =
                    grnQcLogEntityService.findByGrnCodeAndPidOrderByCreatedAtDesc(grnCode, pid);
            try {
                result = new ArrayList<>();
                for (GrnQcLogEntity grnQcLogEntity : grnQcLogEntities) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("barcode", grnQcLogEntity.getBarcode());
                    map.put("qc_reason", grnQcLogEntity.getReason());
                    result.add(map);
                }
                return result;
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
