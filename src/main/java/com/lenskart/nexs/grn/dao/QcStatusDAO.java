package com.lenskart.nexs.grn.dao;

import java.util.List;
import java.util.Map;

import com.lenskart.nexs.grn.dto.request.GRNPidListDTO;
import com.lenskart.nexs.grn.model.PidItemCounter;

public interface QcStatusDAO {

    public boolean hasReferenceKey(String grnCode, String pid);

    public boolean hasGrnKey(String grnCode, String pid);

    public long incrementScanCount(String grnCode, String pid);

    public long decrementScanCount(String grnCode, String pid);

    public long incrementFailCount(String grnCode, String pid);

    public long decrementFailCount(String grnCode, String pid);

    public long getTotalFailed(String grnCode, String pid);

    public long getTotalScanned(String grnCode, String pid);

    public boolean isChannelGreen(String grnCode, String pid);

    public void setGrnStatus(String grnCode, String pid, String status);

    public String getGrnStatus(String grnCode, String pid);

    public boolean isQcStatusFailed(String grnCode, String pid);

    public long incrementItemQtyCount(String itemId, String pid);

    public long incrementItemQtyCount(String itemId, String pid, boolean poCount);

    public long decrementItemQtyCount(String itemId, String pid);

    public long decrementItemQtyCount(String itemId, String pid, boolean poCount);

    public void manualOverrideGRN(String grnCode, String pid);

    public boolean isGRNManualOverriden(String grnCode, String pid);

    public void setTotalFailed(String grnCode, String pid, long totalFailed);

    public long getInvoiceQtyCount(String invoiceRefNum, String pid);

    public long incrementAllScanned(String grnCode, String pid);

    public long decrementAllScanned(String grnCode, String pid);

    public long incrementAllFailed(String grnCode, String pid);

    public long decrementAllFailed(String grnCode, String pid);

    public void setAllFailed(String grnCode, String pid, long failed);

    public long getAllFailed(String grnCode, String pid);

    public void setAllEstimatedQty(String grnCode, String pid, long allEstimatedQty);

    public long getAllEstimatedQty(String grnCode, String pid);

    public long getAllScanned(String grnCode, String pid);

	void setManualOverrideForList(List<GRNPidListDTO> grnPidList);

    void resetInvoiceQtyCount(PidItemCounter invoicePidCounter, Map<String, Long> counterMap);

    void deleteRedisCountKeyForInvoiceAndPO(String invoiceRefNum, String poId, String pid);
}
