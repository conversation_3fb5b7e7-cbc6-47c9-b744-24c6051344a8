package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lenskart.nexs.common.dbutil.CommonDbUtils;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.exception.ValidationException;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dto.request.GRNUpdateDTO;
import com.lenskart.nexs.grn.model.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.GRNSearchFields;
import com.lenskart.nexs.grn.dao.GRNMasterDAO;
import com.lenskart.nexs.grn.dao.UserActivityDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.GRNSearchDTO;
import com.lenskart.nexs.grn.dto.response.GRNDetailsDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;

@Primary
@Component(QualifierConstants.JDBC_GRN_MASTER_DAO)
@Slf4j
@Deprecated
public class GRNMasterDAOImpl implements GRNMasterDAO, Queries, GRNConstants, GRNSearchFields {

	@Autowired
	private DBConfig dbConfig;

	@Autowired
	private UserActivityDAO userActivityDAO;
	
	@Autowired
	private GRNConfig grnConfig;

	@Logging
	@Override
	public boolean createGRN(GRNMaster grnMaster) throws Exception {
		log.info("Creating grn for grnMaster: {}",grnMaster);
		try (Connection con = dbConfig.getDataSource().getConnection()) {
			con.setAutoCommit(false);
			if(createGRNMaster(grnMaster, con))
				userActivityDAO.save(new UserActivity(MDC.get("USER_ID"), CREATE, grnMaster.getFacility(),
						grnMaster.getGrnCode(), MDC.get("USER_ID")), con);
			con.commit();
			return true;
		} catch (SQLException ex) {
			log.error("SQL Exception :  while creating grn {} - {}",grnMaster, ex.getMessage());
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public boolean createGRNMaster(GRNMaster grnMaster, Connection connection) throws Exception {
		try (PreparedStatement pst = connection.prepareStatement(CREATE_GRN_MASTER)){
			int i = 1;
			ObjectMapper mapper = new ObjectMapper();
			pst.setString(i++, grnMaster.getGrnCode());
			pst.setString(i++, grnMaster.getUnicomGrnCode());
			pst.setString(i++, grnMaster.getPoId());
			pst.setString(i++, grnMaster.getInvoiceId());
			pst.setString(i++, mapper.writeValueAsString(grnMaster.getInvoice()));
			pst.setString(i++, mapper.writeValueAsString(grnMaster.getPo()));
			pst.setString(i++, GRN_STATUS_CREATED);
			pst.setInt(i++, 0);
			pst.setInt(i++, 0);
			pst.setString(i++, grnMaster.getCreatedBy());
			pst.setString(i++, grnMaster.getUpdatedBy());
			pst.setString(i++, grnMaster.getFacility());
			pst.execute();
			return true;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public int updateGRN(GRNMaster grnMaster) {
		try (Connection con = dbConfig.getDataSource().getConnection()) {
			con.setAutoCommit(false);
			int updateCount = updateGRNMaster(grnMaster, con);
			if(updateCount > 0)
				userActivityDAO.save(new UserActivity(MDC.get("USER_ID"), UPDATE,  grnMaster.getFacility(),
						grnMaster.getGrnCode(), MDC.get("USER_ID")), con);
			con.commit();
			return updateCount;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public int updateGRNMaster(GRNMaster grnMaster, Connection connection) {
		try (PreparedStatement pst = connection.prepareStatement(UPDATE_GRN_MASTER)
		) {
			int i = 1;
			pst.setString(i++, grnMaster.getGrnStatus());
			pst.setString(i++, grnMaster.getUpdatedBy());
			pst.setTimestamp(i++, grnMaster.getUpdatedAt());
			pst.setString(i++, grnMaster.getFacility());
			pst.setString(i++, grnMaster.getGrnCode());
			return pst.executeUpdate();
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public int editGRN(GRNUpdateDTO grnUpdateDTO) throws Exception {
		try (Connection con = dbConfig.getDataSource().getConnection()) {
			con.setAutoCommit(false);
			List<String> grnList = getGRNList(grnUpdateDTO.getInvoice().getInvoiceRefNum(), con);
			if (grnList == null)
				throw new ApplicationException("No grn in the system for invoice : " +
						grnUpdateDTO.getInvoice().getInvoiceRefNum(), GRNExceptionStatus.GRN_NOT_FOUND);
			int updateCount = editGRNMaster(grnUpdateDTO, con);
			if(updateCount > 0) {
				for( String grn : grnList)
					userActivityDAO.save(new UserActivity(MDC.get("USER_ID"), UPDATE, grnUpdateDTO.getFacilityCode(),
						grn, MDC.get("USER_ID")), con);

				int updatePidMasterCount = updateGRNPidMaster(grnUpdateDTO, con);
				if(updatePidMasterCount > 0)
					updateGRNItem(grnUpdateDTO, con);
			}
			con.commit();
			return updateCount;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private int updateGRNItem(GRNUpdateDTO grnUpdateDTO, Connection con) {
		try (PreparedStatement pst = con.prepareStatement(UPDATE_GRN_ITEMS)
		) {
			int i = 1;
			pst.setString(i++, grnUpdateDTO.getPo().getPoId());
			pst.setString(i++, grnUpdateDTO.getInvoice().getVendor());
			pst.setString(i++, grnUpdateDTO.getUpdatedBy());
			pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
			pst.setString(i++, grnUpdateDTO.getInvoice().getInvoiceRefNum());
			return pst.executeUpdate();
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private int updateGRNPidMaster(GRNUpdateDTO grnUpdateDTO, Connection con) {
		try (PreparedStatement pst = con.prepareStatement(UPDATE_GRN_PID_MASTER)
		) {
			int i = 1;
			pst.setString(i++, grnUpdateDTO.getInvoice().getInvoiceId());
			pst.setString(i++, grnUpdateDTO.getInvoice().getVendor());
			pst.setString(i++, grnUpdateDTO.getUpdatedBy());
			pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
			pst.setString(i++, grnUpdateDTO.getInvoice().getInvoiceRefNum());
			return pst.executeUpdate();
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	public List<String> getGRNList(String invoiceRefNum, Connection con) {
		try (PreparedStatement pst = con.prepareStatement(GET_GRN_LIST);)
		{
			int i = 1;
			pst.setString(i++, invoiceRefNum);
			List<String> grnList = null;
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next()) {
					if(grnList == null)
						grnList = new ArrayList<>();
					grnList.add(rs.getString("grn_code"));
				}
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grnList;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public int editGRNMaster(GRNUpdateDTO grnUpdateDTO, Connection connection) throws Exception {
		try (PreparedStatement pst = connection.prepareStatement(EDIT_GRN_MASTER)
		) {
			ObjectMapper mapper = new ObjectMapper();
			int i = 1;
			pst.setString(i++, mapper.writeValueAsString(grnUpdateDTO.getInvoice()));
			pst.setString(i++, mapper.writeValueAsString(grnUpdateDTO.getPo()));
			pst.setString(i++, grnUpdateDTO.getPo().getPoId());
			pst.setString(i++, grnUpdateDTO.getUpdatedBy());
			pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
			pst.setString(i++, grnUpdateDTO.getInvoice().getInvoiceRefNum());
			return pst.executeUpdate();
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public GRNMaster getGRNMaster(String grnCode, String userId) {
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(GET_GRN_MASTER, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);)
		{
			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, grnCode);
			GRNMaster grnMaster = null;
			try (ResultSet rs = pst.executeQuery();) {
				grnMaster = DBUtils.resultSetToMaster(rs);
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grnMaster;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public GRNMaster getGRNMasterSummary(String grnCode, String userId) {
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_GRN_SUMMARY);)
		{
			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, grnCode);
			GRNMaster grnMaster = null;
			try (ResultSet rs = pst.executeQuery();) {
				grnMaster = DBUtils.resultSetToMasterSummary(rs);
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grnMaster;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public int closeGRN(String grnCode, String userId, String facilityCode) {
		try (Connection con = dbConfig.getDataSource().getConnection()) {
			con.setAutoCommit(false);
			int updateCount = closeGRNMaster(grnCode, userId, con);
			if(updateCount > 0)
				userActivityDAO.save(new UserActivity(userId, CLOSE,  facilityCode, grnCode, userId), con);
			con.commit();
			return updateCount;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public int closeGRNMaster(String grnCode, String userId, Connection connection) {
		try (PreparedStatement pst = connection.prepareStatement(CLOSE_GRN_MASTER)
		) {
			int i = 1;
			pst.setString(i++, GRN_STATUS_CLOSED);
			pst.setString(i++, userId);
			pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
			pst.setString(i++, grnCode);
			return pst.executeUpdate();
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

//	@Logging
//	@Override
//	public boolean updateEstimatedQty(String grnCode, List<PIDEstimatedQty> estimatedQty, List<PIDEstimatedQtyHistory> estimatedQtyList) throws Exception {
//		try (Connection con = dbConfig.getDataSource().getConnection();
//			 PreparedStatement pst = con.prepareStatement(UPDATE_GRN_MASTER_FOR_ESTIMATED_QTY)) {
//			int i=1;
//			ObjectMapper mapper = new ObjectMapper();
//			pst.setString(i++, mapper.writeValueAsString(estimatedQty));
//			pst.setString(i++, mapper.writeValueAsString(estimatedQtyList));
//			pst.setString(i++, grnCode);
//			int rowCount = pst.executeUpdate();
//			if(rowCount > 0) {
//				return true;
//			}
//			return false;
//		} catch (SQLException ex) {
//			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//		}
//
//	}
	
	@Logging
	@Override
	public GRNMaster getGRNMaster(String grnCode) throws Exception {
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(GET_GRN, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);)
		{
			int i = 1;
			pst.setString(i++, grnCode);
			GRNMaster grnMaster = null;
			Map<String, Class> jsonFieldsTypesMap = new HashMap<>();
			jsonFieldsTypesMap.put("invoice", Invoice.class);
			jsonFieldsTypesMap.put("po", PurchaseOrder.class);
			try (ResultSet rs = pst.executeQuery();) {
				if (rs.first()) {
					grnMaster = (GRNMaster) DBUtils.resultsetToPojo(jsonFieldsTypesMap,rs,GRNMaster.class);
				}
			} catch (SQLException ex) {
				throw new ValidationException("SQL Exception : " + ex.getMessage());
			}
			return grnMaster;
		} catch (SQLException ex) {
			throw new ValidationException("SQL Exception : " + ex.getMessage());
		}
	}

	@Logging
	@Override
	public GRNMaster getGRNMasterNP(String grnCode, String userId) {
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_ONLY_GRN_MASTER);)
		{
			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, grnCode);
			GRNMaster grnMaster = null;
			try (ResultSet rs = pst.executeQuery();) {
				grnMaster = DBUtils.resultSetToMasterNP(rs);
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grnMaster;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
	
	@Logging
	@Override
	public List<GRNMaster> getGRNByInvoiceAndUser(String invoiceId, String user, boolean isSupervisor) throws Exception {
		
		List<GRNMaster> grns = null;
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(isSupervisor ? GET_GRN_BY_INVOICE : GET_GRN_BY_INVOICE_AND_USER, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);)
		{
			int i = 1;
			pst.setString(i++, invoiceId);
			if(!isSupervisor) {
				pst.setString(i++, user);
			}
			
			Map<String, Class> jsonFieldsTypesMap = new HashMap<>();
			jsonFieldsTypesMap.put("invoice", Invoice.class);
			jsonFieldsTypesMap.put("po", PurchaseOrder.class);
			try (ResultSet rs = pst.executeQuery();) {
				grns = new ArrayList<>();
				while (rs.next()) {
					GRNMaster grnMaster = (GRNMaster) DBUtils.resultsetToPojo(jsonFieldsTypesMap,rs,GRNMaster.class);
					grns.add(grnMaster);
				}
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grns;
			
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
	
	@Logging
	@Override
	public List<GRNDetailsDTO> getGRNLISTByInvoiceAndUser(String invoiceId, String user) throws JsonMappingException, JsonProcessingException {
		
		List<GRNDetailsDTO> grns = null;
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(GET_GRN_LIST_BY_INVOICE, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);)
		{
			int i = 1;
			pst.setString(i++, invoiceId);

			try (ResultSet rs = pst.executeQuery();) {
				grns = new ArrayList<>();
				while (rs.next()) {
					GRNDetailsDTO grnDetails = (GRNDetailsDTO) DBUtils.resultsetToPojo(null, rs, GRNDetailsDTO.class);
					grns.add(grnDetails);
				}
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grns;
			
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
	
	@Logging
	@Override
	public int getNotClosedGRNCount(String invoiceId) {

		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(GET_NOT_CLOSED_GRN_COUNT_BY_INVOICE, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);) {

			int i = 1;
			pst.setString(i++, invoiceId);

			try (ResultSet rs = pst.executeQuery();) {

				if (rs.first()) {
					return rs.getInt("grn_count");
				}
				return 0;

			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}

		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
	
	
	@Logging
	@Override
	public List<GRNMasterDetails> searchGRN(GRNSearchDTO grnSearchReq,boolean isExportReq) throws JsonMappingException, JsonProcessingException {

		List<GRNMasterDetails> grns = null;
		StringBuilder query = new StringBuilder();
		query.append(SEARCH_GRN);
		String criteria = buildGRNSearchCriteria(grnSearchReq);
		if (StringUtils.isNotBlank(criteria)) {
			query.append(" and " + criteria);
		}

		query.append(" group by gm.grn_code order by created_at DESC");
		
		Integer pageSize = grnConfig.getPageSize();
		if(isExportReq) {
			pageSize = grnConfig.getExportCSVSize();
		}
		Integer offset = grnSearchReq.getPage() * pageSize;
		query.append(" limit " + pageSize + " offset " + offset);

		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(query.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);) {

			Map<String, Class> jsonFieldsTypesMap = new HashMap<>();
			jsonFieldsTypesMap.put("invoice", Invoice.class);
			try (ResultSet rs = pst.executeQuery();) {
				grns = new ArrayList<>();
				while (rs.next()) {
					GRNMasterDetails grnMaster = (GRNMasterDetails) DBUtils.resultsetToPojo(jsonFieldsTypesMap, rs, GRNMasterDetails.class);
					grns.add(grnMaster);
				}

			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}

		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}

		return grns;
	}
	
	@Logging
	@Override
	public int countGRNListing(GRNSearchDTO grnSearchReq) {
		
		StringBuilder query = new StringBuilder();
		query.append(COUNT_GRN_LISTING);
		String criteria = buildGRNSearchCriteria(grnSearchReq);
		if (StringUtils.isNotBlank(criteria)) {
			query.append(" where " + criteria);
		}
		
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(query.toString());) {
			
			try (ResultSet rs = pst.executeQuery();) {
				
				rs.next();
				return rs.getInt("grn_count");
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}

	}
	
	@Logging
	@Override
	public Map<String, Boolean> checkClosedGRNByInvAndPid(String invoiceRefNum, List<String> pids) {
		
		Map<String, Boolean> result = new HashMap<>();
		StringBuilder build = new StringBuilder();
        build.append(CHECK_CLOSED_GRN_BY_INV_PID);
        for (String pid : pids) {
        	if(StringUtils.isNotBlank(pid)) {
        		build.append("?,");
        	}
            
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }

        build.append(") group by gp.pid");
        
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(build.toString(),  ResultSet.TYPE_SCROLL_INSENSITIVE,
		                ResultSet.CONCUR_UPDATABLE);) {
			
			int i = 1;
			pst.setString(i++, invoiceRefNum);
			for(String pid : pids) {
				if(StringUtils.isNotBlank(pid)) {
					pst.setString(i++, pid);
				}	
			}
			
			result.put("isGRNOpen", false);
			try (ResultSet rs = pst.executeQuery();) {
				
				if(rs.isBeforeFirst()) {
					result.put("isGRNOpen", true);
				}
				
				while (rs.next()) {
					result.put(rs.getString("pid"), true);
				}
				
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
		
		return result;
	}

	@Override
	public String getTemplate(String templateId, int version) {
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_TEMPLATE, ResultSet.TYPE_SCROLL_INSENSITIVE,
					 ResultSet.CONCUR_UPDATABLE);) {
			int i = 1;
			pst.setString(i++, templateId);
			pst.setInt(i++, version);
			try (ResultSet rs = pst.executeQuery();) {
				if (rs.first())
					return rs.getString("template");
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
		return null;
	}

	private String buildGRNSearchCriteria(GRNSearchDTO grnSearchReq) {

		StringBuilder build = new StringBuilder();

		if (StringUtils.isNotBlank(grnSearchReq.getGrnCode())) {
			build.append("gm."+GRN_CODE + " = " + "\"" + grnSearchReq.getGrnCode() + "\"");
			return build.toString();
		}

		build.append("gm."+FACILITY + " = " + "\"" + grnSearchReq.getFacility() + "\"");

		if (StringUtils.isNotBlank(grnSearchReq.getGrnStatus())) {
			build.append(" and ");
			build.append("gm."+GRN_STATUS + " = " + "\"" + grnSearchReq.getGrnStatus()+ "\"");
		}

		if (StringUtils.isNotBlank(grnSearchReq.getPo())) {
			build.append(" and ");
			build.append("gm."+PO + " = " + "\"" + grnSearchReq.getPo() + "\"");
		}

		if (StringUtils.isNotBlank(grnSearchReq.getInvoiceRefNumber())) {
			build.append(" and ");
			build.append("gm."+INVOICE_REF_NUM + " = " + "\"" + grnSearchReq.getInvoiceRefNumber() + "\"");
		}
		
		if (StringUtils.isNotBlank(grnSearchReq.getVendor())) {
			build.append(" and ");
			build.append("gp."+VENDOR + " = " + "\"" + grnSearchReq.getVendor() + "\"");
		}
		
		if (StringUtils.isNotBlank(grnSearchReq.getVendorInvoice())) {
			build.append(" and ");
			build.append("gp."+INVOICE_ID + " = " + "\"" + grnSearchReq.getVendorInvoice() + "\"");
		}

		if (StringUtils.isNotBlank(grnSearchReq.getCreatedBy())) {
			build.append(" and ");
			build.append("gm."+CREATED_BY + " = " + "\"" + grnSearchReq.getCreatedBy() + "\"");
		}

		return build.toString();
	}

	@Override
	public List<GRNMaster> getClosedGRNs(String invoiceRefNum, String facility) {
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_CLOSED_GRNS);) {
			int i = 1;
			pst.setString(i++, invoiceRefNum);
			pst.setString(i++, facility);
			List<GRNMaster> list = new ArrayList<>();
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next()) {
					GRNMaster grnMaster = new GRNMaster();
					grnMaster.setGrnCode(rs.getString("grn_code"));
					grnMaster.setUnicomGrnCode(rs.getString("unicom_grn_code"));
					list.add(grnMaster);
				}
				return list;
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public List<GrnMasterEntity> getOpenGRNListByInvoice(String invoiceRefNum, String facility) {
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_OPENED_GRNS);) {
			int i = 1;
			pst.setString(i++, invoiceRefNum);
			pst.setString(i++, facility);
			List<String> list = new ArrayList<>();
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next())
					list.add(rs.getString("grn_code"));
				return null;
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public List<String> getGRNByPOAndUser(String poNum) {
		List<String> grns = null;
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_GRN_BY_PO, ResultSet.TYPE_SCROLL_INSENSITIVE,
					 ResultSet.CONCUR_UPDATABLE);)
		{
			int i = 1;
			pst.setString(i++, poNum);
			try (ResultSet rs = pst.executeQuery();) {
				grns = new ArrayList<>();
				while (rs.next())
					grns.add(rs.getString("grn_code"));
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grns;

		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Override
	public String getGrnByInvoice(String invoiceRefNumber) {
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_GRN_LIST, ResultSet.TYPE_SCROLL_INSENSITIVE,
					 ResultSet.CONCUR_UPDATABLE);) {
			int i = 1;
			pst.setString(i++, invoiceRefNumber);
			String grnCodes = null;
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next())
					if(rs.isFirst())
						grnCodes=rs.getString("grn_code");
					else
						grnCodes+=","+rs.getString("grn_code");
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return grnCodes;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Override
	public Invoice getInvoiceById(String invoiceId) {
		Invoice invoice = null;
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(GET_INVOICE_BY_ID, ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);) {
			int i = 1;
			pst.setString(i++, invoiceId);
			
			Map<String, Class> jsonFieldsTypesMap = new HashMap<>();
			jsonFieldsTypesMap.put("invoice", Invoice.class);
			jsonFieldsTypesMap.put("po", PurchaseOrder.class);
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next()) {
					GRNMaster grnMaster = (GRNMaster) CommonDbUtils.resultsetToPojo(jsonFieldsTypesMap,rs,GRNMaster.class);
					invoice = grnMaster.getInvoice();
				}
			} catch (Exception ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return invoice;
			
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Override
	public List<UnicomUnsyncedGrnCodeDetails> getUnsyncedGrn(String fetchQuery) {
		log.info("Fetching unsynced grns to unicom");
		try (Connection con = dbConfig.getDataSource().getConnection();
			 PreparedStatement pst = con.prepareStatement(GET_UNSYNCED_GRN, ResultSet.TYPE_SCROLL_INSENSITIVE,
					 ResultSet.CONCUR_UPDATABLE);) {

			Map<String, String> grnCodeToFacilityCodeMap = new HashMap<>();
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next()) {
					grnCodeToFacilityCodeMap.put(rs.getString("grn_code"),rs.getString("facility"));
				}
				log.info("Fetched unsynced grns to unicom: {}", grnCodeToFacilityCodeMap);
			} catch (SQLException ex) {
				log.error("SQL Exception : Error while fetching unsynced grns to unicom: {}", ex.getMessage());
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return null;
		} catch (SQLException ex) {
			log.error("SQL Exception : Error while fetching unsynced grns to unicom: {}", ex.getMessage());
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Override
	public List<GRNDetailsDTO> getGRNByInvoiceId(String invoiceId) {
		// TODO Auto-generated method stub
		return null;
	}
}
