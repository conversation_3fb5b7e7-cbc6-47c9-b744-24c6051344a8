package com.lenskart.nexs.grn.dao.hImpl;

import com.lenskart.nexs.common.entity.entityServiceImpl.grn.UserAvtivityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.UserActivityEntity;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.UserActivityDAO;
import com.lenskart.nexs.grn.model.UserActivity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.time.Instant;

@Service(QualifierConstants.JPA_USER_ACTIVITY_DAO)
@Transactional(rollbackFor = Exception.class)
public class UserActivityHDAOImpl implements UserActivityDAO {
    @Autowired
    private UserAvtivityServiceImpl userAvtivityService;

    @Override
    public void assignGRN(UserActivity userActivity) {
        UserActivityEntity userActivityEntity = new UserActivityEntity();
        userActivityEntity.setUserId(userActivity.getUserId());
        userActivityEntity.setAction(userActivity.getAction());
        userActivityEntity.setFacility(userActivity.getFacility());
        userActivityEntity.setGrnCode(userActivity.getGrnCode());
        userActivityEntity.setAssignedTo( userActivity.getAssignedTo());
        userAvtivityService.saveOrUpdate(userActivityEntity);
    }

    @Override
    public void save(UserActivity userActivity) {
        UserActivityEntity  userActivityEntity = new UserActivityEntity();
        userActivityEntity.setUserId(userActivity.getUserId());
        userActivityEntity.setAction(userActivity.getAction());
        userActivityEntity.setFacility(userActivity.getFacility());
        userActivityEntity.setGrnCode(userActivity.getGrnCode());
        userActivityEntity.setAssignedTo( userActivity.getAssignedTo());
        userActivityEntity.setPerformedAt(Instant.now());
        userAvtivityService.saveOrUpdate(userActivityEntity);
    }

    @Override
    public void save(UserActivity userActivity, Connection connection) {
        UserActivityEntity  userActivityEntity = new UserActivityEntity();
        userActivityEntity.setUserId(userActivity.getUserId());
        userActivityEntity.setAction(userActivity.getAction());
        userActivityEntity.setFacility(userActivity.getFacility());
        userActivityEntity.setGrnCode(userActivity.getGrnCode());
        userActivityEntity.setAssignedTo( userActivity.getAssignedTo());
        userActivityEntity.setPerformedAt(Instant.now());
        userAvtivityService.saveOrUpdate(userActivityEntity);
    }
}
