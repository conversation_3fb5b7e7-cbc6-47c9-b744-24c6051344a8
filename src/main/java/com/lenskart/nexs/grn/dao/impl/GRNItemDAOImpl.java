package com.lenskart.nexs.grn.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.grn.model.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dao.GRNItemDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.GRNPidBarcodeDetailsDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.service.CacheUpdateService;

import lombok.extern.slf4j.Slf4j;

import javax.persistence.Tuple;

@Component
@Slf4j
@Deprecated
public class GRNItemDAOImpl implements GRNItemDAO, Queries, GRNConstants {

    @Autowired
    private DBConfig dbConfig;

    @Autowired
    private CacheDAO cacheDAO;

    @Autowired
    private CacheUpdateService cacheUpdateService;

    @Override
    @Logging
    public GrnItemEntity save(GRNItem grnItem, String oldStatus) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(INSERT_INTO_GRN)
        ) {
        	int i = 1;
            pst.setString(i++, grnItem.getBarcode());
            pst.setString(i++, grnItem.getGrnCode());
            pst.setString(i++, grnItem.getStatus());
            pst.setString(i++, grnItem.getPid());
            pst.setString(i++, grnItem.getPoId());
            pst.setString(i++, grnItem.getInvoiceRefNum());
            pst.setString(i++, grnItem.getVendorCode());
            pst.setTimestamp(i++, grnItem.getExpiryDate());
            pst.setString(i++, grnItem.getLotNo());
            pst.setLong(i++, grnItem.getGrnEstimatedQuantity());
            pst.setString(i++, grnItem.getQcPassBoxBarcode());
            pst.setString(i++, grnItem.getQcStatus());
            pst.setString(i++, grnItem.getQcFailCode());
            pst.setString(i++, grnItem.getQcReason());
            pst.setInt(i++, grnItem.getQcMasterSampling());
            pst.setInt(i++, grnItem.getQcGradientSampling());
            pst.setInt(i++, grnItem.getFailureThresholdQcMaster());
            pst.setInt(i++, grnItem.getFailureThresholdQcGradient());
            pst.setString(i++, grnItem.getCreatedBy());
            pst.setString(i++, grnItem.getUpdatedBy());
            pst.setString(i++, grnItem.getFacility());
            pst.setInt(i++, grnItem.getChannelStatus());
            pst.execute();
            if(cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SAVE);
        } catch (SQLException ex) {
            cacheUpdateService.updateCacheState(grnItem, oldStatus);
            if(cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SAVE_EX);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_BAD_GRAMMAR, ex.getCause());
        }
        return null;
    }

    @Logging
    @Override
    public GRNItem getGrnItem(String barcode, String poId) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_GRN_ITEM)
        ) {
            int i = 1;
            pst.setString(i++, barcode);
            try(ResultSet rs = pst.executeQuery()) {
                if(rs.next())
                    return new GRNItem(rs.getString("qc_status"));
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return null;
    }

    @Override
    public void saveGrnItemEntity(GrnItemEntity grnItemEntity) {

    }

    @Logging
	@Override
    public void updateQcStatus(String barcode, String qcStatus, String poId) {

		try (Connection con = dbConfig.getDataSource().getConnection(); PreparedStatement pst = con.prepareStatement(UPDATE_GRN_ITEM_QC_STATUS)) {
			int i = 1;
			pst.setString(i++, qcStatus);
			pst.setString(i++, barcode);
			pst.executeUpdate();
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	@Override
	public void updateGRNItem(UpdateGRNItemDTO updateGRNItemDTO, String oldStatus, long totalFailed, long failed, GRNItem grnItem) {
		try (Connection con = dbConfig.getDataSource().getConnection(); PreparedStatement pst = con.prepareStatement(UPDATE_GRN_ITEM_BOX_BARCODE)) {
			int i = 1;
			pst.setString(i++, updateGRNItemDTO.getQcStatus());
			pst.setString(i++, updateGRNItemDTO.getBoxBarcode());
			pst.setString(i++, updateGRNItemDTO.getQcFailCode());
			pst.setString(i++, updateGRNItemDTO.getQcReason());
			pst.setTimestamp(i++, updateGRNItemDTO.getExpiryDate());
			pst.setString(i++, updateGRNItemDTO.getBarcode());
			pst.executeUpdate();
            if(cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), UPDATE);
		} catch (SQLException ex) {
		    cacheUpdateService.updateRecovery(grnItem, oldStatus,totalFailed, failed);
            if(cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), UPDATE_EX);
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
    @Override
    public GRNItem getGrnItemDetails(String barcode, String poId) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GRN_ITEM_DETAIL)
        ) {
            int i = 1;
            pst.setString(i++, barcode);
            GRNItem grnItem = null;
            try(ResultSet rs = pst.executeQuery()) {
                if(rs.next()) {
                    grnItem = new GRNItem();
                    grnItem.setGrnCode(rs.getString("grn_code"));
                    grnItem.setPid(rs.getString("pid"));
                    grnItem.setPoId(rs.getString("po_id"));
                    grnItem.setVendorCode(rs.getString("vendor_code"));
                    grnItem.setLotNo(rs.getString("lot_no"));
                    grnItem.setQcMasterSampling(rs.getInt("qc_master_sampling"));
                    grnItem.setFailureThresholdQcMaster(rs.getInt("failure_threshold_qc_master"));
                    grnItem.setStatus(rs.getString("status"));
                    grnItem.setFacility(rs.getString("facility"));
                    grnItem.setGrnEstimatedQuantity(rs.getInt("estimated_qty"));
                    grnItem.setInvoiceRefNum(rs.getString("invoice_ref_num"));
                    grnItem.setQcPassBoxBarcode(rs.getString("qc_pass_box_barcode"));
                    grnItem.setBarcode(rs.getString("barcode"));
                    grnItem.setQcStatus(rs.getString("qc_status"));
                    grnItem.setQcFailCode(rs.getString("qc_fail_code"));
                    grnItem.setQcReason(rs.getString("qc_reason"));
                    grnItem.setExpiryDate(rs.getTimestamp("expiry_date"));
                    grnItem.setCreatedAt(rs.getTimestamp("created_at"));
                    grnItem.setUpdatedAt(rs.getTimestamp("updated_at"));
                    grnItem.setCreatedBy(rs.getString("created_by"));
                    grnItem.setUpdatedBy(rs.getString("updated_by"));
                    return grnItem;
                }
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return null;
    }

    @Logging
    @Override
    public int deleteItem(String barcode, String poId) {
        log.info("DELETE ITEM Barcode: {}", barcode);
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(DELETE_GRN_ITEM)
        ) {
            int i = 1;
            pst.setString(i++, barcode);
            return pst.executeUpdate();
        } catch (SQLException ex) {
            log.error("DELETE ITEM Barcode: {}, SQL Exception : {}", barcode, ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Integer> getTotalScanned(String grnCode, String[] pids) {
        StringBuilder build = new StringBuilder();
        build.append(GET_TOTAL_SCANNED);
        for (int n = 0; n < pids.length; n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
        build.append(") group by pid");
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(build.toString());)
        {
            int i = 1;
            Map<String, Integer> map = new HashMap<>();
            pst.setString(i++, grnCode);;
            for(String pid : pids)
                pst.setString(i++, pid);
            try (ResultSet rs = pst.executeQuery();) {
                while (rs.next()) {
                    map.put(rs.getString("pid"), rs.getInt("COUNT(1)"));
                }
                return map;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Integer> getTotalFailed(String grnCode, String[] pids) {
        StringBuilder build = new StringBuilder();
        build.append(GET_TOTAL_FAILED);
        for (int n = 0; n < pids.length; n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
        build.append(") group by pid");
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(build.toString());)
        {
            int i = 1;
            Map<String, Integer> map = new HashMap<>();
            pst.setString(i++, grnCode);
            for(String pid : pids)
                pst.setString(i++, pid);
            try (ResultSet rs = pst.executeQuery();) {
                while (rs.next()) {
                    map.put(rs.getString("pid"), rs.getInt("COUNT(1)"));
                }
                return map;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Integer> getCountByPIDStatus(String grnCode, List<String> pids, String status) {
        StringBuilder build = new StringBuilder();
        build.append(GET_GRN_PIDS);
        int n = 0;
        for (; n < pids.size(); n++)
            build.append("?,");
        if (build.length() > 0 && n > 0)
            build.deleteCharAt(build.lastIndexOf(","));

        build.append(") group by grn_code, pid");
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(build.toString());)
        {
            int i = 1;
            Map<String, Integer> map = new HashMap<>();
            pst.setString(i++, grnCode);
            pst.setString(i++, status);
            for(String pid : pids)
                pst.setString(i++, pid);
            try (ResultSet rs = pst.executeQuery();) {
                while (rs.next()) {
                    map.put(rs.getString("pid"), rs.getInt("COUNT(1)"));
                }
                return map;
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

	@Logging
	@Override
	public Map<String, List<Map<String, Object>>> getQcPassBoxWithItems(String grnCode, String pid, boolean isBoxBarcodeRequired) {
		Map<String, List<Map<String, Object>>> qcPassBoxWithItems = new LinkedHashMap<>();
        String query;
        if(isBoxBarcodeRequired)
            query = GET_PASS_BOX_WITH_ITEM;
        else
            query = GET_PASS_LOT_WITH_ITEM;
		try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(query)) {
			int i = 1;
			pst.setString(i++, grnCode);
			pst.setString(i++, pid);
			pst.setString(i++, grnCode);
			pst.setString(i++, pid);
			try (ResultSet rs = pst.executeQuery()) {

				while (rs.next()) {
                    String boxCode ;
                    if(isBoxBarcodeRequired)
                       boxCode = rs.getString("box_barcode");
                    else
                        boxCode = rs.getString("lot_no");
                    if(!qcPassBoxWithItems.containsKey(boxCode)) {
						List<Map<String, Object>> lst = new ArrayList<>();
						qcPassBoxWithItems.put(boxCode, lst);
					}
		
					if (rs.getString("barcode") != null) {
						Map<String, Object> map = new HashMap<>();
						map.put("barcode", rs.getString("barcode"));
						map.put("channel_status", rs.getInt("channel_status"));
						qcPassBoxWithItems.get(boxCode).add(map);
					}
				}
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
		return qcPassBoxWithItems;
	}

    @Logging
    @Override
    public List<Map<String, Object>> getQcFailBoxWithItems(String grnCode, String pid) {
        List<Map<String, Object>> qcFailBoxWithItems = null;
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_FAIL_BOX_WITH_ITEM)
        ) {
            int i = 1;
            pst.setString(i++, grnCode);
            pst.setString(i++, pid);
       
            try (ResultSet rs = pst.executeQuery()) {
            	qcFailBoxWithItems = new ArrayList<>();
                while (rs.next()) {
                	
                	Map<String, Object> map = new HashMap<>();
                    map.put("barcode", rs.getString("barcode"));
                    map.put("channel_status", rs.getInt("channel_status"));
                    map.put("qc_fail_code", rs.getString("qc_fail_code"));
                    map.put("qc_reason", rs.getString("qc_reason"));
                    
                    qcFailBoxWithItems.add(map);
                	
                }
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return qcFailBoxWithItems;
    }
    
    @Logging
    @Override
    public Map<String, Object> getGRNItemScannedCount(String grnCode, List<String> grnPids) {
    	Map<String, Object> result = new HashMap<>();
    	
    	StringBuilder build = new StringBuilder();
        build.append(GET_GRN_PID_SCANNED_COUNT);
        for (int n = 0; n < grnPids.size(); n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
    	
        build.append(") group by grn_code, pid");
    	try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);)
		{
    		int i = 1;
    		pst.setString(i++, grnCode);
    		
    		for(String pid : grnPids) {
    			pst.setString(i++, pid);
    		}
    		
    		
    		try (ResultSet rs = pst.executeQuery();) {
    			Map<String, Object> pidCountMap;
    			while(rs.next()) {
    				pidCountMap = new HashMap<>();
    				pidCountMap.put("total_count", rs.getInt("total_count"));
    				pidCountMap.put("fail_count", rs.getInt("fail_count"));
    				pidCountMap.put("sampling_percent", rs.getInt("sample"));
    				result.put(rs.getString("pid"), pidCountMap);
    			}
    			
    		} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
    		
    		return result;
    		
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
    }

    @Logging
    @Override
    public Map<String, Map<String, Object>> getMisc(String grnCode, List<String> grnPids) {
        Map<String, Map<String, Object>> result = new HashMap<>();

        StringBuilder build = new StringBuilder();
        build.append(GET_MISC);
        for (int n = 0; n < grnPids.size(); n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }

        build.append(") group by grn_code, pid, expiry_date, lot_no");
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            int i = 1;
            pst.setString(i++, grnCode);

            for(String pid : grnPids) {
                pst.setString(i++, pid);
            }
            try (ResultSet rs = pst.executeQuery();) {
                Map<String, Object> pidCountMap;
                while(rs.next()) {
                    pidCountMap = new HashMap<>();
                    pidCountMap.put("sampling_percent", rs.getInt("sample"));
                    pidCountMap.put("lot_no", rs.getString("lot_no"));
                    pidCountMap.put("expiry_date", rs.getTimestamp("expiry_date"));
                    result.put(rs.getString("pid"), pidCountMap);
                }

            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }

            return result;

        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Timestamp> getFailedMap(String grnCode, List<String> grnPids) {
        Map<String, Timestamp> result = new HashMap<>();
        StringBuilder build = new StringBuilder();
        build.append(GET_FAILED_ON);
        for (int n = 0; n < grnPids.size(); n++)
            build.append("?,");
        if (build.length() > 0)
            build.deleteCharAt(build.lastIndexOf(","));

        build.append(") group by grn_code, pid");
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            int i = 1;
            pst.setString(i++, grnCode);
            for(String pid : grnPids)
                pst.setString(i++, pid);
            try (ResultSet rs = pst.executeQuery();) {
                while(rs.next())
                    result.put(rs.getString("pid"), rs.getTimestamp("failed_on"));
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return result;
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public int getBoxCount(String grnCode) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_BOX_COUNT)
        ) {
            int i = 1;
            pst.setString(i++, grnCode);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next())
                    return rs.getInt("box_count");
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return 0;
    }
    
    @Logging
    @Override
	public List<Map<String, Object>> getTotalScanByInvoice(String invoiceId, String pid) {

		List<Map<String, Object>> result = null;
		StringBuilder build = new StringBuilder();
		build.append(GET_TOTAL_SCAN_COUNT_BY_INVOICE);

		if (StringUtils.isNotBlank(pid)) {
			build.append(" and gi.pid = ? group by gi.grn_code, gi.pid");
		} else {
			build.append(" group by gi.grn_code, gi.pid");
		}

		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);) {
			
			int i=1;
			pst.setString(i++, invoiceId);
			if(StringUtils.isNotBlank(pid)) {
				pst.setString(i++, pid);
			}
			
			try (ResultSet rs = pst.executeQuery();) {
				result = new ArrayList<>();
				while(rs.next()) {
					Map<String, Object> map = new HashMap<>();
					map.put("total_scanned", rs.getInt("total_scanned"));
					map.put("pid", rs.getString("pid"));
					map.put("grn_code", rs.getString("grn_code"));
					result.add(map);
				}
				
				return result;
			} catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
			

		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}

	}
    
    @Logging
    @Override
	public Map<String, Object> getPIDTotalScanByInvoice(String invoiceId, String pid) {

		Map<String, Object> result = null;
		StringBuilder build = new StringBuilder();
		build.append(GET_PID_TOTAL_SCAN_COUNT_BY_INVOICE);

		if (StringUtils.isNotBlank(pid)) {
			build.append(" and pid = ? group by pid");
		} else {
			build.append(" group by pid");
		}

		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);) {
			
			int i=1;
			pst.setString(i++, invoiceId);
			if(StringUtils.isNotBlank(pid)) {
				pst.setString(i++, pid);
			}
			
			try (ResultSet rs = pst.executeQuery();) {
				result = new HashMap<>();
				while(rs.next()) {
					result.put(rs.getString("pid"), rs.getInt("total_scanned"));
				}
				
				return result;
			} catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
			

		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
    
    @Logging
    @Override
    public Map<String, List<Map<String, Object>>> getQcPassItems(String grnCode, String pid) {
        Map<String, List<Map<String, Object>>> qcPassItems = new HashMap<>();
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_QC_PASS_ITEMS)
        ) {
            int i = 1;
            pst.setString(i++, grnCode);
            pst.setString(i++, pid);
            try (ResultSet rs = pst.executeQuery()) {
            	List<Map<String, Object>> qcPassItemList = new ArrayList<>();
            	
                while (rs.next()) {
                	
                	Map<String, Object> map = new HashMap<>();
                    map.put("barcode", rs.getString("barcode"));
                    map.put("channel_status", rs.getInt("channel_status"));
                    qcPassItemList.add(map);
                }
                qcPassItems.put("NO_BOX", qcPassItemList);
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return qcPassItems;
    }

    @Logging
    @Override
    public List<BoxMapping> getEmptyBoxMappings(String grnCode) {
        List<BoxMapping> list = new ArrayList<>();
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_EMPTY_BOXES)
        ) {
            int i = 1;
            pst.setString(i++, grnCode);
            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next())
                    list.add(new BoxMapping(rs.getString("barcode"), grnCode, rs.getString("pid")));
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Logging
    @Override
    public boolean releaseEmptyBoxes(List<BoxMapping> emptyBoxes, List<Box> boxes) {
        try (Connection con = dbConfig.getDataSource().getConnection();) {
            con.setAutoCommit(false);
            if(updateBox(emptyBoxes, con) > 0)
                saveBoxHistory(boxes, con);
            con.commit();
            return true;
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    public int updateBox(List<BoxMapping> emptyBoxes, Connection con) {
        StringBuilder build = new StringBuilder();
        build.append(SET_BOX_AVAILABLE);
        for (int n = 0; n < emptyBoxes.size(); n++)
            build.append("?,");
        if (build.length() > 0)
            build.deleteCharAt(build.lastIndexOf(","));

        build.append(")");
        try (PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
                ResultSet.CONCUR_UPDATABLE);)
        {
            int i = 1;
            pst.setString(i++, emptyBoxes.get(0).getCreatedBy());
            pst.setTimestamp(i++, new Timestamp(System.currentTimeMillis()));
            for(BoxMapping box : emptyBoxes)
                pst.setString(i++, box.getBarcode());
            return pst.executeUpdate();
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    
    @Logging
    @Override
    public Map<String,Map<String,Object>> getGRNScanDetails(List<String> grns) {

        Map<String, Map<String, Object>> result = new HashMap<>();
        StringBuilder build = new StringBuilder();
        build.append(GRN_SCAN_COUNT);
        for (int n = 0; n < grns.size(); n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }

        build.append(") group by grn_code");

        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);) {
            int i = 1;
            for (String grn : grns) {
                pst.setString(i++, grn);
            }

            try (ResultSet rs = pst.executeQuery();) {

                while (rs.next()) {
                    Map<String, Object> map = new HashMap<>();
                    map.put(TOTAL_SCANNED, rs.getInt("total_count"));
                    map.put(TOTAL_PASSED, rs.getInt("pass_count"));

                    result.put(rs.getString("grn_code"), map);
                }

            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }

        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }


        return result;
    }

    @Logging
    public boolean saveBoxHistory(List<Box> boxes, Connection con) {
        try (PreparedStatement pst = con.prepareStatement(INSERT_INTO_BOX_HISTORY)) {
            for(Box box : boxes) {
                int i = 1;
                pst.setString(i++, box.getBarcode());
                pst.setString(i++, box.getFacilityCode());
                pst.setString(i++, box.getLocation());
                pst.setString(i++, box.getStatus());
                pst.setBoolean(i++, box.isEnabled());
                pst.setString(i++, box.getCreatedBy());
                pst.setString(i++, box.getUpdatedBy());
                pst.execute();
            }
            return true;
        } catch (SQLIntegrityConstraintViolationException ex) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Box barcode already used");
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
    
    @Logging
	@Override
	public List<GRNPidBarcodeDetailsDTO> getGrnPidBarcodeDetails(String grnCode) {
        log.info("fetching Grn Pid Barcode Details for grnCode : {}", grnCode);
		List<GRNPidBarcodeDetailsDTO> result = null;
        Map<String,Object> pidMap = new HashMap<>();
        try (Connection con = dbConfig.getDataSource().getConnection();) {
            try (PreparedStatement pidStatement = con.prepareStatement(GET_GRN_PID_MASTER_DETAILS, ResultSet.TYPE_SCROLL_INSENSITIVE,
                    ResultSet.CONCUR_UPDATABLE);)
            {
                int i = 1;
                pidStatement.setString(i++, grnCode);
                Map<String, Object> pidCountMap;
                try (ResultSet rs = pidStatement.executeQuery();) {
                    while (rs.next()) {
                        pidCountMap = new HashMap<>();
                        pidCountMap.put("price", rs.getDouble("price"));
                        pidCountMap.put("tax_rate", rs.getDouble("tax_rate"));
                        pidMap.put(rs.getString("pid"), pidCountMap);
                    }
                }
            }
            PreparedStatement pst = con.prepareStatement(GET_GRN_PID_BARCODE_DETAILS, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
			int i = 1;
			pst.setString(i++, grnCode);
			try (ResultSet rs = pst.executeQuery();) {
				result = new ArrayList<>();
				while (rs.next()) {
					GRNPidBarcodeDetailsDTO grnPidBarcodeDetailsDTO = (GRNPidBarcodeDetailsDTO) DBUtils.resultsetToPojo(null, rs, GRNPidBarcodeDetailsDTO.class);
                    Map<String,Object> pidDetails = (Map<String, Object>) pidMap.get(grnPidBarcodeDetailsDTO.getPid());
                    grnPidBarcodeDetailsDTO.setPrice((Double) pidDetails.get("price"));
                    grnPidBarcodeDetailsDTO.setTaxRate((Double) pidDetails.get("tax_rate"));
                    result.add(grnPidBarcodeDetailsDTO);
				}
			} catch (Exception ex) {
			    log.error("SQL Exception : while fetching Grn Pid Barcode Details for grnCode : {} - {} ", grnCode, ex.getMessage());
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}

		} catch (Exception ex) {
            log.error("SQL Exception : while fetching Grn Pid Barcode Details for grnCode : {} - {} ", grnCode, ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}

        log.info("fetched Grn Pid Barcode Details for grnCode : {} - {}", grnCode, result);
        return result;
	}

    @Logging
    @Override
    public Map<String, Object> getGRNScanCountByPID(String grnCode) {
        Map<String, Object> result = new HashMap<>();
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_GRN_SCAN_COUNT_BY_PID, ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            int i = 1;
            pst.setString(i++, grnCode);
            try (ResultSet rs = pst.executeQuery();) {
                Map<String, Object> pidCountMap;
                while(rs.next()) {
                    pidCountMap = new HashMap<>();
                    pidCountMap.put("total_count", rs.getInt("total_count"));
                    pidCountMap.put("fail_count", rs.getInt("fail_count"));
                    result.put(rs.getString("pid"), pidCountMap);
                }
            } catch (SQLException ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return result;
        } catch (SQLException ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
 
	@Logging
    @Override
	public List<PidItemCounter> getPidItemCount(List<String> invoiceRefNumbers, List<String> poIds) {
		List<PidItemCounter> invoicePidCounters = new ArrayList<>();
		StringBuilder build = new StringBuilder();
		int count = 0;
		if (invoiceRefNumbers != null && !invoiceRefNumbers.isEmpty()) {
			count = invoiceRefNumbers.size();
			build.append(GET_INVOICE_PID_COUNT);
		} else {
			count = poIds.size();
			build.append(GET_PO_PID_COUNT);
		}
		for (int n = 0; n < count; n++)
			build.append("?,");
		if (build.length() > 0)
			build.deleteCharAt(build.lastIndexOf(","));
		if (invoiceRefNumbers != null && !invoiceRefNumbers.isEmpty())
			build.append(") group by invoice_ref_num, pid");
		else
			build.append(") group by po_id, pid");
		try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);) {
			int i = 1;
			if (invoiceRefNumbers != null && !invoiceRefNumbers.isEmpty())
				for (String invoiceRefNumber : invoiceRefNumbers)
					pst.setString(i++, invoiceRefNumber);
			else
				for (String poId : poIds)
					pst.setString(i++, poId);
			try (ResultSet rs = pst.executeQuery();) {
				while (rs.next()) {
					PidItemCounter invoicePidCounter = new PidItemCounter();
					invoicePidCounter.setInvoiceRefNum(rs.getString("item_id"));
					invoicePidCounter.setPid(rs.getString("pid"));
					invoicePidCounter.setCount(rs.getLong("count"));
					invoicePidCounters.add(invoicePidCounter);
				}
			} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(),
						GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			return invoicePidCounters;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

    @Override
    @Logging
    public List<GrnItemPoInvoiceCount> getInvoiceCountForGrnPid(String grnCode, String invoiceRefNum, List<String> pids) {
        log.info("GET_INVOICE_ITEM_COUNT_FOR_GRN Grn code {} ", grnCode);
        List<GrnItemPoInvoiceCount> list = new ArrayList<>();
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(Queries.GET_INVOICE_ITEM_COUNT_FOR_GRN, ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            pst.setString(1, invoiceRefNum);
            pst.setString(2, StringUtils.join(pids,","));
            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next())
                    list.add(new GrnItemPoInvoiceCount(grnCode, rs.getString("pid"), rs.getInt("scanned_quantity"),
                            null, rs.getInt("invoice_quantity")));
            } catch (SQLException ex) {
                log.error("GET_INVOICE_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
                throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            log.error("GET_INVOICE_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
            throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Override
    @Logging
    public List<GrnItemPoInvoiceCount> getPoCountForGrnPid(String grnCode, String invoiceRefNum, List<String> pids) {
        log.info("GET_PO_ITEM_COUNT_FOR_GRN Grn code {} ", grnCode);
        List<GrnItemPoInvoiceCount> list = new ArrayList<>();
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(Queries.GET_PO_ITEM_COUNT_FOR_GRN, ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            pst.setString(1, grnCode);
            pst.setString(2, StringUtils.join(pids,","));
            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next())
                    list.add(new GrnItemPoInvoiceCount(grnCode, rs.getString("pid"), rs.getInt("scanned_quantity"),
                            rs.getInt("po_quantity"), null));
            } catch (SQLException ex) {
                log.error("GET_PO_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
                throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            log.error("GET_PO_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
            throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Override
    @Logging
    public List<GrnPOInvoicePidData> getPidPOInvoiceForGrn(String grnCode) {
        log.info("GrnPOInvoicePidData Grn code {} ", grnCode);
        List<GrnPOInvoicePidData> list = new ArrayList<>();
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(Queries.GET_PID_PO_INVOICE_FOR_GRN, ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            int i = 1;
            pst.setString(i, grnCode);
            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next())
                    list.add(new GrnPOInvoicePidData(rs.getString("pid"), rs.getString("poId"), rs.getString("invoiceRefNum")));
            } catch (SQLException ex) {
                log.error("GrnPOInvoicePidData SQL Exception : " + ex.getMessage());
                throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (SQLException ex) {
            log.error("GrnPOInvoicePidData SQL Exception : " + ex.getMessage());
            throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Override
    public GrnItemEntity saveGrnItemDetails(GRNItem grnItem) throws Exception {
        return null;
    }

    @Override
    public GrnItemEntity convertGrnItemToGrnItemEntity(GRNItem grnItem) {
        return null;
    }

    @Override
    public List<GrnItemPoInvoiceCount> getPoCountAndPriceForGrnPid(String grnCode,
                                                                   String invoiceRefNum, String poNum) {
        return null;
    }

    @Override
    public List<Tuple> updateQcStatusByGrnCode(String grncode, String qcStatus) {
        return null;
    }

    @Override
	public Map<String, Map<String, Object>> getGRNItemsScannedCount(List<GRNPIDMaster> grnPids) {
		Map<String, Map<String, Object>> grnPidCounts = new HashMap<>();
    	StringBuilder build = new StringBuilder();
        build.append(GET_GRN_ITEMS_PID_SCANNED_COUNT);
        Set<String> uniquePids = new HashSet<>();
        Set<String> uniqueGrns = new HashSet<>();
        for (GRNPIDMaster grnPid : grnPids) {
        	uniquePids.add(grnPid.getPid());
        	uniqueGrns.add(grnPid.getGrnCode());
        }
        buildInQuery(build, uniqueGrns.size());
        build.append(") and pid in (");
        buildInQuery(build, uniquePids.size());
        build.append(") group by grn_code, pid");
    	try (Connection con = dbConfig.getDataSource().getConnection();
				PreparedStatement pst = con.prepareStatement(build.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE,
						ResultSet.CONCUR_UPDATABLE);) {
    		int i = 1;
    		for(String grn : uniqueGrns) {
    			pst.setString(i++, grn);
    		}
    		
    		for(String pid : uniquePids) {
    			pst.setString(i++, pid);
    		}
    		
    		Map<String, Object> pidMap;
			try (ResultSet rs = pst.executeQuery();) {
    			while(rs.next()) {
    				if (grnPidCounts.containsKey(rs.getString("grn_code")))
    					pidMap = grnPidCounts.get(rs.getString("grn_code"));
    				else {
    					pidMap = new HashMap<>();
    					grnPidCounts.put(rs.getString("grn_code"), pidMap);
    				}
    				Map<String, Object> pidCountMap = new HashMap<>();
    				pidCountMap.put("total_count", rs.getInt("total_count"));
    				pidCountMap.put("fail_count", rs.getInt("fail_count"));
    				pidCountMap.put("sampling_percent", rs.getInt("sample"));
    				pidMap.put(rs.getString("pid"), pidCountMap);
    			}
    			
    		} catch (SQLException ex) {
				throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
    		return grnPidCounts;
		} catch (SQLException ex) {
			throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void buildInQuery(StringBuilder build, int size) {
		for (int n = 0; n < size; n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
	}

    @Override
    public List<Tuple> getIqcGrnDetails(String grncode, String pid, List<String> boxcode) {
        return null;
    }

    @Override
    public String getVendorIdFromGrnCode(String grncode) {
        return null;
    }

    @Override
    public List<Tuple> getGrnItemUsingBoxOrBarcode(String barcode, String grncode, String pid) {
        return null;
    }

    @Override
    public void updateQcStatusUsingGrnCode(String grnCode, String iqc_in_progress) {

    }

    @Logging
    @Override
    public GRNItem getTopGrnItemDetailForBarcodeAndFacility(String barcode, String facility) {
        return null;
    }

    @Logging
    @Override
    public GRNItem getBarcodeDetails(String barcode, String facility) {
        return null;
    }

	@Override
	public GrnItemEntity getGrnItemEntity(String barcode, String poId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getPutawayCodesByGrnCode(String grnCode) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void updatePutawaySyncStatusByGrnCode(String grnCode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public List<GrnItemEntity> getGrnItemsList(String grnCode) {
		// TODO Auto-generated method stub
		return null;
	}
}
