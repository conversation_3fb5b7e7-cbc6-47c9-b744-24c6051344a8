package com.lenskart.nexs.grn.dao.impl;

import com.lenskart.nexs.common.dbutil.CommonDbUtils;
import com.lenskart.nexs.common.entity.po.grn.UserDetailEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.UserDetailsDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.UserDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;


@Primary
@Slf4j
@Component(QualifierConstants.JDBC_USER_DETAILS_DAO)
@Deprecated
public class UserDetailsDAOImpl implements UserDetailsDAO, Queries {

    @Autowired
    private DBConfig dbConfig;

    @Override
    @Logging
    public void addUserDetails(UserDetails userDetails) {
        try (Connection con = dbConfig.getDataSource().getConnection();
                PreparedStatement pst = con.prepareStatement(INSERT_USER_DETAILS)) {
            int i = 1;
            pst.setString(i++, userDetails.getEmpCode());
            pst.setString(i++, userDetails.getName());
            pst.setString(i++, userDetails.getPhoneCode());
            pst.setString(i++, userDetails.getPhoneNumber());
            pst.setString(i, userDetails.getEmail());
            pst.execute();
        } catch (SQLException ex) {
            log.info("SQL Exception in adding user details for user_details, "+userDetails+" "+ ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public void updateUserDetails(UserDetails userDetails) {
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(UPDATE_USER_DETAILS)) {
            int i = 1;
            pst.setString(i++, userDetails.getName());
            pst.setString(i++, userDetails.getPhoneCode());
            pst.setString(i++, userDetails.getPhoneNumber());
            pst.setString(i++, userDetails.getEmail());
            pst.setString(i, userDetails.getEmpCode());
            pst.execute();
        } catch (SQLException ex) {
            log.info("SQL Exception in updating user details for user_details, "+userDetails+" "+ ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public UserDetails getUserDetailsByEmpCode(String empCode) {
        UserDetails userDetails = null;
        try (Connection con = dbConfig.getDataSource().getConnection();
             PreparedStatement pst = con.prepareStatement(GET_USER_DETAILS_BY_EMP_CODE, ResultSet.TYPE_SCROLL_INSENSITIVE,
                     ResultSet.CONCUR_UPDATABLE);)
        {
            int i = 1;
            pst.setString(i, empCode);

            try (ResultSet rs = pst.executeQuery();) {
                while (rs.next()) {
                    userDetails = (UserDetails) CommonDbUtils.resultsetToPojo(null, rs, UserDetails.class);
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return userDetails;
        } catch (SQLException ex) {
            log.info("SQL Exception in fetching user details for empCode, "+empCode+" "+ ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public UserDetailEntity getUserDetailsByEmpCodeDb(String empCode){
        return null;
    }
}
