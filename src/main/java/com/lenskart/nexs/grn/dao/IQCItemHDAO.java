package com.lenskart.nexs.grn.dao;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.IqcGrnProductEntity;
import com.lenskart.nexs.common.entity.po.invoice.IqcInvoiceProductEntity;
import com.lenskart.nexs.putaway.model.response.CreatePutawayResponse;

import java.util.List;

public interface IQCItemHDAO {
    GrnItemEntity updateIQcStatus(String barcode, String qcStatus, String qcReason, String grnCode, String boxCode, String pid);

    IqcGrnProductEntity getIqcProductGrnDetails(String boxcode, String grnCode, String pid);

    void updatePutawayCodeInGrnItem(CreatePutawayResponse putawayResponse, String barcode);

//   / IqcGrnProductEntity getIQCGrnScanCount(String boxcode, String grnCode);

    List<IqcGrnProductEntity> findByGrnCode(String grnCode);

    GrnItemEntity getGrnItemDetails(String boxCode, String grnCode);

    IqcGrnProductEntity getIqcGrnDetails(String grnCode, String code, String qcPassBoxBarcode);

    IqcInvoiceProductEntity findByInvoiceRefNumber(int invoiceRefNum, int pid);
}
