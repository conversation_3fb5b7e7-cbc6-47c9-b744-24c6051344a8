package com.lenskart.nexs.grn.dao.hImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.dbutil.CommonDbUtils;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.BoxEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.BoxHistoryEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnMasterEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.BoxEntity;
import com.lenskart.nexs.common.entity.po.grn.BoxHistoryEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dao.GRNItemDAO;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.GRNPidBarcodeDetailsDTO;
import com.lenskart.nexs.grn.entity.GrnItemsArchivedEntity;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.ExceptionConstants;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.service.CacheUpdateService;
import com.lenskart.nexs.grn.service.impl.GrnItemsArchivedEntityServiceImpl;
import com.lenskart.nexs.grn.util.GRNItemServiceUtils;
import com.lenskart.nexs.grn.util.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.transform.ResultTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service(QualifierConstants.JPA_GRN_ITEM_DAO)
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class GRNItemHDAOImpl implements GRNItemDAO, Queries, GRNConstants {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private CacheDAO cacheDAO;

    @Autowired
    private CacheUpdateService cacheUpdateService;

    @Autowired
    private GrnItemEntityServiceImpl grnItemEntityService;

    @Autowired
    private GrnItemsArchivedEntityServiceImpl grnItemsArchivedEntityService;

    @Autowired
    private GrnMasterEntityServiceImpl grnMasterEntityService;

    @Autowired
    private BoxEntityServiceImpl boxEntityService;

    @Autowired
    private BoxHistoryEntityServiceImpl boxHistoryEntityService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Logging
    public GrnItemEntity save(GRNItem grnItem, String oldStatus) {
        try {
            GrnItemEntity grnItemEntity = saveGrnItem(grnItem);
            if (cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SAVE);
            return grnItemEntity;
        } catch (SQLException ex) {
            cacheUpdateService.updateCacheState(grnItem, oldStatus);
            if (cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SAVE_EX);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_BAD_GRAMMAR,
                    ex.getCause());
        }
    }

    private GrnItemEntity saveGrnItem(GRNItem grnItem) throws SQLException {
        log.info("[saveGrnItem] barcode {}, grnItem {}", grnItem.getBarcode(), grnItem);
        GrnMasterEntity grnMasterEntity = grnMasterEntityService.findByGrnCode(grnItem.getGrnCode());
        if (grnMasterEntity == null) {
            log.error("GRN not found {}", grnItem.getGrnCode());
            throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
        grnItem.setGrnCode(grnMasterEntity.getGrnCode());

        GrnItemEntity grnItemEntity = convertGrnItemToGrnItemEntity(grnItem);
        log.info("[saveGrnItem] barcode {}, grnItemEntity {}", grnItem.getBarcode(), grnItemEntity);
        saveGrnItemEntity(grnItemEntity);
        return grnItemEntity;
    }

    @Override
    public GrnItemEntity convertGrnItemToGrnItemEntity(GRNItem grnItem) {
        log.info("[convertGrnItemToGrnItemEntity] barcode {}, grnItem {}", grnItem.getBarcode(), grnItem);
        GrnItemEntity grnItemEntity = new GrnItemEntity();
        grnItemEntity.setBarcode(grnItem.getBarcode());
        grnItemEntity.setGrnCode(grnItem.getGrnCode());
        grnItemEntity.setStatus(grnItem.getStatus() == null ? GRNConstants.PASSED : grnItem.getStatus());
        grnItemEntity.setPid(grnItem.getPid());
        grnItemEntity.setPoId(grnItem.getPoId());
        grnItemEntity.setInvoiceRefNum(grnItem.getInvoiceRefNum());
        grnItemEntity.setVendorCode(grnItem.getVendorCode());
        log.info("[saveGrnItem] Barcode {}, expiry date {}", grnItem.getBarcode(), grnItem.getExpiryDate());
        if(grnItem.getExpiryDate() != null)
            grnItemEntity.setExpiryDate(new Timestamp(grnItem.getExpiryDate().getTime()));
        grnItemEntity.setLotNo(grnItem.getLotNo());
        grnItemEntity.setEstimatedQty(grnItem.getGrnEstimatedQuantity());
        grnItemEntity.setQcPassBoxBarcode(grnItem.getQcPassBoxBarcode());
        grnItemEntity.setQcStatus("");
        grnItemEntity.setQcFailCode(grnItem.getQcFailCode());
        grnItemEntity.setQcReason(grnItem.getQcReason());
        grnItemEntity.setQcMasterSampling(grnItem.getQcMasterSampling());
        grnItemEntity.setQcGradientSampling(grnItem.getQcGradientSampling());
        grnItemEntity.setFailureThresholdQcMaster(grnItem.getFailureThresholdQcMaster());
        grnItemEntity.setFailureThresholdQcGradient(grnItem.getFailureThresholdQcGradient());
        grnItemEntity.setCreatedBy(grnItem.getCreatedBy());
        grnItemEntity.setUpdatedBy(grnItem.getUpdatedBy());
        grnItemEntity.setFacility(grnItem.getFacility());
        grnItemEntity.setChannelStatus(grnItem.getChannelStatus());
        grnItemEntity.setPutawayCode(grnItem.getPutawayCode());
        grnItemEntity.setImsSyncStatus(grnItem.getImsSyncStatus());
        grnItemEntity.setPutawaySyncStatus(grnItem.getPutawaySyncStatus());
        grnItemEntity.setLegalOwner(grnItem.getLegalOwner());
        return grnItemEntity;
    }

    @Logging
    @Override
    public void saveGrnItemEntity(GrnItemEntity grnItemEntity) {
        log.info("[saveGrnItemEntity] barcode {}, grnItemEntity {}", grnItemEntity.getBarcode(), grnItemEntity);
        grnItemEntityService.saveOrUpdate(grnItemEntity);
    }

    @Logging
    @Override
    public GRNItem getGrnItem(String barcode, String poId) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndPoId(barcode, poId);
            if (grnItemEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            return new GRNItem(grnItemEntity.getQcStatus());
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public GrnItemEntity getGrnItemEntity(String barcode, String poId) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndPoId(barcode, poId);
            return grnItemEntity;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public void updateQcStatus(String barcode, String qcStatus, String poId) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndPoId(barcode, poId);
            if (grnItemEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            grnItemEntity.setQcStatus(qcStatus);
            saveGrnItemEntity(grnItemEntity);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public void updateGRNItem(UpdateGRNItemDTO updateGRNItemDTO, String oldStatus, long totalFailed, long failed, GRNItem grnItem) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndPoId(updateGRNItemDTO.getBarcode(), grnItem.getPoId());
            if (grnItemEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            grnItemEntity.setQcStatus(updateGRNItemDTO.getQcStatus());
            grnItemEntity.setQcPassBoxBarcode(updateGRNItemDTO.getBoxBarcode());
            grnItemEntity.setQcFailCode(updateGRNItemDTO.getQcFailCode());
            grnItemEntity.setQcReason(updateGRNItemDTO.getQcReason());
            if(updateGRNItemDTO.getExpiryDate()!=null)
                grnItemEntity.setExpiryDate(updateGRNItemDTO.getExpiryDate());
            saveGrnItemEntity(grnItemEntity);
            if (cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), UPDATE);
        } catch (Exception ex) {
            cacheUpdateService.updateRecovery(grnItem, oldStatus, totalFailed, failed);
            if (cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()) &&
                    grnItem.getGrnCode().equals(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())))
                cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), UPDATE_EX);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public GRNItem getGrnItemDetails(String barcode, String poId) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndPoId(barcode, poId);
            if (grnItemEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            GRNItem grnItem = GRNItemServiceUtils.buildGrnItem(grnItemEntity);
            return grnItem;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    

    @Logging
    @Override
    public int deleteItem(String barcode, String poId) {
        try {
            log.info("[deleteItem] barcode {}, poId {}", barcode, poId);
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndPoId(barcode, poId);
            if (grnItemEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            log.info("[deleteItem] barcode {}, poId {}, grnItemEntity {}", barcode, poId, grnItemEntity.toString());
            grnItemEntityService.delete(grnItemEntity);
            return 1;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Integer> getTotalScanned(String grnCode, String[] pids) {
        StringBuilder query = new StringBuilder();
        query.append(GET_TOTAL_SCANNED);
        for (int n = 0; n < pids.length; n++) {
            query.append("?,");
        }
        if (query.length() > 0) {
            query.deleteCharAt(query.lastIndexOf(","));
        }
        query.append(") group by pid");
        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString());
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            Map<String, Integer> map = new HashMap<>();
            for (String pid : pids)
                nativeQuery.setParameter(i++, pid);
            try {
                List<Object[]> resultList = nativeQuery.getResultList();
                for (Object[] objects : resultList) {
                    map.put(String.valueOf(objects[0]), (int) objects[1]);
                }
                return map;
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Integer> getTotalFailed(String grnCode, String[] pids) {
        StringBuilder query = new StringBuilder();
        query.append(GET_TOTAL_FAILED);
        for (int n = 0; n < pids.length; n++) {
            query.append("?,");
        }
        if (query.length() > 0) {
            query.deleteCharAt(query.lastIndexOf(","));
        }
        query.append(") group by pid");
        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString());
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            for (String pid : pids)
                nativeQuery.setParameter(i++, pid);
            Map<String, Integer> map = new HashMap<>();
            try {
                List<Object[]> resultList = nativeQuery.getResultList();
                for (Object[] objects : resultList) {
                    map.put(String.valueOf(objects[0]), (int) objects[1]);
                }
                return map;
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Integer> getCountByPIDStatus(String grnCode, List<String> pids, String status) {
        StringBuilder query = new StringBuilder();
        query.append(GET_GRN_PIDS);
        int n = 0;
        for (; n < pids.size(); n++)
            query.append("?,");
        if (query.length() > 0 && n > 0)
            query.deleteCharAt(query.lastIndexOf(","));

        query.append(") group by grn_code, pid");
        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString());
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            Map<String, Integer> map = new HashMap<>();
            nativeQuery.setParameter(i++, grnCode);
            nativeQuery.setParameter(i++, status);
            for (String pid : pids)
                nativeQuery.setParameter(i++, pid);
            try {
                List<Object[]> resultList = nativeQuery.getResultList();
                for (Object[] objects : resultList) {
                    map.put(String.valueOf(objects[0]), (int) objects[1]);
                }
                return map;
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, List<Map<String, Object>>> getQcPassBoxWithItems(String grnCode, String pid, boolean isBoxBarcodeRequired) {
        Map<String, List<Map<String, Object>>> qcPassBoxWithItems = new LinkedHashMap<>();
        try {
            String query;
            Query nativeQuery;
            int i = 1;
            if(isBoxBarcodeRequired) {
                query = GET_PASS_BOX_WITH_ITEM;
                nativeQuery = entityManager.createNativeQuery(query,Tuple.class);
                nativeQuery.setParameter(i++, grnCode);
                nativeQuery.setParameter(i++, pid);
                nativeQuery.setParameter(i++, grnCode);
                nativeQuery.setParameter(i++, pid);
            } else {
                query = GET_PASS_LOT_WITH_ITEM;
                nativeQuery = entityManager.createNativeQuery(query,Tuple.class);
                nativeQuery.setParameter(i++, grnCode);
                nativeQuery.setParameter(i++, pid);
            }

            List<Tuple> resultList = nativeQuery.getResultList();
            try {
                for (Tuple tuple : resultList) {
                    String boxCode ;
                    if(isBoxBarcodeRequired)
                        boxCode = tuple.get("box_barcode",String.class);
                    else
                        boxCode = tuple.get("lot_no",String.class);
                    if (!qcPassBoxWithItems.containsKey(boxCode)) {
                        List<Map<String, Object>> lst = new ArrayList<>();
                        qcPassBoxWithItems.put(boxCode, lst);
                    }
                    String barCode = tuple.get("barcode",String.class);
                    if (barCode != null) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("barcode", barCode);
                        map.put("channel_status", tuple.get("channel_status",Integer.class));
                        Timestamp expiryDateTs = tuple.get("expiry_date", Timestamp.class);
                        if(expiryDateTs != null){
                            map.put("expiry_date", expiryDateTs.toString().substring(0,10));
                        }
                        qcPassBoxWithItems.get(boxCode).add(map);
                    }
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return qcPassBoxWithItems;
    }

    @Logging
    @Override
    public List<Map<String, Object>> getQcFailBoxWithItems(String grnCode, String pid) {
        List<Map<String, Object>> qcFailBoxWithItems = null;
        try {
            List<GrnItemEntity> grnItemEntities =
                    grnItemEntityService.findFailBarcodeByGrnCodeAndPidAndQcStatusNotOrderByCreatedAtDesc(grnCode, pid, QC_FAIL);
            try {
                qcFailBoxWithItems = new ArrayList<>();
                List<Map<String, Object>> finalQcFailBoxWithItems = qcFailBoxWithItems;
                grnItemEntities.forEach(grnItemEntity -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("barcode", grnItemEntity.getBarcode());
                    map.put("channel_status", grnItemEntity.getChannelStatus());
                    map.put("qc_fail_code", grnItemEntity.getQcFailCode());
                    map.put("qc_reason", grnItemEntity.getQcReason());
                    finalQcFailBoxWithItems.add(map);
                });
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return qcFailBoxWithItems;
    }

    @Logging
    @Override
    public Map<String, Object> getGRNItemScannedCount(String grnCode, List<String> grnPids) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Tuple> resultList = grnItemEntityService.getCountByGrnCodeAndPidIn(grnCode, grnPids);
            try {
                Map<String, Object> pidCountMap;
                for (Tuple tuple : resultList) {
                    pidCountMap = new HashMap<>();
                    pidCountMap.put("total_count", tuple.get("total_count"));
                    pidCountMap.put("fail_count", tuple.get("fail_count"));
                    pidCountMap.put("sampling_percent", tuple.get("sample"));
                    result.put(String.valueOf(tuple.get("pid")), pidCountMap);
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return result;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Map<String, Object>> getMisc(String grnCode, List<String> grnPids) {
        Map<String, Map<String, Object>> result = new HashMap<>();

        StringBuilder build = new StringBuilder();
        build.append(GET_MISC);
        for (int n = 0; n < grnPids.size(); n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
        build.append(") group by grn_code, pid, expiry_date, lot_no");
        try {
            Query nativeQuery = entityManager.createNativeQuery(build.toString(), Tuple.class);
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            for (String pid : grnPids) {
                nativeQuery.setParameter(i++, pid);
            }
            List<Tuple> resultList = nativeQuery.getResultList();
            try {
                Map<String, Object> pidCountMap;
                for (Tuple tuple : resultList) {
                    pidCountMap = new HashMap<>();
                    pidCountMap.put("sampling_percent", tuple.get("sample"));
                    pidCountMap.put("lot_no", tuple.get("lot_no"));
                    pidCountMap.put("expiry_date", tuple.get("expiry_date"));
                    result.put(tuple.get("pid", String.class), pidCountMap);
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return result;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Timestamp> getFailedMap(String grnCode, List<String> grnPids) {
        Map<String, Timestamp> result = new HashMap<>();
        StringBuilder build = new StringBuilder();
        build.append(GET_FAILED_ON);
        for (int n = 0; n < grnPids.size(); n++)
            build.append("?,");
        if (build.length() > 0)
            build.deleteCharAt(build.lastIndexOf(","));

        build.append(") group by grn_code, pid");
        try {
            Query nativeQuery = entityManager.createNativeQuery(build.toString(), Tuple.class);
            int i = 1;
            nativeQuery.setParameter(i++, grnCode);
            for (String pid : grnPids) {
                nativeQuery.setParameter(i++, pid);
            }
            List<Tuple> resultList = nativeQuery.getResultList();
            resultList.forEach(tuple -> {
                result.put(tuple.get("pid", String.class), tuple.get("failed_on",
                        Timestamp.class));
            });
            return result;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public int getBoxCount(String grnCode) {
        try {
            return grnItemEntityService.findDistinctCountGrnCodeAndQcPassBoxBarcodeIsNotNull(grnCode);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public List<Map<String, Object>> getTotalScanByInvoice(String invoiceId, String pid) {
        List<Map<String, Object>> result = null;
        StringBuilder build = new StringBuilder();
        build.append(GET_TOTAL_SCAN_COUNT_BY_INVOICE);
        if (StringUtils.isNotBlank(pid)) {
            build.append(" and gi.pid = ? group by gi.grn_code, gi.pid");
        } else {
            build.append(" group by gi.grn_code, gi.pid");
        }

        try {
            Query nativeQuery = entityManager.createNativeQuery(build.toString(), Tuple.class);
            int i = 1;
            nativeQuery.setParameter(i++, invoiceId);
            if (StringUtils.isNotBlank(pid)) {
                nativeQuery.setParameter(i++, pid);
            }
            List<Tuple> resultList = nativeQuery.getResultList();
            try {
                result = new ArrayList<>();
                for (Tuple tuple : resultList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("total_scanned", tuple.get("total_scanned"));
                    map.put("pid", tuple.get("pid"));
                    map.put("grn_code", tuple.get("grn_code"));
                    result.add(map);
                }
                return result;
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, Object> getPIDTotalScanByInvoice(String invoiceId, String pid) {

        Map<String, Object> result = null;
        List<Tuple> grnItemEntityList = null;
        if (StringUtils.isNotBlank(pid)) {
            grnItemEntityList = grnItemEntityService.findByInvoiceRefNumAndPidGroupByPid(invoiceId, pid);
        } else {
            grnItemEntityList = grnItemEntityService.getInvoiceRefNumGroupByPid(invoiceId);
        }
        try {
            result = new HashMap<>();
            Map<String, Object> finalResult = result;
            grnItemEntityList.forEach(tuple -> {
                finalResult.put(String.valueOf(tuple.get("pid")), tuple.get("total_scanned"));
            });
            result = finalResult;
            return result;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public Map<String, List<Map<String, Object>>> getQcPassItems(String grnCode, String pid) {
        Map<String, List<Map<String, Object>>> qcPassItems = new HashMap<>();
        try {
            List<GrnItemEntity> grnItemEntities =
                    grnItemEntityService.findByGrnCodeAndPidAndQcStatusOrderByCreatedAtDesc(grnCode, pid, "");
            List<Map<String, Object>> qcPassItemList = new ArrayList<>();
            for (GrnItemEntity grnItemEntity : grnItemEntities) {
                Map<String, Object> map = new HashMap<>();
                map.put("barcode", grnItemEntity.getBarcode());
                map.put("channel_status", grnItemEntity.getChannelStatus());
                map.put("expiry_date", grnItemEntity.getExpiryDate());
                qcPassItemList.add(map);
            }
            log.info("[getQcPassItems] qcPassItemList {}", qcPassItemList);
            qcPassItems.put("NO_BOX", qcPassItemList);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return qcPassItems;
    }

    @Logging
    @Override
    public List<BoxMapping> getEmptyBoxMappings(String grnCode) {
        List<BoxMapping> list = new ArrayList<>();
        try {
            List<Tuple> emptyBoxs = grnItemEntityService.getEmptyBoxs(grnCode);
            for (Tuple tuple : emptyBoxs)
                list.add(new BoxMapping(String.valueOf(tuple.get("barcode")), grnCode,
                        String.valueOf(tuple.get("pid"))));

        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Logging
    @Override
    public boolean releaseEmptyBoxes(List<BoxMapping> emptyBoxes, List<Box> boxes) {
        try {
            List<BoxEntity> boxEntities =
                    boxEntityService.findByIds(emptyBoxes.stream().map(BoxMapping::getBarcode).collect(Collectors.toList()));
            boxEntities = boxEntities.stream().filter(boxEntity -> boxEntity.isEnabled()).collect(Collectors.toList());
            boxEntities.forEach(boxEntity -> {
                boxEntity.setStatus(AVAILABLE);
                boxEntity.setUpdatedBy(emptyBoxes.get(0).getCreatedBy());
                boxEntity.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
            });
            List<BoxHistoryEntity> boxHistoryEntities = buildBoxHistory(boxes);
            boxEntityService.saveOrUpdateAll(boxEntities);
            boxHistoryEntityService.saveOrUpdateAll(boxHistoryEntities);
            return true;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private List<BoxHistoryEntity> buildBoxHistory(List<Box> boxes) {
        List<BoxHistoryEntity> boxHistoryEntities = new ArrayList<>();
        for (Box box : boxes) {
            BoxHistoryEntity boxHistoryEntity = new BoxHistoryEntity();
            boxHistoryEntity.setBarcode(box.getBarcode());
            boxHistoryEntity.setFacility(box.getFacilityCode());
            boxHistoryEntity.setLocation(box.getLocation());
            boxHistoryEntity.setStatus(box.getStatus());
            boxHistoryEntity.setEnabled(box.isEnabled());
            boxHistoryEntity.setInsertTime(new Date().toInstant());
            boxHistoryEntity.setCreatedBy(box.getCreatedBy());
            boxHistoryEntity.setUpdatedBy(box.getUpdatedBy());
            boxHistoryEntities.add(boxHistoryEntity);
        }
        return boxHistoryEntities;
    }


    @Logging
    public int updateBox(List<BoxMapping> emptyBoxes, Connection con) {
        return -1;
    }

    @Logging
    @Override
    public Map<String, Map<String, Object>> getGRNScanDetails(List<String> grns) {

        Map<String, Map<String, Object>> result = new HashMap<>();
        StringBuilder build = new StringBuilder();
        build.append(GRN_SCAN_COUNT);
        for (int n = 0; n < grns.size(); n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
        build.append(") group by grn_code");
        try {
            Query nativeQuery = entityManager.createNativeQuery(build.toString(), Tuple.class);
            int i = 1;
            for (String grn : grns) {
                nativeQuery.setParameter(i++, grn);
            }
            List<Tuple> resultList = nativeQuery.getResultList();
            for (Tuple tuple : resultList) {
                Map<String, Object> map = new HashMap<>();
                map.put(TOTAL_SCANNED, tuple.get("total_count"));
                map.put(TOTAL_PASSED, tuple.get("pass_count"));
                result.put(String.valueOf(tuple.get("grn_code")), map);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return result;
    }

    @Logging
    public boolean saveBoxHistory(List<Box> boxes, Connection con) {
        return false;
    }

    @Logging
    @Override
    @SuppressWarnings("unchecked")
    public List<GRNPidBarcodeDetailsDTO> getGrnPidBarcodeDetails(String grnCode) {
        List<GRNPidBarcodeDetailsDTO> result = null;
        Map<String, Object> pidMap = new HashMap<>();
        try {
            Query grmMasterDetails = entityManager.createNativeQuery(GET_GRN_PID_MASTER_DETAILS, Tuple.class);
            int i = 1;
            grmMasterDetails.setParameter(i, grnCode);
            List<Tuple> resultList = grmMasterDetails.getResultList();

            Map<String, Object> pidCountMap;
            for (Tuple tuple : resultList) {
                pidCountMap = new HashMap<>();
                pidCountMap.put("price", tuple.get("price", BigDecimal.class).doubleValue());
                pidCountMap.put("tax_rate", tuple.get("tax_rate", BigDecimal.class).doubleValue());
                pidMap.put(tuple.get("pid", String.class), pidCountMap);
            }

            Query nativeQuery = entityManager.createNativeQuery(GET_GRN_PID_BARCODE_DETAILS, Tuple.class);
            i = 1;
            nativeQuery.setParameter(i++, grnCode);
            try {

                result =
                        nativeQuery.unwrap(org.hibernate.query.Query.class).setResultTransformer(new ResultTransformer() {
                            @Override
                            public Object transformTuple(Object[] tuples, String[] aliase) {
                                return CommonDbUtils.convertToObject(tuples, aliase, GRNPidBarcodeDetailsDTO.class);
                            }

                            @Override
                            public List transformList(List list) {
                                return list;
                            }
                        }).getResultList();
                for (GRNPidBarcodeDetailsDTO grnPidBarcodeDetailsDTO : result) {
                    Map<String, Object> pidDetails = (Map<String, Object>) pidMap.get(grnPidBarcodeDetailsDTO.getPid());
                    grnPidBarcodeDetailsDTO.setPrice((Double) pidDetails.get("price"));
                    grnPidBarcodeDetailsDTO.setTaxRate((Double) pidDetails.get("tax_rate"));
                }
            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return result;
    }

    @Logging
    @Override
    public Map<String, Object> getGRNScanCountByPID(String grnCode) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Tuple> grnScanCountByPid = grnItemEntityService.getGrnScanCountByPid(grnCode);
            Map<String, Object> pidCountMap;
            for (Tuple tuple : grnScanCountByPid) {
                pidCountMap = new HashMap<>();
                pidCountMap.put("total_count", Long.valueOf(tuple.get("total_count").toString()).intValue());
                pidCountMap.put("fail_count", Long.valueOf(tuple.get("fail_count").toString()).intValue());
                result.put(String.valueOf(tuple.get("pid")), pidCountMap);
            }

            return result;
        } catch (Exception ex) {
            log.error("getGRNScanCountByPID exception:" + ex.getMessage(), ex);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public List<PidItemCounter> getPidItemCount(List<String> invoiceRefNumbers, List<String> poIds) {
        List<PidItemCounter> invoicePidCounters = new ArrayList<>();
        StringBuilder build = new StringBuilder();
        int count = 0;
        List<Tuple> resultList;
        if (invoiceRefNumbers != null && !invoiceRefNumbers.isEmpty()) {
            resultList = grnItemEntityService.findByInvoiceRefNumIn(invoiceRefNumbers);
        } else {
            resultList = grnItemEntityService.findByPoIdIn(poIds);
        }
        try {
            for (Tuple tuple : resultList) {
                PidItemCounter invoicePidCounter = new PidItemCounter();
                invoicePidCounter.setInvoiceRefNum(String.valueOf(tuple.get("item_id")));
                invoicePidCounter.setPid(String.valueOf(tuple.get("pid")));
                invoicePidCounter.setCount(Long.valueOf(tuple.get("count").toString()));
                invoicePidCounters.add(invoicePidCounter);
            }
            return invoicePidCounters;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public List<GrnItemPoInvoiceCount> getInvoiceCountForGrnPid(String grnCode, String invoiceRefNum, List<String> pids) {
        log.info("GET_INVOICE_ITEM_COUNT_FOR_GRN Grn code {} ", grnCode);
        List<GrnItemPoInvoiceCount> list = new ArrayList<>();
        try {

            List<Tuple> resultList = grnItemEntityService.getInvoiceCountForGrnPid(invoiceRefNum,pids);
            for (Tuple tuple: resultList) {
                try {
                    list.add(new GrnItemPoInvoiceCount(grnCode, tuple.get("pid", String.class),
                            Integer.parseInt(DBUtils.getOrDefault(tuple.get("scanned_quantity"), 0).toString()),
                            null, Integer.parseInt(DBUtils.getOrDefault(tuple.get("invoice_quantity"), 0).toString())));
                } catch (Exception ex) {
                    log.error("GET_INVOICE_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
                    throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
                }
            }
        } catch (Exception ex) {
            log.error("GET_INVOICE_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
            throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Override
    @Logging
    public List<GrnItemPoInvoiceCount> getPoCountForGrnPid(String grnCode, String poId, List<String> pids) {
        log.info("GET_PO_ITEM_COUNT_FOR_GRN Grn code {} ", grnCode);
        List<GrnItemPoInvoiceCount> list = new ArrayList<>();
        try {
            List<Tuple> resultList = grnItemEntityService.getPoCountForGrnPid(poId, pids);
            try {
                for (Tuple tuple : resultList) {
                    list.add(new GrnItemPoInvoiceCount(grnCode, tuple.get("pid", String.class),
                            Integer.parseInt(DBUtils.getOrDefault(tuple.get("scanned_quantity"), 0).toString()),
                            Integer.parseInt(DBUtils.getOrDefault(tuple.get("po_quantity"), 0).toString()), null));
                }
            } catch (Exception ex) {
                log.error("GET_PO_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
                throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception ex) {
            log.error("GET_PO_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
            throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Override
    @Logging
    public List<GrnPOInvoicePidData> getPidPOInvoiceForGrn(String grnCode) {
        log.info("GrnPOInvoicePidData Grn code {} ", grnCode);
        List<GrnPOInvoicePidData> list = new ArrayList<>();
        try {
            List<Tuple> resultList = grnItemEntityService.getPidPOInvoiceForGrn(grnCode);

            for (Tuple tuple : resultList) {
                try {
                    list.add(new GrnPOInvoicePidData(tuple.get("pid", String.class), tuple.get("po_id", String.class),
                            tuple.get("invoice_ref_num", String.class)));
                } catch (Exception ex) {
                    log.error("GrnPOInvoicePidData SQL Exception : " + ex.getMessage());
                    throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
                }
            }
        } catch (Exception ex) {
            log.error("GrnPOInvoicePidData SQL Exception : " + ex.getMessage());
            throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Override
    public Map<String, Map<String, Object>> getGRNItemsScannedCount(List<GRNPIDMaster> grnPids) {
        Map<String, Map<String, Object>> grnPidCounts = new HashMap<>();
        StringBuilder build = new StringBuilder();
        build.append(GET_GRN_ITEMS_PID_SCANNED_COUNT);
        Set<String> uniquePids = new HashSet<>();
        Set<String> uniqueGrns = new HashSet<>();
        for (GRNPIDMaster grnPid : grnPids) {
            uniquePids.add(grnPid.getPid());
            uniqueGrns.add(grnPid.getGrnCode());
        }
        buildInQuery(build, uniqueGrns.size());
        build.append(") and pid in (");
        buildInQuery(build, uniquePids.size());
        build.append(") group by grn_code, pid");
        try {
            Query nativeQuery = entityManager.createNativeQuery(build.toString(), Tuple.class);

            int i = 1;
            for(String grn : uniqueGrns) {
                nativeQuery.setParameter(i++, grn);
            }

            for(String pid : uniquePids) {
                nativeQuery.setParameter(i++, pid);
            }

            Map<String, Object> pidMap;
            List<Tuple> resultList = nativeQuery.getResultList();
            try {
                for(Tuple tuple: resultList) {
                    if (grnPidCounts.containsKey(tuple.get("grn_code",String.class)))
                        pidMap = grnPidCounts.get(tuple.get("grn_code",String.class));
                    else {
                        pidMap = new HashMap<>();
                        grnPidCounts.put(tuple.get("grn_code",String.class), pidMap);
                    }
                    Map<String, Object> pidCountMap = new HashMap<>();
                    pidCountMap.put("total_count", tuple.get("total_count",Integer.class));
                    pidCountMap.put("fail_count", tuple.get("fail_count", Integer.class));
                    pidCountMap.put("sampling_percent", tuple.get("sample",Integer.class));
                    pidMap.put(tuple.get("pid",String.class), pidCountMap);
                }

            } catch (Exception ex) {
                throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return grnPidCounts;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private void buildInQuery(StringBuilder build, int size) {
        for (int n = 0; n < size; n++) {
            build.append("?,");
        }
        if (build.length() > 0) {
            build.deleteCharAt(build.lastIndexOf(","));
        }
    }

    @Override
    public GrnItemEntity saveGrnItemDetails(GRNItem grnItem) throws Exception {
        try{
            log.info("Save Grn item details {}", grnItem.getBarcode());
            GrnItemEntity grnItemEntity = new GrnItemEntity();
            grnItemEntity.setBarcode(grnItem.getBarcode());
            grnItemEntity.setGrnCode(grnItem.getGrnCode());
            grnItemEntity.setStatus(grnItem.getStatus());
            grnItemEntity.setPid(grnItem.getPid());
            grnItemEntity.setPoId(grnItem.getPoId());
            grnItemEntity.setInvoiceRefNum(grnItem.getInvoiceRefNum());
            grnItemEntity.setVendorCode(grnItem.getVendorCode());
            if(grnItem.getExpiryDate()!=null)
                grnItemEntity.setExpiryDate(new Timestamp(grnItem.getExpiryDate().getTime()));
            grnItemEntity.setLotNo(grnItem.getLotNo());
            grnItemEntity.setEstimatedQty(grnItem.getGrnEstimatedQuantity());
            grnItemEntity.setQcPassBoxBarcode(grnItem.getQcPassBoxBarcode());
            grnItemEntity.setQcStatus(grnItem.getQcStatus());
            grnItemEntity.setQcFailCode(grnItem.getQcFailCode());
            grnItemEntity.setQcReason(grnItem.getQcReason());
            grnItemEntity.setQcMasterSampling(grnItem.getQcMasterSampling());
            grnItemEntity.setQcGradientSampling(grnItem.getQcGradientSampling());
            grnItemEntity.setFailureThresholdQcMaster(grnItem.getFailureThresholdQcMaster());
            grnItemEntity.setFailureThresholdQcGradient(grnItem.getFailureThresholdQcGradient());
            grnItemEntity.setCreatedBy(grnItem.getCreatedBy());
            grnItemEntity.setUpdatedBy(grnItem.getUpdatedBy());
            grnItemEntity.setFacility(grnItem.getFacility());
            grnItemEntity.setChannelStatus(grnItem.getChannelStatus());
            grnItemEntity.setPutawayCode(grnItem.getPutawayCode());
            if(grnItem.getLegalOwner() != null)
                grnItemEntity.setLegalOwner(grnItem.getLegalOwner());
            log.info("Save Grn item details {}", grnItemEntity.toString());
            saveGrnItemEntity(grnItemEntity);
            return grnItemEntity;
        } catch (Exception e){
            log.error("SQL Exception in saving grn item details {}", grnItem.getBarcode());
            throw new ApplicationException("SQL Exception : " + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Logging
    public List<GrnItemPoInvoiceCount> getPoCountAndPriceForGrnPid(String grnCode,
                                                                   String invoiceRefNum,
                                                                   String poNum) {
        log.info("GET_QTY_AND_PRICE_ITEM_COUNT_FOR_GRN Grn code {} ", grnCode);
        List<GrnItemPoInvoiceCount> list = new ArrayList<>();
        try {

            List<Tuple> resultList = grnItemEntityService.getGrnPidItemCountForInvoice(invoiceRefNum, poNum);
            for (Tuple tuple: resultList) {
                try {
                    list.add(new GrnItemPoInvoiceCount(grnCode, tuple.get("pid", String.class),
                            Integer.parseInt(DBUtils.getOrDefault(tuple.get("scanned_quantity"), 0).toString())));
                } catch (Exception ex) {
                    log.error("GET_QTY_AND_PRICE_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
                    throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
                }
            }
        } catch (Exception ex) {
            log.error("GET_QTY_AND_PRICE_ITEM_COUNT_FOR_GRN SQL Exception : " + ex.getMessage());
            throw new ApplicationException("Unable to get po invoice count, SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return list;
    }

    @Override
    public List<Tuple> updateQcStatusByGrnCode(String grncode, String qcStatus) {
        String selectQuery = "SELECT * FROM grn_items WHERE grn_code = '" + grncode + "' and qc_pass_box_barcode is null" +
                " AND qc_status is not null";
        String grnMasterQuery = "UPDATE `grn_master` SET `grn_status` = '" + qcStatus + "' WHERE `grn_code` = '" + grncode + "'";
        log.info("Queries: {} and {}", grnMasterQuery, selectQuery);
        try {
            Query nativeQuery = entityManager.createNativeQuery(grnMasterQuery);
            int updatedRows = nativeQuery.executeUpdate();
            log.info("updated Rows in grn_master : {}", updatedRows);
            Query selectNativeQuery = entityManager.createNativeQuery(selectQuery, Tuple.class);
            List<Tuple> result = selectNativeQuery.getResultList();
            log.info("Query result in [updateQcStatus]: {} ", result);
            return result;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public List<Tuple> getIqcGrnDetails(String grncode, String pid, List<String> boxcode) {
        StringBuilder query = new StringBuilder("SELECT * FROM iqc_grn_product WHERE grn_code = '" + grncode + "'");
        if (boxcode != null && !boxcode.isEmpty())
            query = query.append("and box_code in (" + boxcode.toString().substring(1, boxcode.toString().length()-1) + ")");
        else if (pid != null){
            query = query.append("and pid = '" + pid + "'");
        }
        try {
            log.info("Query in [getIqcGrnDetails]: {}", query);
            Query nativeQuery = entityManager.createNativeQuery(query.toString(), Tuple.class);
            List<Tuple> result = nativeQuery.getResultList();
            if (result.isEmpty()) {
                String newQuery = "SELECT * FROM iqc_grn_product WHERE grn_code = '" + grncode + "'";
                log.info("New query in [getIqcGrnDetails] : {}", newQuery);
                nativeQuery = entityManager.createNativeQuery(newQuery, Tuple.class);
                result = nativeQuery.getResultList();
            }
            log.info("Query result in [getIqcGrnDetails]: {} ", result);
            return result;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public String getVendorIdFromGrnCode(String grncode) {
        String query = "SELECT vendor_code FROM grn_items WHERE grn_code = '" + grncode + "' LIMIT 1";
        try {
            Query nativeQuery = entityManager.createNativeQuery(query);
            List<String> result = nativeQuery.getResultList();
            log.info("Query result in [getVendorIdFromGrnCode]: {} ", result);
            return result.get(0);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public List<Tuple> getGrnItemUsingBoxOrBarcode(String barcode, String grnCode, String pid) {
        log.info("[getGrnItemUsingBoxOrBarcode] barcode {} and pid {} and grncode : {}", barcode, pid, grnCode);
        String query = QueryUtils.getQueryByBarcodeOrPid(barcode, grnCode, pid);
        log.info("[getGrnItemUsingBoxOrBarcode] barcode {} and pid {} and grncode : {}, query {}",
                barcode, pid, grnCode, query);
        try {
            Query nativeQuery = entityManager.createNativeQuery(query, Tuple.class);
            List<Tuple> result = nativeQuery.getResultList();
            log.info("Query result in [getGrnItemUsingBoxOrBarcode]: {} ", result);
            if (result == null || result.isEmpty())
                return Collections.emptyList();
            query = QueryUtils.getQueryByBoxOrBarcode(query, result);
            log.info("New Query in [getGrnItemUsingBoxOrBarcode] : {}", query);
            nativeQuery = entityManager.createNativeQuery(query, Tuple.class);
            List<Tuple> newResult = nativeQuery.getResultList();
            log.info("New query result in [getGrnItemUsingBoxOrBarcode]: {} ", newResult);
            return newResult;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public void updateQcStatusUsingGrnCode(String grnCode, String qcStatus) {
        String query ="UPDATE `grn_master` SET `grn_status` = '" + qcStatus + "' WHERE `grn_code` = '" + grnCode + "'";
        log.info("Query in [updateQcStatusUsingGrnCodeAndPid]: {}", query);
        try {
            Query nativeQuery = entityManager.createNativeQuery(query.toString());
            int updatedRows = nativeQuery.executeUpdate();
            log.info("updated Rows in grn_master [updateQcStatusUsingGrnCodeAndPid]: {}", updatedRows);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public GRNItem getTopGrnItemDetailForBarcodeAndFacility(String barcode, String facility) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.getTopGrnItemDetailForBarcodeAndFacility(barcode, facility);
            if (grnItemEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            GRNItem grnItem = GRNItemServiceUtils.buildGrnItem(grnItemEntity);
            return grnItem;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    @Override
    public GRNItem getBarcodeDetails(String barcode, String facilityCode) {
        try {
            GrnItemEntity grnItemEntity = new GrnItemEntity();
            if(facilityCode == null || facilityCode.isEmpty())
                grnItemEntity = grnItemEntityService.findTopByBarcode(barcode);
            else
                grnItemEntity = grnItemEntityService.getTopGrnItemDetailForBarcodeAndFacility(barcode, facilityCode);
            if (grnItemEntity == null) {
                GrnItemsArchivedEntity grnItemsArchivedEntity;
                if(facilityCode == null || facilityCode.isEmpty())
                    grnItemsArchivedEntity = grnItemsArchivedEntityService.findTopByBarcode(barcode);
                else {
                    grnItemsArchivedEntity = grnItemsArchivedEntityService.getTopGrnItemDetailForBarcodeAndFacility(barcode, facilityCode);

                }
                grnItemEntity = objectMapper.convertValue(grnItemsArchivedEntity, GrnItemEntity.class);
            }
            if (grnItemEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            GRNItem grnItem = GRNItemServiceUtils.buildGrnItem(grnItemEntity);
            return grnItem;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

	@Override
	public List<String> getPutawayCodesByGrnCode(String grnCode) {
		List<String> putawayCodes = new ArrayList<String>();
		List<GrnItemEntity> grnItems = grnItemEntityService.findByGrnCode(grnCode);
		if (grnItems != null && !grnItems.isEmpty()) {
			putawayCodes.addAll(grnItems.stream().map(GrnItemEntity::getPutawayCode).filter(Objects::nonNull)
					.map(String::valueOf).collect(Collectors.toSet()));
		}
		return putawayCodes;
	}

	@Override
	public void updatePutawaySyncStatusByGrnCode(String grnCode) {
	}

	@Override
	public List<GrnItemEntity> getGrnItemsList(String grnCode) {
		return grnItemEntityService.findByGrnCode(grnCode);
	}
}
