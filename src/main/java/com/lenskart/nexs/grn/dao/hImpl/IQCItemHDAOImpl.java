package com.lenskart.nexs.grn.dao.hImpl;

import com.lenskart.nexs.common.entity.entityService.grn.GrnItemEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.IqcGrnProductEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.IqcInvoiceProductEntityService;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.IqcGrnProductEntity;
import com.lenskart.nexs.common.entity.po.invoice.IqcInvoiceProductEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.IQCConstants;
import com.lenskart.nexs.grn.dao.IQCItemHDAO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.ExceptionConstants;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.putaway.model.response.CreatePutawayResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


import java.util.Date;
import java.util.List;

@Repository
@Slf4j
public class IQCItemHDAOImpl implements IQCItemHDAO {

    @Autowired
    private GrnItemEntityService grnItemEntityService;

    @Autowired
    private IqcGrnProductEntityService iqcGrnItemEntityService;

    @Autowired
    IqcInvoiceProductEntityService iqcInvoiceProductEntityService;
    @Logging
    @Override
    public GrnItemEntity updateIQcStatus(String barcode, String qcStatus, String qcReason, String grnCode, String boxCode,  String pid) {
        try {
            GrnItemEntity grnItemEntity = null;
            log.info("[updateIQcStatus] barcode {} and qcStatus {}", barcode, qcStatus);
            if (StringUtils.isNotBlank(boxCode) && !StringUtils.equalsIgnoreCase(boxCode, "null")) {
                 grnItemEntity = grnItemEntityService.findByIdAndGrnCodeAndQcPassBoxBarcodeAndPid(barcode, grnCode, boxCode, pid);
            }else if (StringUtils.isBlank(boxCode) || StringUtils.equalsIgnoreCase(boxCode, "null")) {
                 grnItemEntity = grnItemEntityService.findByIdAndGrnCodeAndPidAndQcPassBoxBarcodeIsNull(barcode, grnCode, pid);
            }

            if (grnItemEntity == null) {
                log.error("[updateIQcStatus]  Error {} received for barcode{} and grnCode{}",ExceptionConstants.BARCODE_NOT_FOUND,barcode,grnCode);
                throw new ApplicationException(ExceptionConstants.BARCODE_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            }
            else {
                if (IQCConstants.IQC_PASS.equalsIgnoreCase(qcStatus) && StringUtils.isEmpty(grnItemEntity.getQcStatus())) { //and in db null
                    log.info("[updateIQcStatus] Inside if condition IQC_PASS, status received from UI {}", qcStatus);
                    grnItemEntity.setQcStatus(qcStatus);
                }
                else if (grnItemEntity.getQcStatus().equalsIgnoreCase(IQCConstants.IQC_PASS)) {
                    log.info("[updateIQcStatus] Inside if IQC_PASS condition, status received from database {}", grnItemEntity.getQcStatus());
                    if (qcStatus.equalsIgnoreCase(IQCConstants.IQC_FAIL)) {
                        log.info("[updateIQcStatus] Inside if IQC_FAIL condition, status received from UI {} and qcReason{} ", qcStatus, qcReason);
                        grnItemEntity.setQcStatus(qcStatus);
                        grnItemEntity.setQcReason(qcReason);
                    }
                    if (qcStatus.equalsIgnoreCase(IQCConstants.IQC_PASS)) {
                        throw new ApplicationException(ExceptionConstants.ALREADY_SCANNED, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
                    }
                }
               else if (grnItemEntity.getQcStatus().equalsIgnoreCase(IQCConstants.IQC_FAIL)) {
                    if (qcStatus.equalsIgnoreCase(IQCConstants.IQC_FAIL) || qcStatus.equalsIgnoreCase(IQCConstants.IQC_PASS)) {
                        log.error("[updateIQcStatus] Inside if IQC_FAIL condition, status received from database {}", grnItemEntity.getQcStatus());
                        throw new ApplicationException(ExceptionConstants.ALREADY_SCANNED, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
                    }
                } else{
                    log.info("[updateIQcStatus] No Condition matched for UIqcStatus{} and DBqcStatus", qcStatus, grnItemEntity.getQcStatus());
                }
                grnItemEntity.setUpdatedBy(MDC.get("USER_ID"));
                grnItemEntity.setUpdatedAt(new Date());
                grnItemEntityService.saveOrUpdate(grnItemEntity);
            }
            return grnItemEntity;
        } catch (Exception ex) {
            log.error("[updateIQcStatus] Exception:  barcode {} , grn code: {} , error: {}",barcode, grnCode,ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

@Override
    public IqcGrnProductEntity getIqcProductGrnDetails(String boxcode, String grnCode, String pid) {
        try {
            IqcGrnProductEntity iqcGrnProductEntity = new IqcGrnProductEntity();
            if(StringUtils.isNotBlank(boxcode) && !StringUtils.equalsIgnoreCase(boxcode, "null")) {
                log.info("[getIqcProductGrnDetails] Received not null boxcode for IQC Complete, boxCode{} and grnCode{}",boxcode, grnCode);
                iqcGrnProductEntity = iqcGrnItemEntityService.findByGrnCodeAndBoxCode(grnCode, boxcode);
            } else if(StringUtils.isNotBlank(pid)){
                log.info("[getIqcProductGrnDetails] Received null boxcode for IQC Complete, pid {} and grnCode {}", pid, grnCode);
                iqcGrnProductEntity = iqcGrnItemEntityService.findByPidAndGrnCode(pid, grnCode);
            }else{
                log.info("[getIqcProductGrnDetails] Received null boxcode for IQC Complete, boxCode{} and grnCode{}",boxcode, grnCode);
                iqcGrnProductEntity = iqcGrnItemEntityService.findByGrnCodeAndBoxCodeIsNull(grnCode);
            }

            if (iqcGrnProductEntity == null) {
                log.error("[getIqcProductGrnDetails]  Error {} received for boxcode{}, pid{} and grnCode{}",ExceptionConstants.IQC_GRN_NOT_FOUND,boxcode,pid,grnCode);
                throw new ApplicationException(ExceptionConstants.IQC_GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            }
            return iqcGrnProductEntity;
        } catch (Exception ex) {
            log.error("[getIqcProductGrnDetails]  Error {} received for boxcode {}, pid {} and grnCode {}",GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR,boxcode,pid,grnCode);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public void updatePutawayCodeInGrnItem(CreatePutawayResponse putawayResponse, String barcode) {
        try {
            Integer putawayCode = Integer.valueOf(putawayResponse.getBarcodeInfo().get(0).getNewPutawayId());
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcode(barcode);
            if (null == grnItemEntity)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            grnItemEntity.setPutawayCode(putawayCode);
            grnItemEntity.setUpdatedBy(MDC.get("USER_ID"));
            grnItemEntity.setUpdatedAt(new Date());
            grnItemEntityService.saveOrUpdate(grnItemEntity);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public List<IqcGrnProductEntity> findByGrnCode(String grnCode) {
        try {
            log.info("[getIqcProductGrnDetailsForGrnCode] grnCode {}", grnCode);
            List<IqcGrnProductEntity> iqcGrnProductEntities = iqcGrnItemEntityService.findByGrnCode(grnCode);
            return iqcGrnProductEntities;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public GrnItemEntity getGrnItemDetails(String barCode, String grnCode) {
        try {
            GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndGrnCode(barCode,grnCode);
            if (null == grnItemEntity)
                throw new ApplicationException(ExceptionConstants.BARCODE_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            return grnItemEntity;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public IqcGrnProductEntity getIqcGrnDetails(String pid, String grnCode, String qcPassBoxBarcode) {
        try {
            log.info("[getIqcGrnDetails] grnCode {}", grnCode);
            IqcGrnProductEntity iqcGrnProductEntity = iqcGrnItemEntityService.findByPidAndGrnCodeAndBoxCode(pid,grnCode,qcPassBoxBarcode);
            if (iqcGrnProductEntity == null)
                throw new ApplicationException(ExceptionConstants.GRN_NOT_FOUND, GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            return iqcGrnProductEntity;
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public IqcInvoiceProductEntity findByInvoiceRefNumber(int invoiceRefNum, int pid) {
        try {
            log.info("[findByInvoiceRefNumber] invoiceRefNum {} and pid {}", invoiceRefNum, pid);
            IqcInvoiceProductEntity iqcInvoiceProductEntity = iqcInvoiceProductEntityService.findByInvoiceRefNumberAndPid(invoiceRefNum,pid);
            log.info("[findByInvoiceRefNumber] iqcInvoiceProductEntity {}, invoiceRefNum {} and pid {}", iqcInvoiceProductEntity, invoiceRefNum, pid);
            if (iqcInvoiceProductEntity == null){
                log.error("Details does not exist in iqcInvoiceProductEntity table for invoiceRefNum {} and pid {}", invoiceRefNum, pid);
                throw new ApplicationException("Details does not exist in iqc invoice entity table",
                        GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            }
            return iqcInvoiceProductEntity;
        } catch (Exception ex) {
            log.error("[findByInvoiceRefNumber]  Error {} received for invoiceRefNum {}, pid{} and grnCode{}",GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR,invoiceRefNum,pid);
            throw new ApplicationException("SQL Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

}
