package com.lenskart.nexs.grn.dao.hImpl;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.BoxDAO;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@Service(QualifierConstants.JPA_BOX_DAO)
@Transactional(rollbackFor = Exception.class)
public class BoxHDAOImpl implements BoxDAO {
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public boolean checkBoxExists(String grnCode, String pid, String boxBarcode) {
        try {
            Query query = entityManager.createNativeQuery(Queries.SELECT_BOX_BARCODE);
            int i = 1;
            query.setParameter(i++, grnCode);
            query.setParameter(i++, pid);
            query.setParameter(i++, boxBarcode);
            List resultList = query.getResultList();
            return !CollectionUtils.isEmpty(resultList);
        } catch (Exception ex) {
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
