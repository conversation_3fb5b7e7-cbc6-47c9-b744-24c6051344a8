package com.lenskart.nexs.grn.exception.enums;

public enum GRNExceptionStatus {
    GRN_INTERNAL_EXCEPTION("LK-GRN-001", "Contact System Administrator"),
    GRN_BAD_REQUEST("LK-GRN-002", "Bad Request"),
    GRN_UNAUTHORIZED("LK-GRN-003", "Un-authorized"),
    GRN_METHOD_NOT_ALLOWED("LK-GRN-004", "Method not allowed"),
    GRN_INTERNAL_SERVER_ERROR("LK-GRN-005", "Internal Server Error"),
    GRN_NOT_FOUND("LK-GRN-006", "Not Found"),
    GRN_BAD_GRAMMAR("LK-GRN-007", "Syntax Error"),
    GRN_QC_FAILED("LK-GRN-008", "GRN pid qc failed, manual intervention required");

    private String errorCode;
    private String errorMsg;

    GRNExceptionStatus(String errorCode, String errorMsg ) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }


    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() {
        return errorMsg;
    }
}
