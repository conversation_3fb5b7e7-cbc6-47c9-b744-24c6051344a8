package com.lenskart.nexs.grn.exception;

import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class InvalidRequestException extends Exception {

    private String errorMessage;
    private GRNExceptionStatus grnExceptionStatus;

    public InvalidRequestException(String errorMessage, GRNExceptionStatus grnExceptionStatus) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.grnExceptionStatus = grnExceptionStatus;
    }
}
