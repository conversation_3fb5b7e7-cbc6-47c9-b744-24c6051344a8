package com.lenskart.nexs.grn.exception;

import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ApplicationException extends RuntimeException {

    private String errorMessage;
    private GRNExceptionStatus grnExceptionStatus;
    private Throwable cause;

    public ApplicationException(String errorMessage, GRNExceptionStatus grnExceptionStatus) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.grnExceptionStatus = grnExceptionStatus;
    }
    
    public ApplicationException(String errorMessage, GRNExceptionStatus grnExceptionStatus, Throwable cause) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.grnExceptionStatus = grnExceptionStatus;
        this.cause = cause;
    }

}
