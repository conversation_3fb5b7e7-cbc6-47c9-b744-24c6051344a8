package com.lenskart.nexs.grn.exception;

import java.sql.SQLIntegrityConstraintViolationException;

import javax.validation.ConstraintViolationException;

import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;

@ControllerAdvice
public class ApplicationExceptionHandler extends ResponseEntityExceptionHandler implements  ExceptionConstants {

	@ExceptionHandler(ApplicationException.class)
    public ResponseEntity<ResponseDTO> handleException(final ApplicationException ex) {
    	HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
    	
    	if(ex.getCause() instanceof SQLIntegrityConstraintViolationException) {
    		status = HttpStatus.CONFLICT;
    	}
        GRNExceptionStatus grnExceptionStatus = ex.getGrnExceptionStatus();
        
        if(GRNExceptionStatus.GRN_NOT_FOUND.equals(grnExceptionStatus)) {
        	status = HttpStatus.NOT_FOUND;
        }
        
        if(GRNExceptionStatus.GRN_QC_FAILED.equals(grnExceptionStatus)) {
        	status = HttpStatus.NOT_ACCEPTABLE;
        }
        
        setMdcKeys(grnExceptionStatus.getErrorCode(), grnExceptionStatus.getErrorMsg());
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setErrorCode(grnExceptionStatus.getErrorCode());
        responseDTO.setDisplayMessage(ex.getMessage());
        return ResponseEntity.status(status).body(responseDTO);
    }

    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(LKGRNException.class)
    @ResponseBody
    public ResponseDTO handleException(final LKGRNException ex) {
        GRNExceptionStatus grnExceptionStatus = ex.getGrnExceptionStatus();
        setMdcKeys(grnExceptionStatus.getErrorCode(), grnExceptionStatus.getErrorMsg());
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setErrorCode(grnExceptionStatus.getErrorCode());
        responseDTO.setDisplayMessage(ex.getMessage());
        return responseDTO;
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(HttpClientErrorException.BadRequest.class)
    @ResponseBody
    public ResponseDTO handleException(final HttpClientErrorException.BadRequest ex) {
        GRNExceptionStatus grnExceptionStatus = GRNExceptionStatus.GRN_BAD_REQUEST;
        setMdcKeys(grnExceptionStatus.getErrorCode(), grnExceptionStatus.getErrorMsg());
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setErrorCode(grnExceptionStatus.getErrorCode());
        responseDTO.setDisplayMessage(ex.getMessage());
        return responseDTO;
    }

    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(HttpClientErrorException.Unauthorized.class)
    @ResponseBody
    public ResponseDTO handleException(final HttpClientErrorException.Unauthorized ex) {
        GRNExceptionStatus grnExceptionStatus = GRNExceptionStatus.GRN_UNAUTHORIZED;
        setMdcKeys(grnExceptionStatus.getErrorCode(), grnExceptionStatus.getErrorMsg());
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setErrorCode(grnExceptionStatus.getErrorCode());
        responseDTO.setDisplayMessage(ex.getMessage());
        return responseDTO;
    }

    @ResponseStatus(value = HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler(HttpClientErrorException.MethodNotAllowed.class)
    @ResponseBody
    public ResponseDTO handleException(final HttpClientErrorException.MethodNotAllowed ex) {
        GRNExceptionStatus grnExceptionStatus = GRNExceptionStatus.GRN_METHOD_NOT_ALLOWED;
        setMdcKeys(grnExceptionStatus.getErrorCode(), grnExceptionStatus.getErrorMsg());
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setErrorCode(grnExceptionStatus.getErrorCode());
        responseDTO.setDisplayMessage(ex.getMessage());
        return responseDTO;
    }
    
    @ExceptionHandler(ResponseStatusException.class)
	public ResponseEntity<ResponseDTO> handleException(ResponseStatusException responseStatusException) {
		HttpHeaders headers = new HttpHeaders();

		ResponseDTO response = new ResponseDTO();
		response.setErrorCode(String.valueOf(responseStatusException.getStatus().value()));
		response.setDisplayMessage(responseStatusException.getReason());

		return ResponseEntity.status(responseStatusException.getStatus()).headers(headers).body(response);
	}
    
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
   	@ExceptionHandler({ ConstraintViolationException.class })
   	@ResponseBody
   	public ResponseDTO handleConstraintViolation(ConstraintViolationException ex) {

   		HttpHeaders headers = new HttpHeaders();

   		ResponseDTO response = new ResponseDTO();
   		response.setErrorCode(String.valueOf(HttpStatus.BAD_REQUEST));
   		response.setDisplayMessage(ex.getMessage());

   		return response;
   	}

    @Override
	public ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException methodArgumentNotValidException, HttpHeaders headers, HttpStatus status, WebRequest webRequest) {

		ResponseDTO response = new ResponseDTO();
		response.setErrorCode(String.valueOf(status.value()));
		response.setDisplayMessage(methodArgumentNotValidException.getBindingResult().getFieldError().getDefaultMessage());

		return ResponseEntity.status(status).headers(headers).body(response);
	}

    private void setMdcKeys(String errorCode, String errorMessage) {
        MDC.put(SYSTEM_NAME_KEY, SYSTEM_NAME_VALUE);
        MDC.put(ERROR_CODE, errorCode);
        MDC.put(MESSAGE, errorMessage);
    }
}
