package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.service.GRNItemService;
import com.lenskart.nexs.grn.service.GRNMasterService;
import com.lenskart.nexs.responseBuilder.ResponseBuilder;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.lenskart.nexs.ems.model.AutoGrnScanDetails;

@RestController
@Slf4j
@RequestMapping("/internal/nexs/api/grn/v1/master/")
public class GRNInternalController {

    @Autowired
    private GRNMasterService grnMasterService;

    @Autowired
    protected ResponseBuilder responseBuilder;

    @Autowired
    private GRNItemService gRNItemService;

    @RestLogging
    @ApiOperation(value = "Create and close auto Grn")
    @PostMapping(value = "create/autoGrn/details")
    public ResponseEntity<BaseResponseModel> createNCloseAutoGrn(@RequestBody AutoGrnScanDetails request) {
        try {
            AutoGrnScanDetails result = grnMasterService.createNCloseAutoGrn(request);
            ResponseEntity<BaseResponseModel> response =
                    responseBuilder.successResponse(result.getGrnCode(), "GRN created and closed successfully", GRNConstants.GRN_SUCCESS);
            return response;
        } catch (Exception ex) {
            log.error("Exception occurred in creating grn : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Validate if the barcode is eligible for inward")
    @GetMapping(value = "validate/inward")
    public ResponseDTO validateInward(@RequestParam(value = "barcode") String barcode,
                                      @RequestParam(value = "facility") String facility,
                                      @RequestParam(value = "type") String type) {
        try {
            return gRNItemService.checkBarcodeInwardValidity(barcode, facility, type);
        } catch (Exception ex) {
            log.error("Exception occurred in validating barcode : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

}
