package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dto.request.CreateGRNPIDTO;
import com.lenskart.nexs.grn.dto.request.GRNPidSearchDTO;
import com.lenskart.nexs.grn.dto.request.GetPIDDTO;
import com.lenskart.nexs.grn.dto.request.ManualOverrideReqDTO;
import com.lenskart.nexs.grn.dto.request.ProductMismatchReqDTO;
import com.lenskart.nexs.grn.dto.response.*;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.service.GRNPIDMasterService;
import com.lenskart.nexs.grn.service.GRNQcLogService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import java.util.List;
import javax.validation.Valid;

@RestController
@Slf4j
@RequestMapping("/nexs/api/grn/v1/pid/master/")
@Validated
public class GRNPIDMasterController implements GRNConstants {

    @Autowired
    private GRNPIDMasterService grnpidMasterService;
    
    @Autowired
    private GRNQcLogService grnQcLogService;

    @RestLogging
    @ApiOperation(value = "POST Api to derive/get respective PID on scan of GTIN/UPC/VENDOR-SKU/PID")
    @PostMapping( value = "/get-pid")
    public ResponseDTO<Result<PIDResponseDTO>> getPID(@RequestBody @Valid GetPIDDTO getPIDDTO) {
        PIDResponseDTO response = grnpidMasterService.getPID(getPIDDTO);
        ResponseDTO<Result<PIDResponseDTO>> responseDTO = new ResponseDTO<>(new Result<>(response, response.getMeta()));
        responseDTO.setDisplayMessage(GRNConstants.GRN_PID_FETCHED);
        return responseDTO;
    }

//    @RestLogging
//    @ApiOperation(value = "POST Api to fetch sampling quantity details against GRN and PID")
//    @PostMapping( value = "/get/sampling-quantity")
//    public ResponseDTO<Result<SamplingQuantityResponseDTO>> getSamplingQuantity(@RequestBody @Valid CreateGRNPIDTO createGRNPIDTO) {
//        SamplingQuantityResponseDTO response = new SamplingQuantityResponseDTO();
//                //grnpidMasterService.getSamplingQuantity(createGRNPIDTO);
//        ResponseDTO<Result<SamplingQuantityResponseDTO>> responseDTO = new ResponseDTO<>(new Result<>(response, response.getMeta()));
//        responseDTO.setDisplayMessage(GRNConstants.GRN_SAMPLING_QUANTITY);
//        return responseDTO;
//    }

    @RestLogging
    @ApiOperation(value = "GET Api to fetch list of item barcodes scanned , in a box for a GRN and PID")
    @GetMapping( value = "/get/box-items/{grnCode}/{pid}")
    public ResponseDTO<Result<BoxItemsResponseDTO>> getBoxWithItems(@PathVariable("grnCode") String grnCode, @PathVariable("pid") String pid, @RequestParam (value = "isBoxBarcodeRequired") boolean isBoxBarcodeRequired) {
        if(StringUtils.isBlank(grnCode) || StringUtils.isBlank(pid))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid grn code or pid");

        BoxItemsResponseDTO response = grnpidMasterService.getBoxWithItems(grnCode, pid, isBoxBarcodeRequired);
        ResponseDTO<Result<BoxItemsResponseDTO>> responseDTO = new ResponseDTO<>(new Result<>(response, null));
        responseDTO.setDisplayMessage(GRNConstants.GRN_GET_BOX_SUCCESS);
        return responseDTO;
    }
    
    @RestLogging
    @ApiOperation(value = "GET Api to fetch details against a GRN and PID")
    @GetMapping( value = "/get/{grnCode}/{pid}")
    public ResponseDTO<Result<GRNPidDetailsDTO>> getGRNPidInfo(@PathVariable("grnCode") String grnCode, @PathVariable("pid") String pid) throws Exception {
    	
    	if(StringUtils.isBlank(grnCode) || StringUtils.isBlank(pid)) {
    		throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid grn code or pid");
    	}
    	
    	GRNPidDetailsDTO grnPidInfo = grnpidMasterService.getGRNPidDetails(grnCode, pid);
    	Result<GRNPidDetailsDTO> result = new Result<GRNPidDetailsDTO>(grnPidInfo);
        return new ResponseDTO<Result<GRNPidDetailsDTO>>(result);
    }
    
    @RestLogging
    @ApiOperation(value = "POST Api to send product mismatch event details")
    @PostMapping(value = "/product-mismatch")
    public ResponseDTO<Boolean> productMismatch(@Valid @RequestBody ProductMismatchReqDTO productMismatchReq) {
    	
    	try {
    		boolean result = grnQcLogService.productMismatch(productMismatchReq);
    		return new ResponseDTO<Boolean>(result);
    	} catch (Exception ex) {
    		throw new ApplicationException("Exception occured while checking product mismatch : " +ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
    	}
    }
    
	@RestLogging
	@ApiOperation(value = "Get Api to search blocked grn pids")
	@GetMapping(value = "/search-blocked-pids")
	public ResponseDTO<SearchResponse<List<PidSearchResponse>>> searchGrnBlockedPid(
			@RequestHeader(value = "facility-code", required = false) String facilityCode, @ModelAttribute GRNPidSearchDTO grnPidSearchReq) {

		if (StringUtils.isBlank(facilityCode)) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
		}

		grnPidSearchReq.setFacility(facilityCode);
		SearchResponse<List<PidSearchResponse>> result = grnpidMasterService.grnPidSearch(grnPidSearchReq);
		return new ResponseDTO<SearchResponse<List<PidSearchResponse>>>(result);
	}
    
    @RestLogging
    @ApiOperation(value = "Put Api to allow manual override on grn pid or reject")
    @PutMapping(value = "/manual-override")
    public ResponseDTO<Result<List<PidSearchResponse>>> manualOverride(@Valid @RequestBody ManualOverrideReqDTO request,
                                                                       @ModelAttribute GRNPidSearchDTO grnPidSearchReq,
                                                                       @RequestHeader(value="facility-code", required=false) String facility) {
        if (StringUtils.isBlank(facility))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
        grnPidSearchReq.setFacility(facility);
    	List<PidSearchResponse> result = grnpidMasterService.updateManualOverride(request, grnPidSearchReq);
    	return new ResponseDTO<Result<List<PidSearchResponse>>>(new Result<List<PidSearchResponse>>(result));
    }

    @RestLogging
    @ApiOperation(value = "Get Api to get all pids of a grn")
    @GetMapping(value = "/grn-pids")
    public ResponseDTO<SearchResponse<GRNProductResponse>> getGRNProducts(@RequestParam("grn_code") String grnCode,
                                                                          @RequestParam(value="page", defaultValue = "0") int page,
                                                                          @RequestParam(value="page_size", defaultValue = "10") int pageSize,
                                                                          @RequestHeader(value="facility-code", required=false) String facility) {
        if (StringUtils.isBlank(facility))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
    	SearchResponse<GRNProductResponse> result = grnpidMasterService.getGRNProducts(grnCode, facility, page, pageSize);
        ResponseDTO<SearchResponse<GRNProductResponse>> responseDTO = new ResponseDTO<SearchResponse<GRNProductResponse>>(result);
        responseDTO.setDisplayMessage(GRN_PID_FETCHED);
        return responseDTO;
    }

	@RestLogging
	@ApiOperation(value = "Get Api to get all pids of a grn")
	@GetMapping(value = "/grn-blocked-pids")
	public ResponseDTO<SearchResponse<GRNBlockedProduct>> getGRNBlockedProducts(
			@RequestHeader(value = "facility-code", required = false) String facilityCode, @ModelAttribute GRNPidSearchDTO grnPidSearchReq) {

		if (grnPidSearchReq.getGrnCode() == null || grnPidSearchReq.getGrnCode().equals(""))
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "GRN code must not be blank");

		if (StringUtils.isBlank(facilityCode)) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
		}

		grnPidSearchReq.setFacility(facilityCode);
		SearchResponse<GRNBlockedProduct> result = grnpidMasterService.grnBlockedPidSearch(grnPidSearchReq);
		ResponseDTO<SearchResponse<GRNBlockedProduct>> responseDTO = new ResponseDTO<SearchResponse<GRNBlockedProduct>>(result);
		responseDTO.setDisplayMessage(GRN_PID_FETCHED);
		return responseDTO;
	}
}
