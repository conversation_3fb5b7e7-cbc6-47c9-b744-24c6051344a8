package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.common.model.request.grn.BarcodePriceRequest;
import com.lenskart.nexs.common.model.response.grn.BarcodePriceResponse;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.dto.response.Result;
import com.lenskart.nexs.grn.dto.response.ScanItemResponseDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.service.GRNItemService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/nexs/api/grn/v1")
@Validated
@Slf4j
public class GRNItemController {

    @Autowired
    private GRNItemService gRNItemService;

    @PostMapping("scan/item-barcode")
    @ApiOperation(value = "POST Api to scan item barcodes in a GRN for a PID")
    @RestLogging
    public ResponseDTO<Result<ScanItemResponseDTO>> scanGrnItem(@RequestHeader(value = "facility-code", required =
            false) String facilityCode,
                                                                @Valid @RequestBody GRNItemDTO grnItemDTO,
                                                                @RequestHeader(value = "showPutaway", required =
                                                                        false) String showPutaway) {

        if (StringUtils.isBlank(facilityCode)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
        }
        log.info("Barcode Scan Request : {}", grnItemDTO);
        MDC.put("showPutaway", showPutaway);
        grnItemDTO.setFacilityCode(facilityCode);
        grnItemDTO.setCreatedBy(MDC.get("USER_ID"));
        grnItemDTO.setUpdatedBy(MDC.get("USER_ID"));
        try {
            ScanItemResponseDTO response = gRNItemService.scanGRNItem(grnItemDTO);
            ResponseDTO<Result<ScanItemResponseDTO>> responseDTO = new ResponseDTO<>(new Result<>(response, null));
            responseDTO.setDisplayMessage(GRNConstants.ITEM_SCAN_SUCCESS);
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (ApplicationException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN item controller : ", ex);
            throw new ApplicationException("Invalid Request, " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("update/grn-item")
    @ApiOperation(value = "PUT Api to update item barcode qc status , box barcode and qc reason")
    @RestLogging
    public ResponseDTO<Result<Map<String, Object>>> updateGrnItem(@RequestHeader(value = "facility-code", required =
            true) String facilityCode, @Valid @RequestBody UpdateGRNItemDTO updateGRNItemDTO, @RequestHeader(value =
            "showPutaway", required = false) String showPutaway) {
        try {
            MDC.put("showPutaway", showPutaway);
            updateGRNItemDTO.setFacilityCode(facilityCode);
            return gRNItemService.updateItem(updateGRNItemDTO);
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (ApplicationException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN item controller : ", ex);
            throw new ApplicationException("Invalid Request, " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("search/grn-item/{barcode}")
    @ApiOperation(value = "GET Api to fetch GRN item barcode details")
    @RestLogging
    public ResponseDTO<Result<GRNItem>> searchGrnItem(@PathVariable("barcode") String barcode,
                                                      @RequestParam(value = "facility_code") String facilityCode) {
        try {
            GRNItem grnItem = gRNItemService.searchItem(barcode, facilityCode, false);
            ResponseDTO<Result<GRNItem>> responseDTO = new ResponseDTO<>(new Result<>(grnItem, null));
            responseDTO.setDisplayMessage("GRN Item fetched successfully");
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN item controller : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("delete/grn-item/{barcode}")
    @ApiOperation(value = "DELETE Api to delete scanned item barcode in a GRN")
    @RestLogging
    public ResponseDTO<Result<Object>> deleteGrnItem(@PathVariable("barcode") String barcode,
                                                     @RequestParam("po_id") String poId,
                                                     @RequestParam(value = "type", required = false) String type,
                                                     @RequestParam(value = "invoice_level", required = false) String invoiceLevel,
                                                     @RequestHeader(value = "showPutaway", required = false) String showPutaway) {
        try {
            MDC.put("showPutaway", showPutaway);
            gRNItemService.deleteItem(barcode, type, invoiceLevel, poId);
            ResponseDTO<Result<Object>> responseDTO = new ResponseDTO<>(new Result<>(null, null));
            responseDTO.setDisplayMessage("Barcode deleted successfully");
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN item controller : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/invoice/total-scanned")
    @ApiOperation(value = "GET Api to fetch GRN item barcode details")
    @RestLogging
    public ResponseDTO<Result<Map<String, Object>>> getTotalScanByInvoice(@RequestParam(value = "invoice_id",
            required = true) String invoiceId, @RequestParam(value = "pid", required = false) String pid) {

        if (StringUtils.isBlank(invoiceId)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid invoice id");
        }

        try {
            Map<String, Object> response = gRNItemService.getTotalScanByInvoice(invoiceId, pid);
            ResponseDTO<Result<Map<String, Object>>> responseDTO = new ResponseDTO<>(new Result<>(response, null));
            responseDTO.setDisplayMessage("Pid total scanned count for given Invoice");
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN item controller : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/{grn_code}/export-barcode-details")
    @ApiOperation(value = "GET Api to export GRN barcode details")
    public void exportGRNBarcodeDetails(@PathVariable("grn_code") String grnCode,
                                        HttpServletResponse httpServletResponse) throws IOException {
        gRNItemService.exportGRNBarcodeDetails(grnCode, httpServletResponse);
    }

    @GetMapping("grn-item")
    @ApiOperation(value = "GET Api to fetch GRN item barcode details")
    @RestLogging
    public ResponseDTO<Result<GRNItem>> getBarcodeDetails(@RequestParam(value = "barcode", required = true) String barcode,
                                                          @RequestParam(value = "facility_code", required = false) String facilityCode) {
        try {
            GRNItem grnItem = gRNItemService.searchItem(barcode, facilityCode, true);
            ResponseDTO<Result<GRNItem>> responseDTO = new ResponseDTO<>(new Result<>(grnItem, null));
            responseDTO.setDisplayMessage("GRN Item fetched successfully");
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN item controller : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("reset/redis-counter")
    @ApiOperation(value = "PUT Api to update barcode scan counter in redis from db")
    @RestLogging
    public ResponseDTO<Result<Map<String, Long>>> resetRedisCounter(@RequestParam(required = false) List<String> invoiceRefNumbers,
                                                                    @RequestParam(required = false) List<String> poIds) {
        try {
            Map<String, Long> resetRedisCounter = gRNItemService.resetRedisCounter(invoiceRefNumbers, poIds);
            ResponseDTO<Result<Map<String, Long>>> responseDTO = new ResponseDTO<>(new Result<>(resetRedisCounter, null));
            responseDTO.setDisplayMessage("Redis counter reset successfully");
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (ApplicationException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred while resetting redis counter : ", ex);
            throw new ApplicationException("Invalid Request, " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Check Duplicate barcode")
    @GetMapping(value = "/validate/duplicate/barcode")
    public ResponseDTO<Boolean> validateDuplicateBarcode(@RequestParam("barcode") String barcode,
                                                         @RequestParam("facility_code") String facilityCode) {
        if (StringUtils.isBlank(barcode)) {
            throw new ApplicationException("Barcode can not be null", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        try{
            Boolean duplicateBarcode = gRNItemService.validateDuplicateBarcode(barcode, facilityCode);
            return new ResponseDTO<Boolean>(duplicateBarcode);
        } catch (Exception ex){
            log.error("Exception occurred in validating barcode : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Scan grn barcode")
    @PostMapping(value = "/scan/grn/barcode")
    public ResponseDTO<GRNItem> scanGrnItemBarcode(@RequestBody GRNScanItemRequest grnScanItemRequest) {
        try{
            GRNItem grnItem = gRNItemService.scanGrnItemBarcode(grnScanItemRequest);
            return new ResponseDTO<GRNItem>(grnItem);
        } catch (Exception ex){
            log.error("Exception occurred in validating barcode : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Scan grn barcode")
    @PostMapping(value = "/sync/ems/barcode")
    public ResponseDTO<Boolean> syncBarcodeToEMS(@RequestBody GRNScanItemRequest grnScanItemRequest) {
        try{
            gRNItemService.pushMessageToEmsKafka(grnScanItemRequest);
            return new ResponseDTO<Boolean>(true);
        } catch (Exception ex){
            log.error("Exception occurred in ems sync : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Scan grn barcode")
    @PostMapping(value = "/sync/ems/barcode/V2")
    public ResponseDTO<Boolean> syncBarcodeToEMSV2(@RequestBody GRNScanItemRequest grnScanItemRequest) {
        try{
            gRNItemService.pushMessageToEmsKafka(grnScanItemRequest);
            return new ResponseDTO<Boolean>(true);
        } catch (Exception ex){
            log.error("Exception occurred in ems sync : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Sync grn barcode to EMS")
    @PostMapping(value = "/sync/grn/barcode")
    public ResponseDTO<Map<String, List<String>>> syncBarcodeInEMS(@RequestParam(value = "barcode_list", required = false)
                                                             List<String> barcodeList,
                                                 @RequestParam(value = "ems_sync_status", required = false)
                                                         String emsSyncStatus) {
        try{
            Map<String, List<String>> syncedList = gRNItemService.syncBarcodeInEMS(barcodeList, emsSyncStatus);
            return new ResponseDTO<Map<String, List<String>>>(syncedList);
        } catch (Exception ex){
            log.error("Exception occurred in validating barcode : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Get barcode price")
    @PostMapping(value = "/get/barcode/price")
    public List<BarcodePriceResponse> getBarcodePrice(@RequestBody BarcodePriceRequest barcodePriceRequest) {
        try {
            List<BarcodePriceResponse> barcodePrice = gRNItemService.getBarcodePrice(barcodePriceRequest);
            return barcodePrice;
        } catch (Exception ex){
            log.error("Exception occurred in validating barcode : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Get barcode expiry date")
    @PostMapping(value = "/get/barcode/expiryDate")
	public ResponseDTO<List<BarcodeExpiryDateResponse>> getBarcodeExpiryDate(
	        @RequestParam(value = "barcode_list", required = false) List<String> barcodeList,
            @RequestBody(required = false) List<String> barcodeReqList) {
		try {
			List<BarcodeExpiryDateResponse> barcodePrice = gRNItemService.getBarcodeExpiryDate(barcodeList, barcodeReqList);
			return new ResponseDTO<List<BarcodeExpiryDateResponse>>(barcodePrice);
		} catch (Exception ex) {
			log.error("Exception occurred in validating barcode : ", ex);
			throw new ApplicationException("Exception : " + ex.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}
}
