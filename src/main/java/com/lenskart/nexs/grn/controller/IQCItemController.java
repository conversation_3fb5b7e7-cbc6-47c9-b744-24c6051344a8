package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.baseController.BaseController;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.common.entity.entityService.grn.IqcGrnProductEntityService;
import com.lenskart.nexs.common.entity.po.grn.IqcGrnProductEntity;
import com.lenskart.nexs.common.model.response.grn.ScanBarcodeResponseModel;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.constants.responseMessage.ResponseCodes;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.IQCConstants;
import com.lenskart.nexs.grn.dto.request.ScanIQCItemRequestDTO;
import com.lenskart.nexs.grn.dto.response.IqcGrnScanItemResponseDTO;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.dto.response.Result;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.service.GRNMasterService;
import com.lenskart.nexs.grn.service.IQCItemService;
import com.nexs.po.common.enums.IqcGrnProductStatusEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/nexs/api/grn/v1")
@Slf4j
public class IQCItemController extends BaseController {

    @Autowired
    private IQCItemService iQCItemService;

    @Autowired
    private IqcGrnProductEntityService iqcGrnProductEntityService;

    @Autowired
    private GRNMasterService grnMasterService;

    @RestLogging
    @ApiOperation(value = "Scan iqc barcode")
    @PostMapping(value = "/scan/iqc/barcode")
    public ResponseDTO<Result<IqcGrnScanItemResponseDTO>> scanIqcGrnItemBarcode(
            @RequestBody ScanIQCItemRequestDTO scanIQCItemRequestDTO) {
        try {
            log.info("Scanning IQC barcode {} with grn code: {}", scanIQCItemRequestDTO.getBarcode(), scanIQCItemRequestDTO.getGrnCode());
            IqcGrnScanItemResponseDTO response = iQCItemService.scanIQCItem(scanIQCItemRequestDTO);
            log.info("Response {} for barcode {} with grn code {}",
                    response, scanIQCItemRequestDTO.getBarcode(), scanIQCItemRequestDTO.getGrnCode());
            ResponseDTO<Result<IqcGrnScanItemResponseDTO>> responseDTO = new ResponseDTO<>(new Result<>(response, null));
            responseDTO.setDisplayMessage(IQCConstants.ITEM_SCAN_SUCCESS);
            return responseDTO;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred during IQC item scan : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Complete iqc for box barcode")
    @PostMapping(value = "/complete/iqc")
    ResponseDTO completeIqc(@RequestParam(value = "box_code") String boxCode,
                            @RequestParam(value = "grn_code") String grnCode,
                            @RequestParam(value = "pid", required = false) String pid) {
        try {
            String userId = MDC.get("USER_ID");
            log.info("Initiating request for IQC Complete for boxCode{} and grnCode{}", boxCode, grnCode);
            if (StringUtils.isBlank(grnCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "grnCode parameter invalid or missing");
            IqcGrnProductEntity iqcGrnProductEntity = iQCItemService.completeIqc(boxCode, grnCode, pid);
            ResponseDTO<Result<IqcGrnProductEntity>> responseDTO = new ResponseDTO<>(new Result<>(iqcGrnProductEntity, null));
            if (IqcGrnProductStatusEnum.IQC_DONE.equals(iqcGrnProductEntity.getStatus())) {
                log.info("IQC GRN done for boxCode{} and grnCode{}", boxCode, grnCode);
                responseDTO.setDisplayMessage(IQCConstants.IQC_DONE);
            }
            return responseDTO;

        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Exception occurred in completing IQC : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Get iqc details for a grn code")
    @GetMapping(value = "/get/iqc/list")
    public ResponseEntity<BaseResponseModel> getIqcGrnProductList(@RequestParam(value = "grn_code") String grnCode,
                                                                  @RequestParam(value = "page_num", defaultValue = "0") Integer pageNum,
                                                                  @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize,
                                                                  @RequestParam(value = "sort_by", defaultValue = "pid") String sortBy,
                                                                  @RequestParam(value = "sort_order", defaultValue = "asc") String sortOrder) {
        log.info("Grn code: {}, page_num: {}, pageSize {}, sortBy {}, sort_order {} in [getIqcGrnProductList]",
                grnCode, pageNum, pageSize, sortBy, sortOrder);
        List<IqcGrnProductEntity> iqcGrnProductEntityList = iqcGrnProductEntityService.findByGrnCodeAndPageable(grnCode,
                this.buildPageableRequest(pageNum, pageSize, sortBy, sortOrder));
        log.info("IqcGrnProductEntityList in [getIqcGrnProductList] : {}", iqcGrnProductEntityList);
        Map<String, Object> iqcBarcodesList = iQCItemService.getIqcBarcodesDetails(iqcGrnProductEntityList, grnCode);
        Map<String, Object> returnData = new HashMap<>();
        returnData.put("results", iqcBarcodesList);
        int hits = iqcGrnProductEntityService.findCountByGrnCode(grnCode);
        log.info("Hits in [getIqcGrnProductList]", hits);
        returnData.put("hits", hits);
        return responseBuilder.successResponse(returnData, "success", ResponseCodes.RESPONSE_SUCCESS);
    }

    private Pageable buildPageableRequest(Integer pageNum, Integer pageSize, String sortBy, String sortOrder) {
        PageRequest pageable = PageRequest.of(pageNum, pageSize, Sort.Direction.fromString(sortOrder),
                sortBy);
        log.info("Pageable Object: {} in [buildPageableRequest]", pageable);
        return (Pageable) pageable;
    }

    @RestLogging
    @ApiOperation(value = "DELETE Api to delete scanned item barcode in a IQC GRN")
    @PutMapping("/delete/iqc")
    ResponseDTO<Result<IqcGrnScanItemResponseDTO>> deleteIqcScanItem(@RequestParam(value = "barcode", required = true) String barCode, @RequestParam(value = "grn_code", required = true) String grnCode) {
        try {
            IqcGrnScanItemResponseDTO response = iQCItemService.deleteIqcScanItem(barCode, grnCode);
            ResponseDTO<Result<IqcGrnScanItemResponseDTO>> responseDTO = new ResponseDTO<>(new Result<>(response, null));
            responseDTO.setDisplayMessage("Barcode deleted successfully");
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN item controller : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "Scan box barcode/barcode to get iqc details")
    @PostMapping(value = "/get/iqc/details")
    public Object scanBoxOrBarCode(@RequestParam(value = "grn_code", required = false) String grnCode,
                                   @RequestParam(value = "pid", required = false) String pid,
                                   @RequestParam(value = "barcode", required = false) String barcode,
                                   @RequestHeader(value = "facility-code", required = true) String facility) throws Exception {
        log.info("Grncode : {}, pid : {}, barcode : {}, facility-code : {} in [scanBoxOrBarCode]", grnCode, pid, barcode, facility);
        ScanBarcodeResponseModel scanBarcodeResponseModel = iQCItemService.scanBoxOrBarCode(grnCode, pid, barcode, facility);
        log.info("ScanBarcodeResponseModel: {}", scanBarcodeResponseModel);
        return responseBuilder.successResponse(scanBarcodeResponseModel, "success", ResponseCodes.RESPONSE_SUCCESS);
    }


    @RestLogging
    @ApiOperation(value = "Add grn product sampling detail")
    @PostMapping(value = "/save/grn/product")
    public ResponseEntity<BaseResponseModel> saveGrnProductSamplingDetails(
            @RequestParam(value = "grn_code") String grnCode) {
        try {
            List<IqcGrnProductEntity> result = grnMasterService.saveGrnProductSamplingDetails(grnCode);
            ResponseEntity<BaseResponseModel> response =
                    responseBuilder.successResponse(result, "GRN Product sampling " +
                                    "details saved successfully",
                            GRNConstants.GRN_SUCCESS);
            return response;
        } catch (Exception ex) {
            log.error("Exception occurred in creating grn : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Move IQC_DONE iqc grn product status to IQC_COMPLETE")
    @PostMapping(value = "/mark/grn/product/iqcComplete")
    public ResponseEntity<BaseResponseModel> markGrnIqcComplete(@RequestParam(value = "grn_code") String grnCode,
                                                                @RequestParam(value = "box_code", required = false) String boxCode,
                                                                @RequestParam(value = "pid", required = false) String pid) {
        try {
            String result = iQCItemService.markGrnIqcComplete(grnCode, boxCode, pid);
            ResponseEntity<BaseResponseModel> response =
                    responseBuilder.successResponse(result, "GRN product successfully iqc complete",
                            GRNConstants.GRN_SUCCESS);
            return response;
        } catch (Exception ex) {
            log.error("Exception occurred in mark grn product iqc complete grnCode {}, boxCode {}, error {}",
                    grnCode, boxCode, ex.getMessage());
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
