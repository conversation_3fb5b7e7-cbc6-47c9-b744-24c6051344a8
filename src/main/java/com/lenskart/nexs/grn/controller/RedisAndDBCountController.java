package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.model.RedisCountDBCountDiffResponse;
import com.lenskart.nexs.grn.service.RedisAndDBCountService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RequestMapping("/nexs/api/grn/v1/master/count/")
@RestController
@Slf4j
public class RedisAndDBCountController {

    @Autowired
    RedisAndDBCountService redisAndDBCountService;

//    @Autowired
//    CommunicationService communicationService;
//
//    @Value("${from.email.id}")
//    String fromEmailId;

    @PostMapping("getRedisAndDBCountDiff")
    @ApiOperation(value = "POST Api to get count difference between DB and Redis")
    @RestLogging
    public ResponseDTO<List<RedisCountDBCountDiffResponse>> getRedisAndDBCountDiff(@RequestParam String type, @RequestBody Set<String> itemIds) {
        log.info("Received request for getRedisAndDBCountDiff with input type:{} and invoice numbers: {}",type, itemIds);
        List<RedisCountDBCountDiffResponse> redisCountDBCountDiffResponses = redisAndDBCountService.getRedisAndDBCountDiff(type, itemIds);
        log.info("Response list size for getRedisAndDBCountDiff with input type: {} is {}", type, redisCountDBCountDiffResponses.size());
//        try {
//            communicationService.sendMail(redisCountDBCountDiffResponses.toString(), fromEmailId, new String[]{"<EMAIL>"},"GET Api to get count difference between DB and Redis");
//        } catch (IOException e) {
//            throw new ApplicationException("IO exception while sending email: "+e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        } catch (SendGridException e) {
//            throw new ApplicationException("SendGrid exception while sending email: "+e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        }

        return new ResponseDTO<>(redisCountDBCountDiffResponses);
    }
}
