package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.grn.dto.response.ASNResponseDTO;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.dto.response.Result;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.service.ASNService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

@RestController
@RequestMapping("/nexs/api/grn/v1/asn")
@Validated
@Slf4j
public class ASNController {

    @Autowired
    private ASNService asnService;

    @GetMapping("/barcode")
    @ApiOperation(value = "GET Api to fetch ASN Barcode details")
    @RestLogging
    public ResponseDTO<Result<ASNResponseDTO>> getASN(@RequestParam("po_number") String poId,
                                                      @RequestParam("vendor_invoice_num") String vendorInvoiceNum,
                                                      @RequestParam("barcode") String barcode,
                                                      @RequestHeader(value="facility-code", required=false) String facilityCode) {
        if (StringUtils.isBlank(facilityCode))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");

        try {
            ASNResponseDTO asnResponse = asnService.getASN(poId, vendorInvoiceNum, barcode);
            ResponseDTO<Result<ASNResponseDTO>> responseDTO = new ResponseDTO<>(new Result<>(asnResponse, null));
            responseDTO.setDisplayMessage("ASN fetched successfully");
            return responseDTO;
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in ASN controller : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
