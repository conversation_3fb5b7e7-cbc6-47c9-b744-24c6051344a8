package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.ems.model.AutoGrnScanDetails;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dto.request.*;
import com.lenskart.nexs.grn.dto.response.*;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.service.GRNMasterService;
import com.lenskart.nexs.grn.service.GRNPIDMasterService;
import com.lenskart.nexs.grn.util.GRNUtils;
import com.lenskart.nexs.responseBuilder.ResponseBuilder;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/nexs/api/grn/v1/master/")
@Validated
public class GRNMasterController {

    @Autowired
    private GRNMasterService grnMasterService;

    @Autowired
    private GRNPIDMasterService grnPIDMasterService;

    @Autowired
    private GRNConfig grnConfig;

    @Autowired
    protected ResponseBuilder responseBuilder;

    @Value("${lkauth.token.header.name}")
    private String lkauthHeaderTokenName;

    @Value("${lkauth.authme.validation.enabled}")
    private boolean authMeValidation;

    @Value("${lkauth.authme.url}")
    private String lkauthMeUrl;

    @RestLogging
    @ApiOperation(value = "POST Api to create GRN/Start new GRN")
    @PostMapping(value = "/create")
    public ResponseDTO<Result<CreateGRNResponseDTO>> createGRN(@RequestHeader(value = "facility-code", required = false) String facilityCode,
                                                               @RequestBody @Valid CreateGRNMasterDTO grnMasterCreateRequestDTO) {
        try {
            if (StringUtils.isBlank(facilityCode)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
            }
            grnMasterCreateRequestDTO.setFacilityCode(facilityCode);

            log.info("[validateJitPOType] Create Grn po {}, type {}", grnMasterCreateRequestDTO.getPo().getPoId(), grnMasterCreateRequestDTO.getType());
            grnMasterService.validatePOType(grnMasterCreateRequestDTO.getPo().getPoId(), grnMasterCreateRequestDTO.getInvoice().getInvoiceId(), grnMasterCreateRequestDTO.getType());

            log.info("Received request to create GRN, grnMasterCreateRequestDTO: {} }", grnMasterCreateRequestDTO);

            CreateGRNResponseDTO response = grnMasterService.createGRN(grnMasterCreateRequestDTO, facilityCode, null);
            ResponseDTO<Result<CreateGRNResponseDTO>> responseDto = new ResponseDTO<>(new Result<CreateGRNResponseDTO>(response, response.getMeta()));
            responseDto.setDisplayMessage(GRNConstants.GRN_MASTER_CREATED);

            log.info("Response to request for creating GRN, grnMasterCreateRequestDTO:{} and  response DTO: {} ", grnMasterCreateRequestDTO, responseDto);

            return responseDto;
        } catch (ResponseStatusException ne) {
            log.error("Exception while creating grn : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while creating grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @ApiOperation(value = "GET Api to fetch details of a GRN")
    @GetMapping(value = "/get/{grnCode}")
    public ResponseDTO<Result<GetGRNResponseDTO>> getGRN(@PathVariable("grnCode") String grnCode) {
        try {
            GetGRNResponseDTO response = grnMasterService.getGRN(grnCode);
            ResponseDTO<Result<GetGRNResponseDTO>> responseDto = new ResponseDTO<>(new Result<GetGRNResponseDTO>(response, response.getMeta()));
            responseDto.setDisplayMessage(GRNConstants.GRN_MASTER_FETCHED);
            return responseDto;
        } catch (ResponseStatusException ne) {
            log.error("Exception while get grn summary : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while fetching grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @ApiOperation(value = "PUT Api to update GRN status")
    @PutMapping(value = "/update")
    public void editGRN(@Valid @RequestBody GRNUpdateDTO grnUpdateDTO,
                        @RequestHeader(value = "facility-code", required = false) String facilityCode) {
        try {
            if (StringUtils.isBlank(facilityCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
            grnMasterService.editGRN(grnUpdateDTO, facilityCode);
        } catch (ResponseStatusException ne) {
            log.error("Exception while update grn : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while updating grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @ApiOperation(value = "PUT Api to mark GRN close/complete")
    @PutMapping(value = "/close/{grnCode}")
    public ResponseDTO<Result<Object>> closeGRN(@PathVariable("grnCode") String grnCode,
                                                @RequestHeader(value = "facility-code", required = false) String facilityCode,
                                                @RequestHeader(value = "showPutaway", required = false) String showPutaway,
                                                @RequestHeader(value = "createPutaway", required = false, defaultValue = "true") boolean createPutaway,
                                                HttpServletRequest request) {
        try {
            MDC.put("showPutaway", showPutaway);
            if (StringUtils.isBlank(facilityCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
            GRNMaster grnMaster = grnMasterService.getGRNMaster(grnCode);
            if (grnMaster != null && MDC.get("FACILITY_CODE") != null && grnMaster.getFacility() != null && !grnMaster.getFacility().equals(MDC.get("FACILITY_CODE"))) {
                throw new Exception("Incorrect facility chosen in header");
            }
            if (!GRNUtils.canThisPersonCloseGRN(request, grnMaster.getCreatedBy(), grnConfig.getAuthSecretKey(), grnConfig.getAppName(), lkauthHeaderTokenName, authMeValidation, lkauthMeUrl))
                throw new ResponseStatusException(HttpStatus.METHOD_NOT_ALLOWED, "User doesn't have access to close this GRN");
            grnMasterService.closeGRN(grnCode, facilityCode, grnMaster, createPutaway);
            ResponseDTO<Result<Object>> responseDTO = new ResponseDTO<>(new Result<>(null, null));
            responseDTO.setDisplayMessage(GRNConstants.GRN_MASTER_CLOSED);
            return responseDTO;
        } catch (ResponseStatusException ne) {
            log.error("Exception while close grn : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while closing grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @ApiOperation(value = "PUT Api to mark all GRNs of an invoice close/complete")
    @PutMapping(value = "/close-grns/{invoiceRefNum}")
    public ResponseDTO<Result<Object>> closeAllGRN(@PathVariable("invoiceRefNum") String invoiceRefNum,
                                                   @RequestHeader(value = "facility-code", required = false) String facilityCode,
                                                   @RequestHeader(value = "showPutaway", required = false) String showPutaway, HttpServletRequest request) {
        try {
            MDC.put("showPutaway", showPutaway);
            if (StringUtils.isBlank(facilityCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
            if (!GRNUtils.canThisPersonCloseAllGRN(request, grnConfig.getAuthSecretKey(), grnConfig.getAppName(), lkauthHeaderTokenName, authMeValidation, lkauthMeUrl))
                throw new ResponseStatusException(HttpStatus.METHOD_NOT_ALLOWED, "User doesn't have access to close all GRNs");
            grnMasterService.closeAllGRN(invoiceRefNum, facilityCode);
            ResponseDTO<Result<Object>> responseDTO = new ResponseDTO<>(new Result<>(null, null));
            responseDTO.setDisplayMessage("Successfully closed all GRNs of invoice : " + invoiceRefNum);
            return responseDTO;
        } catch (ResponseStatusException ne) {
            log.error("Exception while close grn : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while closing grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

//    @RestLogging
//    @ApiOperation(value = "PUT Api to update estimated quantity against a GRN and PID")
//    @PutMapping("/update/estimated-quantity")
//    public void updateEstimatedQuantity(@Valid @RequestBody EstimatedQtyChangeReq request) {
//        try {
//            grnMasterService.updateEstimatedQty(request);
//        } catch (ResponseStatusException e) {
//            throw e;
//        } catch (Exception ex) {
//            log.error("Unexpected exception occurred in GRN master controller : ", ex);
//            throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, ex.getMessage());
//        }
//    }

    @RestLogging
    @ApiOperation(value = "PUT Api to assign GRN to a user")
    @PutMapping("/assign-grn")
    public void assignGRN(@RequestHeader(value = "facility-code", required = false) String facilityCode, @Valid @RequestBody UserActivity userActivity) {

        if (StringUtils.isBlank(userActivity.getAssignedTo())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "User id of user, grn is assigned to is missing");
        }

        if (StringUtils.isBlank(facilityCode)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
        }

        userActivity.setFacility(facilityCode);
        try {
            grnMasterService.assignGRN(userActivity);
        } catch (ResponseStatusException ne) {
            log.error("Exception while assign grn : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while assigning grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @ApiOperation(value = "PUT Api to mark GRN as manual-override")
    @PutMapping("/{grn_code}/product/{pid}/manual-override")
    public void setManualOverride(@PathVariable("grn_code") String grnCode, @PathVariable("pid") String pid,
                                  @RequestParam(value = "isEnabled", required = true) Boolean isEnabled) {

        if (StringUtils.isBlank(grnCode) || StringUtils.isBlank(pid) || isEnabled == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Either grn code or pid is invalid");
        }

        try {
            grnPIDMasterService.setManualOverride(grnCode, pid, isEnabled);
        } catch (ResponseStatusException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while setting manual override : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @PostMapping("/add/grn-pid")
    @ApiOperation(value = "POST Api to add PID in GRN")
    public ResponseDTO<Result<GRNAddPidResponseDTO>> addGrnPID(@Valid @RequestBody CreateGRNPIDTO grnpidMasterDTO, @RequestHeader(value = "facility-code", required = false) String facilityCode) {
        try {
            GRNAddPidResponseDTO response = grnMasterService.addGrnPID(grnpidMasterDTO, facilityCode);
            ResponseDTO<Result<GRNAddPidResponseDTO>> responseDto = new ResponseDTO<>(new Result<GRNAddPidResponseDTO>(response, response.getMeta()));
            responseDto.setDisplayMessage(GRNConstants.GRN_PID_ADDED);
            return responseDto;
        } catch (ResponseStatusException ne) {
            log.error("Exception while add pid to grn : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while adding pid to grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @GetMapping(value = "/summary/{grnCode}")
    @ApiOperation(value = "GET Api to fetch GRN summary")
    public ResponseDTO<Result<GRNSummaryDTO>> getGRNSummary(@RequestHeader(value = "facility-code", required = false) String facilityCode, @PathVariable("grnCode") String grnCode) {

        if (StringUtils.isBlank(facilityCode)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
        }

        try {
            return grnMasterService.getGRNSummary(grnCode, facilityCode);
        } catch (ResponseStatusException ne) {
            log.error("Exception while get grn summary  : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while fetching grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @GetMapping(value = "/grn-details")
    @ApiOperation(value = "GET Api to fetch all GRNs with details against an INVOICE")
    public ResponseDTO<Result<List<GRNDetailsDTO>>> getGRNDetailsByInvoice(@RequestParam(value = "invoice", required = true) String invoiceId) {

        long startTime = Calendar.getInstance().getTimeInMillis();
        if (StringUtils.isBlank(invoiceId)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid invoice id");
        }

        try {
            List<GRNDetailsDTO> grnList = grnMasterService.getGRNDetailsByInvoice(invoiceId);
            return new ResponseDTO<Result<List<GRNDetailsDTO>>>(new Result<List<GRNDetailsDTO>>(grnList));
        } catch (ResponseStatusException ne) {
            log.error("Exception while get grn details  : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while fetching grn list : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        } finally {
            log.info("Invoice summary total time taken for {} : {}", invoiceId, Calendar.getInstance().getTimeInMillis() - startTime);
        }
    }

    @RestLogging
    @ApiOperation(value = "GET Api to fetch GRN list against INVOICE")
    @GetMapping(value = "/grn-list")
    public ResponseDTO<Result<List<GRNDetailsDTO>>> getGRNListByInvoice(@RequestParam(value = "invoice", required = true) String invoiceId) {

        if (StringUtils.isBlank(invoiceId)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid invoice id");
        }

        try {
            List<GRNDetailsDTO> grnList = grnMasterService.getGRNListByInvoice(invoiceId);
            return new ResponseDTO<Result<List<GRNDetailsDTO>>>(new Result<List<GRNDetailsDTO>>(grnList));
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while fetching grn list : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @ApiOperation(value = "GET Api to check is all GRN closed against INVOICE")
    @GetMapping(value = "/check-grn-closed")
    public ResponseDTO<Boolean> checkAllGRNClosed(@RequestParam(value = "invoice_id", required = true) String invoiceId,
                                                  @RequestParam(value = "pid", required = false) String pid) {

        if (StringUtils.isBlank(invoiceId)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid invoice id");
        }

        boolean result = grnMasterService.checkAllGRNClosedForInvoice(invoiceId, pid);
        return new ResponseDTO<Boolean>(result, GRNConstants.GRN_SUCCESS);

    }

    @RestLogging
    @ApiOperation(value = "GET Api to search GRN")
    @GetMapping(value = "/search")
    public ResponseDTO<SearchResponse<List<GRNSearchResponseDTO>>> searchGRN(@RequestHeader(value = "facility-code", required = false) String facilityCode, @ModelAttribute GRNSearchDTO grnSearchReq) {

        if (StringUtils.isBlank(facilityCode)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
        }
        grnSearchReq.setFacility(facilityCode);
        SearchResponse<List<GRNSearchResponseDTO>> grnSearchResponse = grnMasterService.grnSearch(grnSearchReq);
        return new ResponseDTO<SearchResponse<List<GRNSearchResponseDTO>>>(grnSearchResponse);
    }

    @ApiOperation(value = "GET Api to export GRN Details")
    @GetMapping(value = "export-details")
    public void exportGRNDetails(@ModelAttribute GRNSearchDTO grnSearchReq, HttpServletResponse response) throws IOException {
        grnMasterService.exportGRNDetails(grnSearchReq, response);
    }

    @RestLogging
    @ApiOperation(value = "GET Api to check if any GRN open against INVOICE and PID")
    @PostMapping(value = "/is-grn-open")
    public ResponseDTO<Map<String, Boolean>> isGRNOpen(@Valid @RequestBody IsGRNOpenReqDTO request) {

        Map<String, Boolean> result = grnMasterService.isGRNOpen(request.getInvoiceId(), request.getPids());
        return new ResponseDTO<Map<String, Boolean>>(result, GRNConstants.GRN_SUCCESS);

    }

    @GetMapping(value = "/grn-pdf/{grnCode}")
    @ApiOperation(value = "GET Api to fetch GRN pdf")
    public ResponseEntity<InputStreamResource> getGRNPdf(@RequestHeader(value = "facility-code", required = false) String facilityCode, @PathVariable("grnCode") String grnCode) {
        log.info("grn view pdf generator with grn_code : " + grnCode);
        if (StringUtils.isBlank(facilityCode)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
        }

        try {
            InputStream inputStream = grnMasterService.getGRNPdf(grnCode, facilityCode);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Disposition", "filename=output.pdf");
            httpHeaders.setContentDispositionFormData("output.pdf", "output.pdf");
            return ResponseEntity
                    .ok()
                    .headers(httpHeaders)
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(new InputStreamResource(inputStream));
        } catch (ResponseStatusException ne) {
            log.error("Exception while creating grn pdf  : ", ne);
            throw ne;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while fetching grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @GetMapping(value = "/closed-grns")
    @ApiOperation(value = "GET API to fetch closed grn of a invoice_ref_num")
    public GRNListDTO getClosedGRNs(@RequestParam(value = "invoice_ref_num") String invoiceRefNum,
                                    @RequestHeader(value = "facility-code", required = false) String facilityCode) {
        if (StringUtils.isBlank(facilityCode))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");

        if (StringUtils.isBlank(invoiceRefNum))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "invoice_ref_num must not be blank");

        List<Map<String, String>> closedGrns = grnMasterService.getClosedGRNs(invoiceRefNum, facilityCode);
        GRNListDTO grnListDTO = new GRNListDTO();
        grnListDTO.setSuccess(true);
        grnListDTO.setGrns(closedGrns);
        return grnListDTO;
    }

    @RestLogging
    @ApiOperation(value = "GET Api to fetch GRN list against PurchaseOrder")
    @GetMapping(value = "/po-grn-list")
    public ResponseDTO<Result<List<String>>> getGRNListByPO(@RequestParam(value = "poNum", required = true) String poNum) {

        if (StringUtils.isBlank(poNum)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid poNum");
        }

        try {
            List<String> grnList = grnMasterService.getGRNListByPO(poNum);
            return new ResponseDTO<Result<List<String>>>(new Result<List<String>>(grnList));
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in GRN master controller : ", ex);
            throw new ApplicationException("Error while fetching grn list : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    @RestLogging
    @ApiOperation(value = "Prod fix API")
    @PostMapping(value = "/temp/closeGrns")
    public ResponseDTO<List<String>> closeGRNEvent(@RequestBody GRNCloseEvent grnCloseEvent,
                                                   @RequestHeader(value = "facility-code", required = false) String facilityCode) {

        List<String> successfulGRNs = new ArrayList<>();

        for (String grnCode : grnCloseEvent.getGrnList()) {
            try {
                if (StringUtils.isBlank(facilityCode)) {
                    log.error("facility-code header value invalid or missing , grnCloseEvent: {}", grnCloseEvent);
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
                }
                GRNMaster grnMaster = grnMasterService.getGRNMaster(grnCode);
                grnMasterService.closeGRNEvent(grnCode, facilityCode, grnMaster);
                ResponseDTO<Result<Object>> responseDTO = new ResponseDTO<>(new Result<>(null, null));
                responseDTO.setDisplayMessage(GRNConstants.GRN_MASTER_CLOSED);
                successfulGRNs.add(grnCode);
            } catch (ResponseStatusException ne) {
                log.error("Exception while close grn : ", ne);
                throw ne;
            } catch (Exception ex) {
                log.error("Unexpected exception occurred in GRN master controller : ", ex);
                throw new ApplicationException("Error while closing grn : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            }
        }

        return new ResponseDTO<>(successfulGRNs);
    }

    @RestLogging
    @ApiOperation(value = "GET Api to get all GRN code against INVOICE")
    @GetMapping(value = "/get-grn-numbers")
    public ResponseDTO<String> getGrnCodes(@RequestParam(value = "invoice_id", required = true) String invoiceId) {
        if (StringUtils.isBlank(invoiceId)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid invoice id");
        }
        String result = grnMasterService.getGRNListByPOInvoice(invoiceId);
        return new ResponseDTO<String>(result, GRNConstants.GRN_SUCCESS);
    }

    @RestLogging
    @ApiOperation(value = "Post API to create transfer GRN")
    @PostMapping(value = "/create/transfer/grn")
    public ResponseEntity<BaseResponseModel> createTransferGRN(
            @RequestBody CreateTransferGrnRequest request) throws Exception {
        String result = grnMasterService.createTransferGRN(request);
        ResponseEntity<BaseResponseModel> response =
                responseBuilder.successResponse(result, "GRN created successfully", GRNConstants.GRN_SUCCESS);
        return response;
    }

    @RestLogging
    @ApiOperation(value = "Create Grn using vendor invoice number")
    @PostMapping(value = "/create/invoice/grn")
    public ResponseEntity<BaseResponseModel> createGrn(@RequestBody CreateGrnMasterRequest request) {
        try{
            String result = grnMasterService.createGRNMaster(request);
            ResponseEntity<BaseResponseModel> response =
                    responseBuilder.successResponse(result, "GRN created successfully", GRNConstants.GRN_SUCCESS);
            return response;
        } catch (Exception ex){
            log.error("Exception occurred in creating grn : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @RestLogging
    @ApiOperation(value = "Create Grn")
    @PostMapping(value = "/internal/create/grn/details")
    public ResponseEntity<BaseResponseModel> createGrnDetails(@RequestBody AutoGrnScanDetails request) {
        try {
            AutoGrnScanDetails result = grnMasterService.createGrnDetails(request);
            ResponseEntity<BaseResponseModel> response =
                    responseBuilder.successResponse(result, "GRN created successfully", GRNConstants.GRN_SUCCESS);
            return response;
        } catch (Exception ex) {
            log.error("Exception occurred in creating grn : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "Post Api to mark GRN close/complete")
    @PostMapping(value = "/internal/closeAutoGRN")
    public ResponseDTO<String> closeAutoGRN(@RequestBody AutoGrnScanDetails autoGrnScanDetails) {
        try {
        	String facilityCode = autoGrnScanDetails.getFacilityCode();
        	String createdBy = autoGrnScanDetails.getCreatedBy();
        	String grnCode = autoGrnScanDetails.getGrnCode();
        	log.info("[closeAutoGRN] Received auto close request for grn {} and facility {} : {}",
        			grnCode, facilityCode, autoGrnScanDetails);
            MDC.put("showPutaway", "true");
            MDC.put("FACILITY_CODE", facilityCode);
            MDC.put("USER_ID", createdBy);
            if (StringUtils.isBlank(facilityCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Facility code is invalid or missing");
            GRNMaster grnMaster = grnMasterService.getGRNMaster(grnCode);
			if (grnMaster == null || (grnMaster != null && MDC.get("FACILITY_CODE") != null
					&& grnMaster.getFacility() != null && !grnMaster.getFacility().equals(MDC.get("FACILITY_CODE")))) {
				log.error("[closeAutoGRN] Invalid grn_code {} passed in the request for facility {} : {}",
	        			grnCode, facilityCode, autoGrnScanDetails);
				throw new Exception("Invalid grn_code passed in the request " + grnCode);
			}
			String operation = GRNConstants.GRN_QC_PASS;
			if (!CollectionUtils.isEmpty(autoGrnScanDetails.getGrnItemDetailList())) {
				operation = autoGrnScanDetails.getGrnItemDetailList().get(0).getImsOperation();
			}
            if (!StringUtils.equalsIgnoreCase(grnMaster.getGrnStatus(), "closed")) {
            	log.info("[closeAutoGRN] Calling close call for grn {} and facility {} for status {}",
            			grnCode, facilityCode, grnMaster.getGrnStatus());
            	grnMasterService.closeGRN(grnCode, facilityCode, grnMaster, operation, false, true, true);
            }
            return new ResponseDTO<String>(GRNConstants.GRN_MASTER_CLOSED);
        } catch (Exception ex) {
            log.error("Exception while pushing message in Kafka: {}", autoGrnScanDetails, ex);
            throw new ApplicationException("Exception while pushing message in Kafka: " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "Push message to EMS for autoGrn, just for testing purpose")
    @PostMapping(value = "/internal/autoGrn/message/push/ems")
    public ResponseDTO<String> autoGrnCallPushToEMS(@RequestBody AutoGrnScanDetails autoGrnScanDetails) {
        try {
            grnMasterService.autoGrnCallPushToEMS(autoGrnScanDetails);
            return new ResponseDTO<String>("GRN Successfully pushed to EMS");
        } catch (Exception ex) {
            log.error("Exception while pushing message in Kafka: ", ex);
            throw new ApplicationException("Exception while pushing message in Kafka: " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}