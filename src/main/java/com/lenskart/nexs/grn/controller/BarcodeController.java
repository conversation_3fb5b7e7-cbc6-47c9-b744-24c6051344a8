package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.dto.response.Result;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.Barcode;
import com.lenskart.nexs.grn.service.BarcodeService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

@RestController
@RequestMapping("/nexs/api/grn/v1")
@Slf4j
public class BarcodeController {

    @Autowired
    private BarcodeService barcodeService;

    @GetMapping("/stock-in/barcode-series")
    @ApiOperation(value = "GET Api to fetch Barcode series")
    @RestLogging
    public ResponseDTO<Result<List<Barcode>>> getBarcodeSeries(@RequestHeader(value="facility-code", required=false) String facilityCode) {
        if (StringUtils.isBlank(facilityCode))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "facility-code header value invalid or missing");
        try {
            List<Barcode> barcodeList = barcodeService.getBarcodeSeries();
            ResponseDTO<Result<List<Barcode>>> responseDTO = new ResponseDTO<>(new Result<>(barcodeList, null));
            responseDTO.setDisplayMessage("Barcode Series fetched successfully");
            return responseDTO;
        } catch (Exception ex) {
            log.error("Unexpected exception occurred in Barcode controller : ", ex);
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }
}
