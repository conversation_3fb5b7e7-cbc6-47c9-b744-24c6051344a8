package com.lenskart.nexs.grn.retry;

import com.lenskart.nexs.grn.config.GRNConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ConnectTimeoutException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.server.ServerErrorException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class RetryHandler {

    public static RetryTemplate retryTemplate() {
        log.info("Max Retry "+ GRNConfig.retryMaxAttempts);
        RetryTemplate template = new RetryTemplate();
        Integer maxAttempt =  GRNConfig.retryMaxAttempts;
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(maxAttempt, getException());
        FixedBackOffPolicy fixedBackOffPolicy = new FixedBackOffPolicy();
        fixedBackOffPolicy.setBackOffPeriod(GRNConfig.retryMaxDelay);

        template.setBackOffPolicy(fixedBackOffPolicy);
        template.setRetryPolicy(retryPolicy);
        return template;
    }

    public static Map<Class<? extends Throwable>, Boolean> getException(){
        Map<Class<? extends Throwable>, Boolean> exceptions = new HashMap<>();
        exceptions.put(ConnectTimeoutException.class, true);
        exceptions.put(IOException.class, true);
        exceptions.put(RestClientException.class, true);
        exceptions.put(ServerErrorException.class, true);
        exceptions.put(HttpServerErrorException.BadGateway.class, true);
        return exceptions;
    }
}
