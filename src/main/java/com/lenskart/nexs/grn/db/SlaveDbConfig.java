package com.lenskart.nexs.grn.db;

import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Properties;

@Data
@Configuration
public class SlaveDbConfig {
    private HikariDataSource dataSource;

    private HikariDataSource dataSourceSlave;

    @Value("${slave.spring.datasource.jdbcUrl}")
    private String jdbcUrl;

    @Value("${slave.spring.datasource.username}")
    private String user;

    @Value("${slave.spring.datasource.password}")
    private String password;

    @Value("${slave.spring.datasource.driver-class-name}")
    private String driverClassName;

    @Value("${slave.spring.datasource.cachePrepStmts:true}")
    private String cachePreparedStatementsInventory;

    @Value("${slave.spring.datasource.prepStmtCacheSize:250}")
    private String preparedStatementCacheSizeInventory;

    @Value("${slave.spring.datasource.prepStmtCacheSqlLimit:2048}")
    private String preparedStatementCacheSqlLimitInventory;

    @PostConstruct
    public void setDataSource() {
        Properties properties = new Properties();
        properties.setProperty("jdbcUrl", jdbcUrl);
        properties.setProperty("dataSource.user", user);
        properties.setProperty("dataSource.password", password);
        properties.setProperty("dataSource.cachePrepStmts", cachePreparedStatementsInventory);
        properties.setProperty("dataSource.prepStmtCacheSize", preparedStatementCacheSizeInventory);
        properties.setProperty("dataSource.prepStmtCacheSqlLimit", preparedStatementCacheSqlLimitInventory);
        properties.setProperty("driverClassName", driverClassName);
        properties.setProperty("dataSource.driverClassName", driverClassName);
        dataSourceSlave = LKDatasource.createDataSource(properties);
    }
}
