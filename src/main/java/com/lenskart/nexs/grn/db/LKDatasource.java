package com.lenskart.nexs.grn.db;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LKDatasource {

    /**
     * Generic method get to create {@link HikariDataSource} , can be used to create connections to more than one DB/Schema
     * This method should be used only once, to get connection to 'A' db/schema, otherwise everytime it will create a pool of connection
     *
     * @param properties -- properties loaded from properties file at classpath. Use {@link ClassLoader} to get the classpath
     * @return HikariDataSource if there is no exception
     *
     * <B>Exits the JVM on Exception<B/>
     */
    public static HikariDataSource createDataSource(Properties properties)  {
        try {
            HikariConfig config = new HikariConfig(properties);
            log.info("Hikari Properties " + config);
            return new HikariDataSource(config);
        } catch (Exception e) {
            log.error("Exception in connecting to db : ", e);
            System.exit(0);
        }
        return null;
    }

    /**
     * Generic method get to create {@link HikariDataSource} , can be used to create connections to more than one DB/Schema
     * This method should be used only once, to get connection to 'A' db/schema, otherwise everytime it will create a pool of connection
     *
     * @param classPathFile -- file in the classpath should be used. Use {@link ClassLoader} to get the classpath
     * @return HikariDataSource if there is no exception
     *
     * <B>Exits the JVM on Exception<B/>
     */
    public static HikariDataSource createDataSource(String classPathFile)  {
        try {
            HikariConfig config = new HikariConfig(classPathFile);
            log.info("Hikari Properties " + config);
            return new HikariDataSource(config);
        } catch (Exception e) {
        	log.error("Exception in connecting to db : ", e);
            System.exit(0);
        }
        return null;
    }

    /**
     *
     * @param ds -- pass the created DS to get the connection, connection will be popped from the passed DataSource object
     * @return
     * @throws SQLException
     */
    public static Connection getDBConnection(HikariDataSource ds) throws SQLException {
        return ds.getConnection();
    }
}
