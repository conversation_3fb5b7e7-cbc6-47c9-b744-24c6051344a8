package com.lenskart.nexs.grn.db;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.grn.dto.response.GRNProduct;
import com.lenskart.nexs.grn.dto.response.PidSearchResponse;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.util.CommonUtils;

import javax.persistence.Tuple;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

public class DBUtils {

	/**
	 * @param jsonFieldsTypesMap -- set NUll if there are no json columns in the table
	 * 				<b> mysql returns json values as string, string will be converted to <i>expected</i> object</b>
	 * @param rs -- resultset
	 * @param pojo -- map will be convered to pojo which is the end result
	 * @return -- <PERSON><PERSON> of the requested type
	 * @throws SQLException
	 * @throws JsonProcessingException
	 * @throws JsonMappingException
	 */
	public static Object resultsetToPojo(Map<String, Class> jsonFieldsTypesMap, ResultSet rs, Class pojo)
			throws SQLException, JsonProcessingException, JsonMappingException {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		Map<String, Object> map = new HashMap<String, Object>();
		for (int j = 1; j <= rs.getMetaData().getColumnCount(); j++) {
			if(jsonFieldsTypesMap != null && jsonFieldsTypesMap.get(rs.getMetaData().getColumnLabel(j)) != null) //json field
			{
				map.put(rs.getMetaData().getColumnLabel(j), mapper.readValue(((String)rs.getObject(j)), jsonFieldsTypesMap.get(rs.getMetaData().getColumnLabel(j))));
			} else {
				map.put(rs.getMetaData().getColumnLabel(j), rs.getObject(j));
			}
		}
		return mapper.convertValue(map, pojo);
	}


	public static GRNMaster resultSetToMaster(ResultSet rs) throws SQLException, JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		GRNMaster grnMaster = new GRNMaster();
//		List<PIDEstimatedQty> list = new ArrayList<>();
		boolean recordFound = false;
		while (rs.next()) {
			recordFound = true;
			grnMaster.setGrnCode(rs.getString("grn_code"));
			grnMaster.setInvoice(mapper.readValue(rs.getString("invoice"), Invoice.class));
			grnMaster.setPo(mapper.readValue(rs.getString("po"), PurchaseOrder.class));
			grnMaster.setPoId(rs.getString("po_id"));
			grnMaster.setInvoiceId(rs.getString("invoice_id"));
			grnMaster.setUnicomGrnCode(rs.getString("unicom_grn_code"));
			grnMaster.setGrnStatus(rs.getString("grn_status"));
			grnMaster.setFacility(rs.getString("facility"));
			grnMaster.setCreatedBy(rs.getString("created_by"));
//			if(rs.getString("pid") != null) {
//				PIDEstimatedQty grnMasterPID = new PIDEstimatedQty(rs.getString("pid"),
//						rs.getLong("estimated_total_quantity"));
//				list.add(grnMasterPID);
//			}
		}
//		grnMaster.setEstimatedTotalQuantity(list);
		return recordFound ? grnMaster : null;
	}

	public static GRNMaster resultSetToMaster(Tuple tuple) throws SQLException, JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		GRNMaster grnMaster = new GRNMaster();
//		List<PIDEstimatedQty> list = new ArrayList<>();
		boolean recordFound = false;
		if (Objects.nonNull(tuple)) {
			recordFound = true;
			grnMaster.setGrnCode(tuple.get("grn_code",String.class));
			grnMaster.setInvoice(mapper.readValue(tuple.get("invoice",String.class), Invoice.class));
			grnMaster.setPo(mapper.readValue(tuple.get("po",String.class), PurchaseOrder.class));
			grnMaster.setPoId(tuple.get("po_id",String.class));
			grnMaster.setInvoiceId(tuple.get("invoice_id",String.class));
			grnMaster.setUnicomGrnCode(tuple.get("unicom_grn_code",String.class));
			grnMaster.setGrnStatus(tuple.get("grn_status",String.class));
			grnMaster.setFacility(tuple.get("facility",String.class));
			grnMaster.setCreatedBy(tuple.get("created_by",String.class));
//			if(tuple.get("pid",String.class) != null) {
//				PIDEstimatedQty grnMasterPID = new PIDEstimatedQty(tuple.get("pid",String.class),
//						Long.valueOf(tuple.get("estimated_total_quantity").toString()));
//				list.add(grnMasterPID);
//			}
		}
//		grnMaster.setEstimatedTotalQuantity(list);
		return recordFound ? grnMaster : null;
	}
	
	public static GRNMaster resultSetToMasterNP(ResultSet rs) throws SQLException, JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		GRNMaster grnMaster = new GRNMaster();
		boolean recordFound = false;
		if (rs.next()) {
			recordFound = true;
			grnMaster.setGrnCode(rs.getString("grn_code"));
			grnMaster.setInvoice(mapper.readValue(rs.getString("invoice"), Invoice.class));
			grnMaster.setPo(mapper.readValue(rs.getString("po"), PurchaseOrder.class));
			grnMaster.setPoId(rs.getString("po_id"));
			grnMaster.setInvoiceId(rs.getString("invoice_id"));
			grnMaster.setUnicomGrnCode(rs.getString("unicom_grn_code"));
			grnMaster.setGrnStatus(rs.getString("grn_status"));
		}
		return recordFound ? grnMaster : null;
	}

	public static GRNMaster resultSetToMasterNP(Tuple tuple) throws SQLException, JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		GRNMaster grnMaster = new GRNMaster();
		boolean recordFound = false;
		if (Objects.nonNull(tuple)) {
			recordFound = true;
			grnMaster.setGrnCode(tuple.get("grn_code", String.class));
			grnMaster.setInvoice(mapper.readValue(tuple.get("invoice", String.class), Invoice.class));
			grnMaster.setPo(mapper.readValue(tuple.get("po", String.class), PurchaseOrder.class));
			grnMaster.setPoId(tuple.get("po_id", String.class));
			grnMaster.setInvoiceId(tuple.get("invoice_id", String.class));
			grnMaster.setUnicomGrnCode(tuple.get("unicom_grn_code", String.class));
			grnMaster.setGrnStatus(tuple.get("grn_status", String.class));
		}
		return recordFound ? grnMaster : null;
	}


	public static GRNMaster resultSetToMasterSummary(ResultSet rs) throws SQLException, JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		GRNMaster grnMaster = new GRNMaster();
//		List<PIDEstimatedQty> list = new ArrayList<>();
		boolean recordFound = false;
		while (rs.next()) {
			recordFound = true;
			grnMaster.setGrnCode(rs.getString("grn_code"));
			grnMaster.setUnicomGrnCode(rs.getString("unicom_grn_code"));
			grnMaster.setGrnStatus(rs.getString("grn_status"));
			grnMaster.setPoId(rs.getString("po_id"));
			grnMaster.setCreatedAt(rs.getTimestamp("created_at"));
			grnMaster.setCreatedBy(rs.getString("created_by"));
			grnMaster.setInvoiceId(rs.getString("invoice_id"));
			grnMaster.setInvoice(mapper.readValue(rs.getString("invoice"), Invoice.class));
//			if(rs.getString("pid") != null) {
//				PIDEstimatedQty grnMasterPID = new PIDEstimatedQty(rs.getString("pid"),
//						rs.getLong("estimated_total_quantity"));
//				list.add(grnMasterPID);
//			}
		}
//		grnMaster.setEstimatedTotalQuantity(list);
		return recordFound ? grnMaster : null;
	}

	public static GRNMaster resultSetToMasterSummary(List<Tuple> tuples) throws SQLException, JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		GRNMaster grnMaster = new GRNMaster();
//		List<PIDEstimatedQty> list = new ArrayList<>();
		boolean recordFound = false;
		for (Tuple tuple:tuples) {
			if (Objects.nonNull(tuple)) {
				recordFound = true;
				grnMaster.setGrnCode(tuple.get("grn_code", String.class));
				grnMaster.setUnicomGrnCode(tuple.get("unicom_grn_code", String.class));
				grnMaster.setGrnStatus(tuple.get("grn_status", String.class));
				grnMaster.setPoId(tuple.get("po_id", String.class));
				grnMaster.setCreatedAt(tuple.get("created_at", Timestamp.class));
				grnMaster.setCreatedBy(tuple.get("created_by", String.class));
				grnMaster.setInvoiceId(tuple.get("invoice_id", String.class));
				grnMaster.setInvoice(mapper.readValue(tuple.get("invoice", String.class), Invoice.class));
//				if (tuple.get("pid", String.class) != null) {
//					PIDEstimatedQty grnMasterPID = new PIDEstimatedQty(tuple.get("pid", String.class),
//							Long.valueOf(tuple.get("estimated_total_quantity").toString()));
//					list.add(grnMasterPID);
//				}
			}
		}
//		grnMaster.setEstimatedTotalQuantity(list);
		return recordFound ? grnMaster : null;
	}
	
	public static PidSearchResponse resultSetToPidSearchResponse(ResultSet rs) throws SQLException, JsonMappingException, JsonProcessingException {
		
		PidSearchResponse pidSearchResponse = new PidSearchResponse();
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		
		pidSearchResponse.setGrnCode(rs.getString("grn_code"));
		pidSearchResponse.setGrnStatus(rs.getString("grn_status"));
		pidSearchResponse.setCreatedOn(rs.getString("created_at"));
		pidSearchResponse.setVendorName(rs.getString("vendor"));
		pidSearchResponse.setPoId(rs.getString("po_id"));
		pidSearchResponse.setPid(rs.getString("pid"));
		pidSearchResponse.setCreatedBy(rs.getString("created_by"));
//		pidSearchResponse.setEstimatedQty(rs.getInt("estimated_total_quantity"));
//		pidSearchResponse.setSamplingPercent(rs.getInt("sampling"));
		pidSearchResponse.setFailurePercent(rs.getInt("failure_percentage"));
		pidSearchResponse.setQcPass(rs.getInt("pass_count"));
		pidSearchResponse.setQcFail(rs.getInt("total_count") - rs.getInt("pass_count"));
		
		Date updatedAt = rs.getDate("updated_at");
		Date currDate = new Date(System.currentTimeMillis());
		long failedSince = ((currDate.getTime() - updatedAt.getTime()) / (1000 * 60 * 60 * 24)) % 365; 
		
		pidSearchResponse.setFailedSince(failedSince < 1 ? "0 Days" : Long.toString(failedSince) + " Days");
		Invoice invoice = mapper.readValue((String)rs.getObject("invoice"), Invoice.class);
		Product product = CommonUtils.getProductDetailsFromInvoice(invoice.getPids(), rs.getString("pid"));
		
		pidSearchResponse.setInvoiceQty(product != null ? product.getQuantity() : 0L);
		pidSearchResponse.setCategoryId(product != null ? product.getCategoryId() : null);
		return pidSearchResponse;
	}

	public static PidSearchResponse resultSetToPidSearchResponse(Tuple tuple) throws JsonProcessingException {

		PidSearchResponse pidSearchResponse = new PidSearchResponse();
		ObjectMapper mapper = CommonUtils.objectMapper;
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

		pidSearchResponse.setGrnCode(tuple.get("grn_code").toString());
		pidSearchResponse.setGrnStatus(tuple.get("grn_status").toString());
		pidSearchResponse.setCreatedOn(tuple.get("created_at").toString());
		pidSearchResponse.setVendorName(tuple.get("vendor").toString());
		pidSearchResponse.setPoId(tuple.get("po_id").toString());
		pidSearchResponse.setPid(tuple.get("pid").toString());
		pidSearchResponse.setCreatedBy(tuple.get("created_by").toString());
//		pidSearchResponse.setEstimatedQty((int)tuple.get("estimated_total_quantity"));
//		pidSearchResponse.setSamplingPercent((int)tuple.get("sampling"));
		pidSearchResponse.setFailurePercent((int)tuple.get("failure_percentage"));
		pidSearchResponse.setQcPass((int)tuple.get("pass_count"));
		pidSearchResponse.setQcFail((int)tuple.get("total_count") - (int)tuple.get("pass_count"));

		Date updatedAt = tuple.get("updated_at",Date.class);
		Date currDate = new Date(System.currentTimeMillis());
		long failedSince = ((currDate.getTime() - updatedAt.getTime()) / (1000 * 60 * 60 * 24)) % 365;

		pidSearchResponse.setFailedSince(failedSince < 1 ? "0 Days" : Long.toString(failedSince) + " Days");
		Invoice invoice = mapper.readValue((String)tuple.get("invoice"), Invoice.class);
		Product product = CommonUtils.getProductDetailsFromInvoice(invoice.getPids(), tuple.get("pid").toString());

		pidSearchResponse.setInvoiceQty(product != null ? product.getQuantity() : 0L);
		pidSearchResponse.setCategoryId(product != null ? product.getCategoryId() : null);
		return pidSearchResponse;
	}

    public static GRNProduct resultSetToGRNProduct(ResultSet rs) throws SQLException {
		GRNProduct grnProduct = new GRNProduct();
		grnProduct.setCgst(rs.getDouble("cgst_rate"));
		grnProduct.setSgst(rs.getDouble("sgst_rate"));
		grnProduct.setIgst(rs.getDouble("igst_rate"));
		grnProduct.setLot(rs.getString("lot_no"));
		grnProduct.setReceived(rs.getInt("received"));
		grnProduct.setPid(rs.getString("pid"));
		grnProduct.setPrice(rs.getDouble("price"));
		grnProduct.setPidStatus(rs.getString("grn_pid_status"));
		return grnProduct;
    }

	public static GRNProduct resultSetToGRNProduct(Tuple tuple) throws SQLException {
		GRNProduct grnProduct = new GRNProduct();
		grnProduct.setCgst(Double.valueOf(getOrDefault(tuple.get("cgst_rate"), 0).toString()));
		grnProduct.setSgst(Double.valueOf(getOrDefault(tuple.get("sgst_rate"), 0).toString()));
		grnProduct.setIgst(Double.valueOf(getOrDefault(tuple.get("igst_rate"), 0).toString()));
		grnProduct.setLot(getOrDefault(tuple.get("lot_no"), "").toString());
		grnProduct.setReceived(Long.valueOf(getOrDefault(tuple.get("received"), 0).toString()));
		grnProduct.setPid(getOrDefault(tuple.get("pid"),"").toString());
		grnProduct.setPrice(Double.valueOf(getOrDefault(tuple.get("price"), 0).toString()));
		grnProduct.setPidStatus(getOrDefault(tuple.get("grn_pid_status"), "").toString());
		return grnProduct;
	}

	public static Object getOrDefault(Object value, Object defaultValue) {
		if(Objects.isNull(value))
			return defaultValue;
		return value;
	}
}
