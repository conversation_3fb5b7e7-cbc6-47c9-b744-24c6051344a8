package com.lenskart.nexs.grn.db;

public interface Queries {
    
    public String CREATE_GRN_MASTER = "insert into grn_master (grn_code, unicom_grn_code, po_id, invoice_id,  " +
            " invoice, po, grn_status, grn_synced_to_unicom, grn_synced_to_NAV, created_by, updated_by, facility, grn_sync_status) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    public String GET_GRN_MASTER = "SELECT g.grn_code, g.unicom_grn_code, g.created_by, g.po_id, " +
            "g.invoice_id, g.grn_status, gp.pid, gp.estimated_total_quantity, g.facility, g.grn_type FROM grn_master g LEFT JOIN " +
            "grn_pid_master gp ON g.grn_code = gp.grn_code where g.grn_code = ?";
    public String GET_GRN_SUMMARY = "SELECT g.grn_code, g.po_id, " +
            "g.invoice_id, g.grn_status, gp.pid, gp.estimated_total_quantity, g.created_at, g.created_by, g.unicom_grn_code FROM grn_master g LEFT JOIN " +
            "grn_pid_master gp ON g.grn_code = gp.grn_code where g.grn_code = ?";
    public String GET_ONLY_GRN_MASTER = "select g.* from grn_master g, (select * from user_activity where " +
            "grn_code = ? order by performed_at DESC limit 1) u WHERE u.grn_code = g.grn_code and g.grn_code = ?";
    public String UPDATE_GRN_MASTER = "update grn_master SET grn_status = ?, updated_by = ?, updated_at = ?, " +
            "facility = ? where grn_code = ?";
    public String EDIT_GRN_MASTER = "update grn_master SET invoice = ?, po = ?, po_id = ?, updated_by = ?, " +
            "updated_at = ? where invoice_id = ?";
    public String CLOSE_GRN_MASTER = "update grn_master SET grn_status = ?, updated_by = ?, updated_at = ? " +
            "where grn_code = ?";
    public String SELECT_GRN_MASTER_FROM_UNICOM_GRN_CODE = "select * from grn_master where unicom_grn_code = ?";
    public String UPDATE_GRN_MASTER_FOR_ESTIMATED_QTY = "update grn_master set estimated_total_quantity = ? and " +
            "estimated_quantity_list = ? where grn_code = ?";
    public String UPDATE_GRN_ITEM_QC_STATUS = "update grn_items set qc_status = ? where barcode = ?";
    public String UPDATE_GRN_ITEM_BOX_BARCODE = "update grn_items set qc_status = ?, qc_pass_box_barcode = ?, " +
            "qc_fail_code = ?, qc_reason = ?, expiry_date = ? where barcode = ?";
    public String USER_ACTIVITY_ASSIGN_GRN = "insert into user_activity (user_id, action, facility, grn_code, " +
            "assigned_to) values (?,?,?,?,?)";
    public String INSERT_INTO_USER_ACTIVITY = "INSERT INTO user_activity (`user_id`, `action`, `facility`, `grn_code`" +
            ", `assigned_to`, `performed_at`) VALUES (?, ?, ?, ?, ?, ?)";
    public String GET_GRN = "select * from grn_master where grn_code = ?";
    
    public String SET_MANUAL_OVERRIDE = "update grn_pid_master set manual_override = ? where grn_code = ? and pid = ?";
    public String UPDATE_GRN_PID_ESTIMATED_QTY = "update grn_pid_master set estimated_total_quantity = ?, " +
            "estimated_quantity_list = ?, grn_pid_status = 'PENDING' where grn_code = ? and pid = ?";
  
    public String CREATE_GRN_PID_MASTER = "insert into grn_pid_master (grn_code, pid, invoice_id, invoice_ref_num, " +
            "vendor, brand, category_id, pid_description, price, tax_rate, cgst_rate, sgst_rate, igst_rate, estimated_total_quantity, estimated_quantity_list, grn_pid_status, created_by, updated_by, created_at" +
            ", updated_at, manual_override) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    public String GET_MANUAL_OVERRIDE = "SELECT COUNT(1) FROM grn_pid_master where invoice_ref_num = ? and pid = ? " +
            "and manual_override = 1";
    public String GET_GRN_PID_MASTER = "select * from grn_pid_master where grn_code = ? and pid = ?";
    public String GET_GRN_PID_MASTER_BY_GRN = "select * from grn_pid_master where grn_code = ?";
    public String GET_GRN_PID_MASTER_BY_GRN_LIST = "select * from grn_pid_master where grn_code in (";
    public String UPDATE_GRN_PID_MASTER_STATUS = "update grn_pid_master set grn_pid_status = ? where grn_code = ? and pid = ?";
    
    public String INSERT_INTO_GRN = "INSERT INTO grn_items (`barcode`, `grn_code`, `status`, `pid`, `po_id`," +
            " `invoice_ref_num`, `vendor_code`, `expiry_date`, `lot_no`, `estimated_qty`, `qc_pass_box_barcode`, `qc_status`," +
            " `qc_fail_code`,`qc_reason`,`qc_master_sampling`, `qc_gradient_sampling`, `failure_threshold_qc_master`, " +
            "`failure_threshold_qc_gradient`, `created_by`, `updated_by`, `facility`, `channel_status`) values(?, ?, ?, " +
            "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    public String GET_GRN_ITEM = "SELECT `qc_status` FROM grn_items where barcode = ?";
    public String UPDATE_GRN_ITEM = "UPDATE grn_items SET qc_status = ?, qc_fail_code = ?, qc_reason = ?, expiry_date = ? where barcode = ?";

    public String GRN_ITEM_DETAIL = "SELECT * FROM grn_items where barcode = ?";

    public String DELETE_GRN_ITEM = "DELETE FROM grn_items where barcode = ?";

    public String GET_TOTAL_SCANNED = "SELECT pid, COUNT(1) FROM grn_items where grn_code = ? and pid IN (";
    public String GET_TOTAL_FAILED = "SELECT pid, COUNT(1) FROM grn_items where grn_code = ? and qc_status = 'fail' " +
            "and pid IN (";
    public String GET_NEXS_BARCODE = "SELECT * FROM barcode_series where nexs_barcode = 1 and status = 1 and is_box = 0";
    public String GET_NON_NEXS_BARCODE = "SELECT * FROM barcode_series where nexs_barcode = 0 and status = 1";
    public String GET_BARCODE_COUNT = "SELECT COUNT(1) FROM grn_items where barcode = ?";
    public String GET_GRN_PIDS = "SELECT grn_code, pid, count(1) FROM grn_items where grn_code = ? and qc_status = ?" +
            " and pid in (";
    public String GET_PASS_BOX_WITH_ITEM = "SELECT b.barcode as box_barcode, g.barcode, g.channel_status, g.expiry_date FROM " +
            "box_mapping b INNER JOIN box bo ON b.barcode = bo.barcode LEFT JOIN (SELECT * FROM grn_items where " +
            "grn_code = ? and pid = ?) g ON b.barcode = g.qc_pass_box_barcode WHERE b.grn_code = ? and b.pid = ? and " +
            "bo.enabled = true and bo.status = 'InUse' order by b.created_at DESC, g.created_at DESC";

    public String GET_FAIL_BOX_WITH_ITEM = "SELECT barcode, channel_status, qc_fail_code, qc_reason FROM grn_items where grn_code = ? and pid =" +
            " ? and qc_status <> 'pass' order by created_at DESC";

    public String GET_GRN_BY_INVOICE_AND_USER = "select gm.grn_code, gm.created_at, gm.grn_status, gm.invoice_id, gm.po_id, gm.created_by " +
    		"from grn_master gm, (select u2.grn_code, u2.assigned_to " +
            "from (SELECT max(performed_at) max_time, grn_code, assigned_to FROM user_activity group by grn_code) u1 " +
            "join user_activity u2 on u1.grn_code = u2.grn_code and u1.max_time = u2.performed_at) ua where " +
            "gm.grn_code = ua.grn_code and gm.invoice_id = ? and ua.assigned_to = ? group by grn_code";
    
    public String GET_INVOICE_BY_ID = "select * from grn_master where invoice_id = ? limit 1";

    public String GET_GRN_BY_INVOICE = "select grn_code, created_at, grn_status, invoice_id, po_id, created_by from grn_master where invoice_id = ?";
    
    public String GET_GRN_PID_DETAILS = "select * from grn_pid_master where grn_code in (";
    public String GET_GRN_PID_SCANNED_COUNT = "select grn_code, pid, count(*) as total_count,  SUM(CASE WHEN qc_status='fail' THEN 1 ELSE 0 END) as fail_count,  Max(qc_master_sampling) as sample from grn_items where grn_code = ? and pid in (";
    public String GET_GRN_ITEMS_PID_SCANNED_COUNT = "select grn_code, pid, count(*) as total_count,  SUM(CASE WHEN qc_status='fail' THEN 1 ELSE 0 END) as fail_count,  Max(qc_master_sampling) as sample from grn_items where grn_code in (";
    public String GET_MISC = "SELECT grn_code, pid, expiry_date, lot_no, Max(qc_master_sampling) as sample FROM " +
            "grn_items where grn_code = ? and pid in (";
    public String GET_FAILED_ON = "SELECT grn_code, pid, Max(created_at) as failed_on FROM grn_items where grn_code = ? " +
            "and status = 'FAILED' and pid in (";
    public String GET_BOX_COUNT = "SELECT COUNT(DISTINCT(qc_pass_box_barcode)) as box_count FROM grn_items WHERE grn_code = ? " +
            "and qc_pass_box_barcode is not null";
    
    public String GET_GRN_LIST_BY_INVOICE = "select gm.grn_code, gm.grn_status, gm.created_by, gm.created_at as " +
            "created_on, gm.invoice_id as invoice_reference_num from grn_master gm, (select u2.grn_code, " +
            "u2.assigned_to from (SELECT max(performed_at) max_time, grn_code, assigned_to FROM user_activity group " +
            "by grn_code) u1 join user_activity u2 on u1.grn_code = u2.grn_code and u1.max_time = u2.performed_at) ua "+
            "where gm.grn_code = ua.grn_code and gm.invoice_id = ? and gm.grn_status != " +
            "'closed' group by grn_code;";
    
    public String GET_TOTAL_SCAN_COUNT_BY_INVOICE = "select  count(*) as total_scanned, gi.pid, gi.grn_code from grn_items gi , grn_master gm where gi.grn_code = gm.grn_code and gm.invoice_id = ?"; 
    
    public String GET_PID_TOTAL_SCAN_COUNT_BY_INVOICE = "select count(*) as total_scanned , pid from grn_items where invoice_ref_num = ?"; 
    	
    public String INSERT_GRN_QC_LOG = "insert into grn_qc_log(grn_code, pid, barcode, action, reason, created_by) values (?, ?, ?, ?, ?, ?)";
    
    public String GET_GRN_QC_LOG_BY_GRN_PID = "select barcode, reason from grn_qc_log where grn_code = ? and pid = ? order by created_at DESC";
    
    public String GET_NOT_CLOSED_GRN_COUNT_BY_INVOICE = "select count(*) as grn_count from grn_master where invoice_id = ? and grn_status != 'closed'";

    public String GET_BARCODE_SERIES = "SELECT * FROM barcode_series where status = 1";

    public String GET_QC_PASS_ITEMS = "SELECT barcode, channel_status FROM grn_items where grn_code = ? and pid =" +
            " ? and qc_status = 'pass' order by created_at DESC";

    public String GET_EMPTY_BOXES = "SELECT b.barcode, b.pid, count(g.barcode) FROM box_mapping b INNER JOIN box bo " +
            "ON b.barcode = bo.barcode LEFT JOIN grn_items g ON b.barcode = g.qc_pass_box_barcode WHERE b.grn_code = ?" +
            " and bo.`enabled` = true and bo.status = 'InUse' group by b.barcode, b.pid having count(g.barcode) = 0";

    public String SET_BOX_AVAILABLE = "update box set `status` = 'Available', `updated_by` = ?, `updated_at` = ? " +
            " where `enabled` = true and `barcode` in (";

    public String GET_FAILED_STATUS_GRN_PIDS = "select  grn_pid_status, manual_override from grn_pid_master where " +
            "invoice_ref_num = ? and pid = ? and grn_pid_status = ?";
    
	public String SEARCH_GRN = "select gm.grn_code, gm.grn_status, gm.po_id, gm.invoice_id, gm.invoice, gm.created_by, ua.assigned_to, gm.created_at from "
			+ "grn_master gm inner join user_activity ua on ua.grn_code = gm.grn_code left join grn_pid_master gp on gp.grn_code = gm.grn_code "
			+ "where ua.performed_at = (select max(performed_at) from user_activity where grn_code =  gm.grn_code)";
    
    public String GRN_SCAN_COUNT = "select grn_code,  count(*) as total_count,  SUM(CASE WHEN qc_status='pass' THEN 1 ELSE 0 END) as pass_count from grn_items where grn_code in (";
    
	public String GRN_PID_SEARCH = "select  gp.grn_code, gp.pid, gm.grn_status, gp.vendor, gm.po_id, gm.created_at, " +
            "gp.created_by, gp.updated_at, gp.estimated_total_quantity, max(gi.qc_master_sampling) as sampling, " +
            "max(gi.failure_threshold_qc_master) as failure_percentage, count(gi.qc_status) as total_count, " +
            "SUM(CASE WHEN gi.qc_status ='pass' THEN 1 ELSE 0 END) as pass_count, gm.invoice " +
            "from grn_pid_master gp inner join grn_master gm on gp.grn_code = gm.grn_code left join grn_items gi on" +
            " gi.grn_code = gp.grn_code where gp.grn_pid_status = 'FAILED' and gp.manual_override = 0";

    public String SELECT_BOX_BARCODE = "select count(*) as total_count from box_mapping b JOIN box bo ON b.barcode = bo" +
            ".barcode where grn_code = ? and pid = ? and b.barcode = ? and bo.enabled = true and bo.status = 'InUse'";

    public String INSERT_INTO_BOX_HISTORY = "insert into box_history (`barcode`, `facility`, `location`, `status`, " +
            "`enabled`, `created_by`, `updated_by`) values(?, ?, ?, ?, ?, ?, ?)";

    public String GET_GRN_PID_MASTER_DETAILS = "select * from grn_pid_master where grn_code= ?";
    public String GET_GRN_PID_BARCODE_DETAILS = "select gi.grn_code, gi.barcode , gi.pid , gi.qc_status, IF(gi" +
            ".qc_reason is null, 'NA',gi.qc_reason) as qc_reason, gi.created_by as scanned_by, gi.created_at as " +
            "scanned_at from grn_items gi where gi.grn_code = ?";
    
    public String UPDATE_MANUAL_OVERRIDE_FLAG = "update grn_pid_master set manual_override = ? where (grn_code,pid) in";

    public String GET_GRN_PRODUCT = "select gm.grn_code, gm.created_at, gm.grn_status, gm.po_id, gp.pid, " +
            "gp.grn_pid_status, gi.lot_no, count(gi.grn_code) as received, gp.price, gp.sgst_rate, gp.igst_rate, " +
            "gp.cgst_rate, u.assigned_to, gm.invoice_id from grn_master gm left join grn_pid_master gp on gm.grn_code " +
            "= gp.grn_code left join grn_items gi on gi.grn_code = gp.grn_code and gi.pid = gp.pid left join (SELECT " +
            "* FROM user_activity where grn_code = ? order by performed_at DESC LIMIT 1) u on gm.grn_code = " +
            "u.grn_code where gm.grn_code = ?";;
    
    public String COUNT_GRN_LISTING = "select count(distinct (gm.grn_code)) as grn_count from grn_master gm left join grn_pid_master gp on gp.grn_code = gm.grn_code";
    
    public String COUNT_PIDS = "select count(1) as pid_count from grn_pid_master gp, grn_master gm where gm.grn_code = gp.grn_code";
    
    public String CHECK_CLOSED_GRN_BY_INV_PID = "select gp.pid, count(1) as grn_count from grn_master gm inner join grn_pid_master gp on gp.grn_code = gm.grn_code where gm.grn_status != 'closed' and gp.invoice_ref_num = ? and pid in (";

    public String GET_GRN_LIST = "select grn_code from grn_master where invoice_id = ?";

    public String UPDATE_GRN_PID_MASTER = "update grn_pid_master set invoice_id = ?, vendor = ?, updated_by = ?, " +
            "updated_at = ? where invoice_ref_num = ?";

    public String UPDATE_GRN_ITEMS = "update grn_items set po_id = ?, vendor_code = ?, updated_by = ?, updated_at = ?" +
            " where invoice_ref_num = ? ";

    public String GET_TEMPLATE = "select template from templates where template_id = ? and version = ?";

    public String GET_CLOSED_GRNS = "select * from grn_master where invoice_id = ? and grn_status = 'closed' and " +
            "facility = ?";

    public String GET_OPENED_GRNS = "select grn_code from grn_master where invoice_id = ? and grn_status != 'closed' and " +
            "facility = ?";

    public String GET_GRN_SCAN_COUNT_BY_PID = "select grn_code, pid, count(*) as total_count, " +
            "SUM(CASE WHEN qc_status='fail' THEN 1 ELSE 0 END) as fail_count from grn_items where grn_code = ? group " +
            "by grn_code, pid";

    public String GET_GRN_BY_PO = "select * from grn_master where po_id = ?";

    public String GET_USER_DETAILS_BY_EMP_CODE = "select * from user_details where emp_code = ?";

    public String UPDATE_USER_DETAILS = "update user_details set name = ?, phone_code = ?, phone_number = ? " +
            ", email = ? where emp_code = ?";

    public String INSERT_USER_DETAILS = "INSERT INTO user_details (`emp_code`, `name`, `phone_code`, " +
            "`phone_number`, `email`) VALUES (?, ?, ?, ?, ?)";
    
    public String GET_COUNT_FOR_INVOICE = "select count(*) as items_count from grn_items where pid = ? and invoice_ref_num = ?";

    public String GET_COUNT_FOR_PO = "select count(*) as items_count from grn_items where pid = ? and po_id = ?";

    public String GET_GRN_STATUS = "select status from grn_items where grn_code = ? and pid = ?";
    
    public String GET_INVOICE_PID_COUNT = "SELECT invoice_ref_num as item_id, pid, count(barcode) as count FROM nexs.grn_items where invoice_ref_num in (";

    public String GET_PO_PID_COUNT = "SELECT po_id as item_id, pid, count(barcode) as count FROM nexs.grn_items where po_id in (";

    public String GET_INVOICE_NOT_CLOSED = "SELECT invoice_ref_number as item_id FROM purchase_invoice where status = 'CREATED'";

    public String GET_PO_NOT_CLOSED = "SELECT po_num as item_id FROM purchase_order where status = 'APPROVED'";

    public String GET_COUNT_PID_FOR_INVOICE = "select count(*) as items_count,pid from grn_items where invoice_ref_num = ? group by pid";

    public String GET_COUNT_PID_FOR_PO = "select count(*) as items_count,pid from grn_items where po_id = ? group by pid";

    public String GET_PID_PO_INVOICE_FOR_GRN = "select distinct pid, invoice_ref_num, po_id from grn_items where grn_code = ?";

    public String GET_INVOICE_ITEM_COUNT_FOR_GRN = "select gi.pid, count(barcode) scanned_quantity, ANY_VALUE(pi.quantity) as invoice_quantity " +
            "from grn_items gi join purchase_invoice_item pi on gi.invoice_ref_num = pi.invoice_ref_number and gi.pid = pi.product_id " +
            "where gi.invoice_ref_num = ? and gi.pid in (?) group by gi.pid";

    public String GET_PO_ITEM_COUNT_FOR_GRN = "select gi.pid, count(barcode) scanned_quantity, ANY_VALUE(po.quantity) as po_quantity " +
            "from grn_items gi join purchase_order_item po on gi.po_id = po.po_num and gi.pid = po.product_id where gi.po_id = ? and gi.pid in (?) group by gi.pid";

    public String GET_UNSYNCED_GRN = "select grn_code, grn_type, facility from grn_master where grn_status = 'closed' and grn_sync_status < 7 and version < 5 "
    		+ "and created_at between DATE_SUB(now(), INTERVAL 7 DAY) and DATE_SUB(now(), INTERVAL 30 MINUTE) order by updated_at limit 50";

    public String GET_OLD_UNSYNCED_GRN = "select grn_code, grn_type, facility from grn_master where grn_status = 'closed' and grn_sync_status < 7 and version < 10 "
            + "and created_at between DATE_SUB(now(), INTERVAL 7 DAY) and DATE_SUB(now(), INTERVAL 1 DAY) order by updated_at limit 50";

    public String ACCOUNT_BARCODE_PURCHASE_PRICING = "SELECT * FROM account_barcode_purchase_pricing WHERE barcode IN (%s) AND legal_owner = ? order by id";

    public String GET_PASS_LOT_WITH_ITEM = "SELECT lot_no, barcode, channel_status, expiry_date,created_at FROM grn_items WHERE grn_code = ?"
            + " and pid = ? and enabled = true order by created_at DESC";
}
