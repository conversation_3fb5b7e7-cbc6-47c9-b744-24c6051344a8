package com.lenskart.nexs.grn.db;

import java.util.Properties;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.zaxxer.hikari.HikariDataSource;

import lombok.Data;

@Configuration
@Data
public class DBConfig {

    private HikariDataSource dataSource;

    private HikariDataSource dataSourceInventory;

    @Value("${spring.datasource.url}")
    private String jdbcUrl;

    @Value("${spring.datasource.username}")
    private String user;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    @Value("${jdbcUrl.in}")
    private String jdbcUrlInventory;

    @Value("${dataSource.in.user}")
    private String userInventory;

    @Value("${dataSource.in.password}")
    private String passwordInventory;

    @Value("${dataSource.in.cachePrepStmts}")
    private String cachePreparedStatementsInventory;

    @Value("${dataSource.in.prepStmtCacheSize}")
    private String preparedStatementCacheSizeInventory;

    @Value("${dataSource.in.prepStmtCacheSqlLimit}")
    private String preparedStatementCacheSqlLimitInventory;

    @Value("${driverClassName.in}")
    private String driverClassNameInventory;

    @PostConstruct
    public void setDataSource() {
        Properties properties = new Properties();
        properties.setProperty("jdbcUrl", jdbcUrlInventory);
        properties.setProperty("dataSource.user", userInventory);
        properties.setProperty("dataSource.password", passwordInventory);
        properties.setProperty("dataSource.cachePrepStmts", cachePreparedStatementsInventory);
        properties.setProperty("dataSource.prepStmtCacheSize", preparedStatementCacheSizeInventory);
        properties.setProperty("dataSource.prepStmtCacheSqlLimit", preparedStatementCacheSqlLimitInventory);
        properties.setProperty("driverClassName", driverClassNameInventory);
        properties.setProperty("dataSource.driverClassName", driverClassNameInventory);
        dataSourceInventory = LKDatasource.createDataSource(properties);
    }

}
