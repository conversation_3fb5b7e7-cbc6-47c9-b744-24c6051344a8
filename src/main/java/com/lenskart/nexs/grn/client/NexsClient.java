package com.lenskart.nexs.grn.client;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.platform.fl.utils.config.DisableTypeInfoMixIn;
import com.lenskart.platform.fl.utils.dto.AbstractEventData;
import com.lenskart.platform.fl.utils.dto.grn.Grn;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.time.Duration;
import java.util.Collections;

@Service
@Slf4j
public class NexsClient {

    @Autowired
    ObjectMapper objectMapper;
    @Value("${nexs.grn.search.api.base.url:http://grn-search-consumer.nexs.preprod-eks.internal}")
    private String nexsGrnSearchApiBaseUrl;
    private static final String grnPayloadApi = "/nexs/d365/sync/grn";

    private static final String RETURN_PAYLOAD = "returnPayload";
    private static final String ADMIN_USER = "adminUser";
    private static final RestTemplate restTemplate = RestUtils.getRestTemplate(Duration.ofMillis(2000), Duration.ofMillis(2000));


    @SneakyThrows
    public Grn getGrnPayload(String grnCode) {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.addMixIn(AbstractEventData.class, DisableTypeInfoMixIn.class);
        URI uri = UriComponentsBuilder.fromUriString(nexsGrnSearchApiBaseUrl + grnPayloadApi)
                .queryParam(RETURN_PAYLOAD, true)
                .queryParam(ADMIN_USER, true)
                .build().toUri();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(Collections.singletonList(grnCode), httpHeaders);
        ResponseEntity<String> responseEntity = nexsSearchForwardRequest(uri, HttpMethod.POST, entity);
        if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
            log.info("[getGrnPayload] Successful ");
            return objectMapper.readValue(responseEntity.getBody(), Grn.class);
        } else {
            log.error("[getGrnPayload] Unsuccessful");
            return null;
        }
    }

    public ResponseEntity<String> nexsSearchForwardRequest(URI uri, HttpMethod httpMethod, HttpEntity<Object> entity) {
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(uri, httpMethod, entity, String.class);
            log.info("[NexsClient][nexsForwardRequest] responseEntity: {} " + responseEntity);
            return responseEntity;
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("[NexsClient][nexsForwardRequest] Request result : " + e.getResponseBodyAsString());
            throw new RuntimeException(e);
        } catch (RuntimeException e) {
            log.error("[NexsClient][nexsForwardRequest] Request result : " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

}
