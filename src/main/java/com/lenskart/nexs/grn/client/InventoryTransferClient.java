package com.lenskart.nexs.grn.client;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.lenskart.nexs.grn.constants.GRNConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.grn.dto.request.BarcodeAndFacilityCodeRequest;
import com.lenskart.nexs.grn.dto.response.BarcodeAndCostPriceDto;
import org.springframework.http.HttpStatus;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class InventoryTransferClient {
	@Value("${nexs.inventory.transfer.url}")
	private String nexsTransferUrl;

	@Autowired
	@Qualifier("itRestTemplate")
	private RestTemplate restTemplate;

	@Autowired
	private ObjectMapper objectMapper;

	public RestTemplate createRestTemplateWithTimeout(int timeout) {
		RestTemplate restTemplate = new RestTemplate();
		SimpleClientHttpRequestFactory rf = new SimpleClientHttpRequestFactory();
		rf.setReadTimeout(timeout);
		rf.setConnectTimeout(timeout);
		restTemplate.setRequestFactory(rf);
		return restTemplate;
	}

	public List<BarcodeAndCostPriceDto> getCostPrice(List<String> barcodes, String facilityCode) throws Exception {
		List<BarcodeAndCostPriceDto> barcodeAndCostPriceDtos = new ArrayList<>();
		try {
			String url = nexsTransferUrl + GRNConstants.NEXS_END_POINT_FOR_BARCODE_COSTPRICE;
			BarcodeAndFacilityCodeRequest barcodeAndFacilityCodeRequest = BarcodeAndFacilityCodeRequest.builder()
					.facilityCode(facilityCode).barcodes(barcodes).build();
			HttpHeaders header = new HttpHeaders();
			header.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity entity = new HttpEntity<Object>(barcodeAndFacilityCodeRequest, header);
			log.info("getCostPriceForBarcode url for barcodes {} : {}", barcodes, url);
			restTemplate = createRestTemplateWithTimeout(10000);
			ResponseEntity<BaseResponseModel> response = restTemplate.exchange(url, HttpMethod.POST, entity,
					BaseResponseModel.class);
			log.info("[getCostPriceForBarcode] response for barcode {} : {}", barcodes, response);
			if(Objects.nonNull(response.getBody()) && HttpStatus.OK.equals(response.getStatusCode())){
				barcodeAndCostPriceDtos = objectMapper.convertValue(response.getBody().getData(),
																	new TypeReference<List<BarcodeAndCostPriceDto>>() {
																	});
			}
		} catch (Exception ex) {
			log.error("getCostPrice error for barcodes : {}", barcodes, ex);
		}
		return barcodeAndCostPriceDtos;
	}

}
