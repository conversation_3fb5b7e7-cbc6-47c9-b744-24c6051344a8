package com.lenskart.nexs.grn.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ActionStatus {
    IMS_INSERT_GRN_ITEMS("insert"), IMS_DELETE_GRN_ITEMS("delete"), IMS_UPDATE_GRN_ITEMS("update"),

    IMS_BAD_IQC_GRN_HOLD("badIqcHold"),IMS_GOOD_IQC_GRN_HOLD("goodIqcHold"), IMS_IQC_GRN_COMPLETE("complete");
    @JsonValue
    private String actionStatus;

    ActionStatus(String actionStatus) {
        this.actionStatus = actionStatus;
    }

    public String getName() {
        return actionStatus;
    }
}
