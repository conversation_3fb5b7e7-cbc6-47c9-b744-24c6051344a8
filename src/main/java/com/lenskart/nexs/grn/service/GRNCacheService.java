package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceEntityService;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dao.GRNItemDAO;
import com.lenskart.nexs.grn.dao.GRNPIDDAO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.InvalidRequestException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.model.InvoiceQtyAndPriceResponse;
import com.lenskart.nexs.grn.util.CacheUtils;
import com.lenskart.nexs.grn.util.GRNCacheServiceUtils;
import com.lenskart.nexs.service.RedisHandler;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class GRNCacheService implements  GRNConstants {

    @Autowired
    private CacheDAO cacheDAO;

//    @Autowired
//    @Qualifier(QualifierConstants.JPA_QC_STATUS_DAO)
//    private QcStatusDAO qcStatusDAO;

    @Autowired
    PurchaseInvoiceEntityService purchaseInvoiceEntityService;

    @Autowired
    private GRNCacheServiceUtils grnCacheServiceUtils;

    @Autowired
    @Qualifier(QualifierConstants.JPA_GRN_PID_DAO)
    private GRNPIDDAO grnPIDMasterDAO;

    @Autowired
    @Qualifier(QualifierConstants.JPA_GRN_ITEM_DAO)
    private GRNItemDAO grnItemDAO;

    @Autowired
    private GRNConfig grnConfig;

    @Logging
    public boolean updateCache(GRNItem grnItem) {
        log.info("[updateCache] grnCode {}, barcode {}, qcStatus {}",
                grnItem.getGrnCode(), grnItem.getBarcode(), grnItem.getQcStatus());
        grnCacheServiceUtils.incrementAllScanned(grnItem.getGrnCode(), grnItem.getPid());
        long totalScanned = grnCacheServiceUtils.incrementScanCount(grnItem.getGrnCode(), grnItem.getPid());
        long totalFailed = 0;
        if(QC_FAIL.equals(grnItem.getQcStatus().toLowerCase())) {
            totalFailed = grnCacheServiceUtils.incrementFailCount(grnItem.getGrnCode(), grnItem.getPid());
            grnCacheServiceUtils.incrementAllFailed(grnItem.getGrnCode(), grnItem.getPid());
        } else
            totalFailed = grnCacheServiceUtils.getTotalFailed(grnItem.getGrnCode(), grnItem.getPid());
        log.info(grnItem.getGrnCode() + " : Total scanned : " + totalScanned + ", Total failed : " + totalFailed);
        if(grnCacheServiceUtils.isGRNManualOverriden(grnItem.getGrnCode(), grnItem.getPid())){
            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : manual override is set");
            setItemParams(grnItem, PASSED, 1);
            return true;
        }

        if(grnCacheServiceUtils.isQcStatusFailed(grnItem.getGrnCode(), grnItem.getPid())) {
            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : Qc status : fail");
            return false;
        }

        if(grnCacheServiceUtils.isChannelGreen(grnItem.getGrnCode(), grnItem.getPid())) {
            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : in : green channel");
            setItemParams(grnItem, PASSED, 1);
            return true;
        }

        if(grnItem.getQcStatus().equals(QC_PASS)) {
            grnCacheServiceUtils.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), PASSED);
            grnPIDMasterDAO.updateGRNPIDMasterStatus(PASSED, grnItem.getGrnCode(), grnItem.getPid());
            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : moved to : green channel");
            setItemParams(grnItem, PASSED, 0);
            return true;
        }
        setItemParams(grnItem, PENDING, 0);

        return true;

    }

    @Logging
    public void itemQuantityCheck(GRNItem grnItem, String hsnClassificationType, String invoiceLevel) throws InvalidRequestException {
        log.info("[itemQuantityCheck] Grn code: {}, barcode: {}, type {}, Po: {}, pId: {}, invoiceLevel {}",
                grnItem.getGrnCode(), grnItem.getBarcode(), hsnClassificationType,
                grnItem.getPoId(), grnItem.getPid(), invoiceLevel);

        long invoiceQty = 0, poQty = 0;
        if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(invoiceLevel)){
            log.info("[itemQuantityCheck] Item type is CL, grnCode {}", grnItem.getGrnCode());
            InvoiceQtyAndPriceResponse invoiceQtyAndPriceResponse =
                    grnCacheServiceUtils.getInvoiceDetails(grnItem.getInvoiceRefNum());
            Double invoiceItemPrice = grnCacheServiceUtils.getInvoiceItemPrice(grnItem.getInvoiceRefNum(),
                    grnItem.getPid());
            long grnInvoiceQty = grnCacheServiceUtils.incrementCLInvoiceItemQtyCount(grnItem.getInvoiceRefNum());
            double grnInvoiceItemTotalPrice = grnCacheServiceUtils.incrementCLInvoiceItemPrice(grnItem.getInvoiceRefNum(), invoiceItemPrice,
                    grnItem.getPoId(), grnItem.getGrnCode());
            log.info("[itemQuantityCheck] Grn code: {}, barcode: {}," +
                            "Total grn quantity {}, invoice qty {}, total grn price {}, invoice price {}, " +
                    "invoiceItemPrice {}", grnItem.getGrnCode(), grnItem.getBarcode(),
                    grnInvoiceQty, invoiceQtyAndPriceResponse.getInvoiceTotalCount(),
                    grnInvoiceItemTotalPrice, invoiceQtyAndPriceResponse.getInvoiceTotalPrice(), invoiceItemPrice);

            if(grnInvoiceItemTotalPrice > invoiceQtyAndPriceResponse.getInvoiceTotalPrice()
                    || grnInvoiceQty > invoiceQtyAndPriceResponse.getInvoiceTotalCount()){
                log.error("Grn Total count or price can not be greater than Invoice qty or price");
                grnCacheServiceUtils.decrementCLInvoiceItemQtyCount(grnItem.getInvoiceRefNum());
                grnCacheServiceUtils.decrementCLInvoiceItemPrice(grnItem.getInvoiceRefNum(), invoiceItemPrice);
                throw new ApplicationException("Grn Total count or price can not be greater than Invoice qty or price",
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            log.info("Total grn quantity {}, invoice qty {}, total grn price {}, invoice price {}",
                    grnInvoiceQty, invoiceQtyAndPriceResponse.getInvoiceTotalCount(),
                    grnInvoiceItemTotalPrice, invoiceQtyAndPriceResponse.getInvoiceTotalPrice());

            poQty = grnCacheServiceUtils.incrementItemQtyCount(grnItem.getPoId(), grnItem.getPid(), true);
            log.info("[itemQuantityCheck] Grn code: {}, barcode: {}, " +
                            "Po: {}, pId: {}, invoiceQty: {},  " +
                            "grn invoiceQty: {}, poQty: {}, grn poQty: {}",
                    grnItem.getGrnCode(), grnItem.getBarcode(), grnItem.getPoId(), grnItem.getPid(),
                    grnInvoiceQty, grnItem.getInvoiceQuantity(), poQty, grnItem.getPoQuantity());
            if(poQty > grnItem.getPoQuantity()){
                grnCacheServiceUtils.decrementCLInvoiceItemQtyCount(grnItem.getInvoiceRefNum());
                grnCacheServiceUtils.decrementCLInvoiceItemPrice(grnItem.getInvoiceRefNum(), invoiceItemPrice);
                long decrPoQty = grnCacheServiceUtils.decrementItemQtyCount(grnItem.getPoId(), grnItem.getPid(), true);

                String errorMessage = "PO quantity reached for pid : " + grnItem.getPid();
                log.error("[itemQuantityCheck] Po: {}, pId: {}, DECREMENT COUNT AFTER grn code: {}, decrPoQty :{}",
                        grnItem.getPoId(), grnItem.getPid(), grnItem.getGrnCode(), decrPoQty);
                throw new InvalidRequestException(errorMessage, GRNExceptionStatus.GRN_BAD_REQUEST);
            }
        } else {
            log.info("[itemQuantityCheck] grnCode {}", grnItem.getGrnCode());
            invoiceQty = grnCacheServiceUtils.incrementItemQtyCount(grnItem.getInvoiceRefNum(), grnItem.getPid());
            poQty = grnCacheServiceUtils.incrementItemQtyCount(grnItem.getPoId(), grnItem.getPid(), true);

            log.info("[itemQuantityCheck] Po: {}, pId: {}, grn code: {}, invoiceQty: {},  grn invoiceQty: {}, poQty: {}, grn poQty: {}", grnItem.getPoId(),
                    grnItem.getPid(), grnItem.getGrnCode(), invoiceQty, grnItem.getInvoiceQuantity(), poQty, grnItem.getPoQuantity());

            if(invoiceQty > grnItem.getInvoiceQuantity() || poQty > grnItem.getPoQuantity()) {
                log.error("[itemQuantityCheck] Po: {}, pId: {}, DECREMENT COUNT grn code: {}, invoiceQty: {}, grn invoiceQty: {}," +
                                " poQty: {}, grn poQty: {},",
                        grnItem.getPoId(), grnItem.getPid(), grnItem.getGrnCode(),
                        invoiceQty, grnItem.getInvoiceQuantity(), poQty, grnItem.getPoQuantity());

                long decrInvoiceQty = grnCacheServiceUtils.decrementItemQtyCount(grnItem.getPoId(), grnItem.getPid());
                long decrPoQty = grnCacheServiceUtils.decrementItemQtyCount(grnItem.getInvoiceRefNum(), grnItem.getPid(), true);

                String errorMessage = "Invoice quantity reached for pid : " + grnItem.getPid();
                if (poQty > grnItem.getPoQuantity())
                    errorMessage = "PO quantity reached for pid : " + grnItem.getPid();

                log.error("[itemQuantityCheck] Po: {}, pId: {}, DECREMENT COUNT AFTER grn code: {}, decrInvoiceQty: {}, decrPoQty :{}",
                        grnItem.getPoId(), grnItem.getPid(), grnItem.getGrnCode(), decrInvoiceQty, decrPoQty);
                throw new InvalidRequestException(errorMessage, GRNExceptionStatus.GRN_BAD_REQUEST);
            }
        }
    }

    @Logging
    public void loadConfig(GRNItem grnItem) {
        if(!grnCacheServiceUtils.hasReferenceKey(grnItem.getInvoiceRefNum(), grnItem.getPid()))
            cacheDAO.loadConfig(grnItem, true);
        if(!grnCacheServiceUtils.hasGrnKey(grnItem.getGrnCode(), grnItem.getPid())) {
            cacheDAO.initializeKeys(grnItem, 0 ,0);
        }
    }

    public void setItemParams(GRNItem grnItem, String status, int channelStatus) {
        grnItem.setStatus(status);
        grnItem.setChannelStatus(channelStatus);
        log.info("Grn item after setting params : " + grnItem);
    }

    public void deleteClInvoiceGrnKey(GrnItemEntity grnItem) throws InvalidRequestException {
        try{
            log.info("[decrementClInvoiceGrnKey] barcode {}", grnItem.getBarcode());
            String invoiceRefNumber = grnItem.getInvoiceRefNum();
            String scanCountKey = CacheUtils.getCLInvoiceItemScanCountKey(grnConfig.getKeyPrefix(), invoiceRefNumber);
            String priceKey = CacheUtils.getCLInvoiceItemPriceKey(grnConfig.getKeyPrefix(), invoiceRefNumber);
            String poKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), grnItem.getPoId(), grnItem.getPid());
            RedisHandler.redisOps(RedisOps.DEL, scanCountKey);
            RedisHandler.redisOps(RedisOps.DEL, priceKey);
            RedisHandler.redisOps(RedisOps.DEL, poKey);
            log.info("[decrementClInvoiceGrnKey] barcode {} keys deleted scanCountKey {}, priceKey {}, poKey {}",
                    grnItem.getBarcode(), scanCountKey, priceKey, poKey);
        } catch (Exception e){
            log.error("[decrementClInvoiceGrnKey] barcode {}, error {}", grnItem.getBarcode(), e.getMessage());
            throw new InvalidRequestException("Error in deleting redis key " + e.getMessage(),
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }
}
