package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.UserDetailsDAO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.UserDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserDetailsService {

    @Autowired
    @Qualifier(QualifierConstants.JPA_USER_DETAILS_DAO)
    private UserDetailsDAO userDetailsDAO;

    @Logging
    public void addUserDetails(String userId) {
        try{
            UserDetails existingUserDetails = getUserDetailsByEmpCode( getTrimValue(MDC.get("USER_ID")));

            UserDetails centralAuthUserDetails = new UserDetails();
            centralAuthUserDetails.setEmpCode( getTrimValue(MDC.get("USER_ID")));
            centralAuthUserDetails.setEmail( getTrimValue(MDC.get("USER_MAIL")));
            centralAuthUserDetails.setPhoneNumber( getTrimValue(MDC.get("PHONE_NUMBER")));
            centralAuthUserDetails.setPhoneCode( getTrimValue(MDC.get("PHONE_CODE")));
            centralAuthUserDetails.setName( getTrimValue(MDC.get("USER_NAME")));
            log.info("Adding central auth user Details in NEXS,  "+centralAuthUserDetails);

            if(existingUserDetails == null){
                log.info("Adding User Details");
                userDetailsDAO.addUserDetails(centralAuthUserDetails);
            }else{
                if(!( StringUtils.equalsIgnoreCase( getTrimValue(existingUserDetails.getEmpCode()), getTrimValue(MDC.get("USER_ID"))) &&
                       StringUtils.equalsIgnoreCase( getTrimValue(existingUserDetails.getName()), getTrimValue(MDC.get("USER_NAME"))) &&
                       StringUtils.equalsIgnoreCase( getTrimValue(existingUserDetails.getPhoneCode()), getTrimValue(MDC.get("PHONE_CODE"))) &&
                       StringUtils.equalsIgnoreCase( getTrimValue(existingUserDetails.getPhoneNumber()), getTrimValue(MDC.get("PHONE_NUMBER"))) &&
                       StringUtils.equalsIgnoreCase( getTrimValue(existingUserDetails.getEmail()), getTrimValue(MDC.get("USER_MAIL"))))){
                    log.info("Updating User Details");
                    userDetailsDAO.updateUserDetails(centralAuthUserDetails);
               }else{
                    log.info("User details are already updated");
                }
            }
        }catch (Exception e){
            log.info("Error in adding user details "+e.getMessage());
            throw new ApplicationException("Error in adding user details : " + e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    public UserDetails getUserDetailsByEmpCode(String userId) {
        UserDetails userDetails = userDetailsDAO.getUserDetailsByEmpCode(userId);
        log.info("Existing user Details for user id "+userId +", "+userDetails);
        return userDetails;
    }

    public String getTrimValue(String value){
        return value == null ? null : value.trim();
    }
}
