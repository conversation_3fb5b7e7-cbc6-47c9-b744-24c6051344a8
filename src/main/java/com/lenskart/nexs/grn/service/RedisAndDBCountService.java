package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.common.entity.entityServiceImpl.PurchaseOrderEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.invoice.PurchaseInvoiceEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.PurchaseOrder;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.RedisCountDBCountDiffResponse;
import com.lenskart.nexs.grn.util.CacheUtils;
import com.lenskart.nexs.service.RedisHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RedisAndDBCountService {

//    @Autowired
//    private DBConfig dbConfig;

    @Autowired
    private PurchaseInvoiceEntityServiceImpl purchaseInvoiceEntityService;

    @Autowired
    private PurchaseOrderEntityServiceImpl purchaseOrderEntityService;

    @Autowired
    private GrnItemEntityServiceImpl grnItemEntityService;

    @Autowired
    private GRNConfig grnConfig;

    public List<RedisCountDBCountDiffResponse> getRedisAndDBCountDiff(String type) {
        List<String> itemIds = getIdsFromDB(type);
        log.info("Fetched " + type + " item ids of size: {} : {}", itemIds.size(), itemIds);
        List<RedisCountDBCountDiffResponse> redisCountDBCountDiffResponses = new ArrayList<>();
        for (String itemId : itemIds) {
            redisCountDBCountDiffResponses.addAll(getEachPidDiff(itemId, type));
            log.info("end of processing type: {} for id: {}", type, itemId);
        }
        log.info("end of processing type: {}", type);
        return redisCountDBCountDiffResponses;
    }

    private List<String> getIdsFromDB(String type) {
        try {
            String query;
            if (type.equalsIgnoreCase("invoice")) {
                List<PurchaseInvoiceEntity> purchaseInvoiceEntities = purchaseInvoiceEntityService.findByStatus(
                        "CREATED");
                return purchaseInvoiceEntities.stream().map(PurchaseInvoiceEntity::getInvoiceRefNumber)
                        .map(Object::toString).collect(Collectors.toList());
            } else if (type.equalsIgnoreCase("po")) {
                List<PurchaseOrder> purchaseOrderList = purchaseOrderEntityService.findByStatus("APPROVED");
                return purchaseOrderList.stream().map(PurchaseOrder::getPoNum).collect(Collectors.toList());
            } else {
                log.error("Unsupported type '{}' please give 'invoice' or 'po' as the input", type);
                throw new ApplicationException("Unsupported type '" + type + "' please give 'invoice' or 'po' as the " +
                        "input", GRNExceptionStatus.GRN_BAD_REQUEST);
            }
        } catch (Exception ex) {
            log.error("Exception : {}", ex.getMessage());
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private List<RedisCountDBCountDiffResponse> getEachPidDiff(String itemId, String type) {
        try {
            List<Tuple> grnItemEntityList = new ArrayList<>();
            if (type.equalsIgnoreCase("invoice")) {
                grnItemEntityList = grnItemEntityService.getInvoiceRefNumGroupByPid(itemId);
            } else if (type.equalsIgnoreCase("po")) {
                grnItemEntityList = grnItemEntityService.getPoIdGroupByPid(itemId);
            } else {
                log.error("Unsupported type '{}' please give 'invoice' or 'po' as the input", type);
                throw new ApplicationException("Unsupported type '" + type + "' please give 'invoice' or 'po' as the " +
                        "input", GRNExceptionStatus.GRN_BAD_REQUEST);
            }

            List<RedisCountDBCountDiffResponse> redisCountDBCountDiffResponses = new ArrayList<>();
            for (Tuple tuple : grnItemEntityList) {
                long dbCount = tuple.get("total_scanned", Long.class);
                String pid = tuple.get("pid", String.class);
                Long redisCount = getRedisCount(itemId, pid);
                if (redisCount != null && dbCount != redisCount) {
                    RedisCountDBCountDiffResponse diffResponse = new RedisCountDBCountDiffResponse();
                    diffResponse.setId(itemId);
                    diffResponse.setPid(pid);
                    diffResponse.setDbCount(dbCount);
                    diffResponse.setRedisCount(redisCount);
                    diffResponse.setDifference(Math.abs(dbCount - redisCount));
                    redisCountDBCountDiffResponses.add(diffResponse);
                }
            }
            return redisCountDBCountDiffResponses;
        } catch (Exception ex) {
            log.error("Exception : {}", ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private Long getRedisCount(String itemId, String pid) {
        try {
            String cacheKey = CacheUtils.getItemScanCountKey(grnConfig.getKeyPrefix(), itemId, pid);
            log.info("checking for key: {} in redis", cacheKey);
            if (RedisHandler.hasKey(cacheKey)) {
                return Long.parseLong(RedisHandler.redisOps(RedisOps.GET, cacheKey).toString());
            } else {
                return null;
            }
        } catch (Exception ex) {
            log.error("Unexpected Exception {}", ex.getMessage());
            throw new ApplicationException("Unexpected Exception " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public List<RedisCountDBCountDiffResponse> getRedisAndDBCountDiff(String type, Set<String> itemIds) {
        List<RedisCountDBCountDiffResponse> redisCountDBCountDiffResponses = new ArrayList<>();
        log.info("TimeStamp at start of processing: {} of type: {} and ids:{}", new Date(), type, itemIds);
        for (String itemId : itemIds) {
            redisCountDBCountDiffResponses.addAll(getEachPidDiff(itemId, type));
            log.info("end of processing type:{} for id: {}", type, itemId);
        }
        log.info("TimeStamp at end of processing: {}of type: {}", new Date(), type);
        return redisCountDBCountDiffResponses;
    }
}
