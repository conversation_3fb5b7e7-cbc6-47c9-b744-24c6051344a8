package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.IqcGrnProductEntity;
import com.lenskart.nexs.common.model.response.grn.ScanBarcodeResponseModel;
import com.lenskart.nexs.grn.dto.request.ScanIQCItemRequestDTO;
import com.lenskart.nexs.grn.dto.response.IqcGrnScanItemResponseDTO;
import com.lenskart.nexs.grn.model.GRNItem;

import java.util.List;
import java.util.Map;


public interface IQCItemService {
    public IqcGrnScanItemResponseDTO scanIQCItem(ScanIQCItemRequestDTO scanIQCItemRequestDTO);

    // public IqcGrnProductEntity getIqcProductGrnDetails(String boxcode, String grnCode);

    public void updateImsItemStatus(String grnCode, List<GrnItemEntity> grnItems) throws Exception;

    void createAndUpdatePutAway(GRNItem grnItem) throws Exception;

    ScanBarcodeResponseModel scanBoxOrBarCode(String grnCode, String pid, String barcode, String facility) throws Exception;

    IqcGrnProductEntity completeIqc(String boxcode, String grnCode, String pid);

    String markGrnIqcComplete(String grnCode, String boxCode, String pid);

    IqcGrnScanItemResponseDTO deleteIqcScanItem(String boxCode, String barCode);

    Map<String, Object> getIqcBarcodesDetails(List<IqcGrnProductEntity> iqcGrnProductEntityList, String grnCode);

}
