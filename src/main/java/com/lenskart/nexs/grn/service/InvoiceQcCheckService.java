package com.lenskart.nexs.grn.service;

import java.util.Set;

import javax.annotation.PostConstruct;

import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.grn.constants.GRNConstants;
//import com.lenskart.nexs.grn.model.QcConfig;
import com.lenskart.nexs.grn.util.CacheUtils;
import com.lenskart.nexs.service.RedisHandler;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class InvoiceQcCheckService implements GRNConstants, Runnable {

    @Autowired
    private GRNConfig grnConfig;

    @PostConstruct
    public void invoiceQcCheck() {
        //new Thread(this).start();
    }

    @Logging
    @Override
    public void run() {
        while (true) {
            threadSleep();
            Set<String> keys = RedisHandler.keys(grnConfig.getKeyPrefix() + INVOICE + "*" + QC_CONFIG);
            log.info("invoice keys map : " + keys);

            for(String key : keys) {
                long qcProcessed = 0;
                try {
//                    QcConfig invoiceConfig = CommonUtils.objectMapper.readValue(RedisHandler.redisOps(RedisOps.GET, key).toString(),
//                            QcConfig.class);
//                    long invoiceSamplingQty = invoiceConfig.getSamplingQuantity();

                    String[] parts = key.split(DEL);
                    Set<String> grnSet = RedisHandler.keys(grnConfig.getKeyPrefix() + GRN + "*" + parts[3] + "*" + STATUS);
                    for(String grn : grnSet) {
                        String[] gParts = grn.split(DEL);
                        String configKey = CacheUtils.getGrnConfigKey(grnConfig.getKeyPrefix(), gParts[2], gParts[3]);
                        String scanKey = CacheUtils.getScanCountKey(grnConfig.getKeyPrefix(), gParts[2], gParts[3]);
//                        QcConfig qcConfig = CommonUtils.objectMapper.readValue(RedisHandler.redisOps(RedisOps.GET, configKey).toString(), QcConfig.class);
//                        long samplingQuantity = qcConfig.getSamplingQuantity();
                        long totalScanned = Long.parseLong(RedisHandler.redisOps(RedisOps.GET, scanKey).toString());
                        qcProcessed += totalScanned;
                    }

//                    if(qcProcessed >= invoiceSamplingQty) {
//                        for(String statusKey : grnSet) {
//                            String status = RedisHandler.redisOps(RedisOps.GET, statusKey).toString();
//                            if(PENDING.equals(status))
//                                RedisHandler.redisOps(RedisOps.SETVALUE, statusKey, PASSED);
//                        }
//                    }
                } catch (JsonProcessingException ex) {
                    log.error("JsonProcessingException while invoice qcCheck", ex);
                } catch (Exception ex) {
                    log.error("Unknown Exception while invoice qcCheck", ex);
                }
            }
        }
    }

    @Logging
    public void threadSleep() {
        try {
            Thread.sleep(60000);
        } catch (InterruptedException ex) {
          	log.error("InvoiceQcCheckService Exception while invoice qcCheck", ex);
        }
    }
}