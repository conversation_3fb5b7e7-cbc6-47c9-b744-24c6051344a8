package com.lenskart.nexs.grn.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderEntityService;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderItemEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceItemEntityService;
import com.lenskart.nexs.common.entity.po.PurchaseOrderItem;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceItemEntity;
import com.lenskart.nexs.common.entity.repositories.PurchaseOrderRepositories;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.POProduct;
import com.lenskart.nexs.grn.model.Product;
import com.lenskart.nexs.grn.model.PurchaseOrder;
import com.lenskart.nexs.grn.service.PoInvoiceService;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class PoInvoiceServiceImpl implements PoInvoiceService {

    @CustomLogger
    private Logger log;

    @Autowired
    private PurchaseOrderItemEntityService purchaseOrderItemEntityService;

    @Autowired
    PurchaseInvoiceEntityService purchaseInvoiceEntityService;

    @Autowired
    PurchaseInvoiceItemEntityService purchaseInvoiceItemEntityService;

    @Autowired
    PurchaseOrderEntityService purchaseOrderEntityService;

    @Autowired
    private PurchaseOrderRepositories purchaseOrderRepositories;

    @Override
    public Invoice getInvoiceDetails(String invoiceRefNum) throws Exception {
        try {
            log.info("[getInvoiceDetails] invoiceRefNum {}", invoiceRefNum);
            if (invoiceRefNum == null) {
                log.error("Invoice ref number can not be null");
                throw new ApplicationException("Invoice ref number can not be null",
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            PurchaseInvoiceEntity invoiceEntity =
                    purchaseInvoiceEntityService.getInvoiceByReferenceNumber(Integer.valueOf(invoiceRefNum), true);
            if (invoiceEntity == null) {
                log.error("Invoice details does not exist for invoice ref num {}", invoiceRefNum);
                throw new ApplicationException("Invoice details does not exist for invoice ref num " + invoiceRefNum,
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            List<PurchaseInvoiceItemEntity> invoiceItemEntities =
                    purchaseInvoiceItemEntityService.getByInvoiceRefNumber(Integer.valueOf(invoiceRefNum), true);
            com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder =
                    purchaseOrderEntityService.getPurchaseOrder(invoiceEntity.getPoNum());
            Invoice invoiceDetails = createInvoiceDetailsObjectFromInvoice(purchaseOrder, invoiceEntity, invoiceItemEntities);
            log.info("[getInvoiceDetails] Invoice {}", invoiceDetails);
            return invoiceDetails;
        } catch (Exception e) {
            log.error("Error {}", e.getMessage());
            throw new ApplicationException("Error in getting invoice details " + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public Invoice getInvoiceDetailsByVendorInvoiceNum(String vendorInvoiceNum) throws Exception {
        try {
            log.info("[getInvoiceDetails] vendorInvoiceNum {}", vendorInvoiceNum);
            if (vendorInvoiceNum == null) {
                log.error("Invoice ref number can not be null");
                throw new ApplicationException("Invoice ref number can not be null",
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            PurchaseInvoiceEntity invoiceEntity =
                    purchaseInvoiceEntityService.findByVendorInvoiceNumber(vendorInvoiceNum);
            if (invoiceEntity == null) {
                log.error("Invoice details does not exist for vendor invoice num {}", vendorInvoiceNum);
                throw new ApplicationException("Invoice details does not exist for vendor invoice num " + vendorInvoiceNum,
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            List<PurchaseInvoiceItemEntity> invoiceItemEntities =
                    purchaseInvoiceItemEntityService.getByInvoiceRefNumber(invoiceEntity.getInvoiceRefNumber(), true);
            com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder =
                    purchaseOrderEntityService.getPurchaseOrder(invoiceEntity.getPoNum());
            Invoice invoiceDetails = createInvoiceDetailsObjectFromInvoice(purchaseOrder, invoiceEntity, invoiceItemEntities);
            log.info("[getInvoiceDetails] Invoice {}", invoiceDetails);
            return invoiceDetails;
        } catch (Exception e) {
            log.error("Error {}", e.getMessage());
            throw new ApplicationException("Error in getting invoice details " + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private Invoice createInvoiceDetailsObjectFromInvoice(com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder,
                                                          PurchaseInvoiceEntity invoiceEntity,
                                                          List<PurchaseInvoiceItemEntity> invoiceItemEntities) throws JsonProcessingException {
        try {
            log.info("[createInvoiceDetailsObjectFromInvoice] Create invoice object for invoice ref num {}",
                    invoiceEntity.getInvoiceRefNumber());
            Invoice invoice = new Invoice();
            List<Product> pids = new ArrayList<>();
            invoice.setInvoiceRefNum(String.valueOf(invoiceEntity.getInvoiceRefNumber()));
            invoice.setInvoiceId(invoiceEntity.getVendorInvoiceNumber());
            invoice.setPoId(invoiceEntity.getPoNum());
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            if (purchaseOrder.getApprovedAt() != null) {
                Date approvedAt = purchaseOrder.getApprovedAt();
                invoice.setPoDate(format.format(approvedAt));
                Timestamp poApprovedAt = new Timestamp(approvedAt.getTime());
                poApprovedAt.setHours(poApprovedAt.getHours() + 5);
                poApprovedAt.setMinutes(poApprovedAt.getMinutes() + 30);
                invoice.setApprovedAt(poApprovedAt.getTime());
                log.info("[createInvoiceDetailsObjectFromInvoice] poDate {}, approvedAt {}",
                        invoice.getPoDate(), invoice.getApprovedAt());
            }
            if (invoiceEntity.getInvoiceDate() != null) {
                Date invoiceDate = invoiceEntity.getInvoiceDate();
                invoice.setInvoiceDate(format.format(invoiceDate));
                Timestamp invoiceDateTime = new Timestamp(invoiceDate.getTime());
                invoiceDateTime.setHours(invoiceDateTime.getHours() + 5);
                invoiceDateTime.setMinutes(invoiceDateTime.getMinutes() + 30);
                invoice.setInvoice_date(invoiceDateTime.getTime());
            }
            invoice.setPreFilledInvoice(invoiceEntity.isPrefilledInvoice());
            invoice.setVendor(purchaseOrder.getVendorId());
            invoice.setVendorName(purchaseOrder.getVendorName());
            invoice.setCurrency(invoiceEntity.getCurr());
            if (invoiceEntity.getB2bInvoiceDate() != null)
                invoice.setB2bInvoiceDate(invoiceEntity.getB2bInvoiceDate().getTime());
            invoice.setSendToParty(invoiceEntity.getSendToParty());
            invoice.setHandoverParty(invoiceEntity.getHandoverParty());
            invoice.setBillOfEntryNumber(invoiceEntity.getBillOfEntryNumber());
            invoice.setBillOfEntryAmount(invoiceEntity.getBillOfEntryAmount());
            invoice.setTotalInvoiceQty(invoiceEntity.getTotalInvoiceQty());
            invoice.setOrderAcceptedQuantity(invoice.getOrderAcceptedQuantity());
            invoice.setOrderRejectedQuantity(invoice.getOrderRejectedQuantity());
            if (invoiceEntity.getBillOfEntryDate() != null)
                invoice.setBillOfEntryDate(invoiceEntity.getBillOfEntryDate().getTime());
            Double totalReceivedAmount = 0.0;
            Double totalRejectedAmount = 0.0;
            for (PurchaseInvoiceItemEntity item : invoiceItemEntities) {
                Product product = createProductItemsObject(item);
                pids.add(product);
                totalReceivedAmount = totalReceivedAmount + (item.getVendorUnitCostPrice() * item.getAcceptedQuantity());
                totalRejectedAmount = totalRejectedAmount + (item.getVendorUnitCostPrice() * item.getRejectedQuantity());
            }
            invoice.setTotalReceivedAmount(totalReceivedAmount);
            invoice.setTotalRejectedAmount(totalRejectedAmount);
            invoice.setPids(pids);
            invoice.setLegalOwner(invoiceEntity.getLegalOwner());
            log.info("[createInvoiceDetailsObjectFromInvoice] invoice {}", invoice);
            return invoice;
        } catch (Exception e) {
            log.info("[createInvoiceDetailsObjectFromInvoice] error {}", e.getMessage());
            throw new ApplicationException("Unable to create invoice details object" + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private Product createProductItemsObject(PurchaseInvoiceItemEntity item) throws JsonProcessingException {
        try {
            log.info("[createProductItemsObject] Create Product Item");
            Product product = new Product();
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> productAttributesMapping =
                    mapper.readValue(item.getProductAttributes(), new TypeReference<Map<String, Object>>() {
                    });

            product.setPid(String.valueOf(item.getProductId()));
            product.setQuantity(item.getQuantity());
            product.setPrice(item.getVendorUnitCostPrice());
            if (productAttributesMapping.get("classification") != null)
                product.setCategoryId((int) productAttributesMapping.get("classification"));
            if (productAttributesMapping.get("brand") != null)
                product.setBrand((String) productAttributesMapping.get("brand"));
            if (productAttributesMapping.get("vendor_sku") != null)
                product.setVendorSku((String) productAttributesMapping.get("vendor_sku"));
            if (productAttributesMapping.get("gtin") != null)
                product.setGtin((String) productAttributesMapping.get("gtin"));
            if (productAttributesMapping.get("upc") != null)
                product.setUpc((String) productAttributesMapping.get("upc"));
            if (productAttributesMapping.get("desc") != null)
                product.setPidDescription((String) productAttributesMapping.get("desc"));
            product.setTaxRate(item.getUgstRate());
            product.setCgstRate(item.getCgstRate());
            product.setSgstRate(item.getSgstRate());
            product.setIgstRate(item.getIgstRate());
            product.setTotalVendorCostPrice(item.getTotalVendorCostPrice());
            product.setPriceWithTaxes(item.getPriceWithTaxes());
            product.setIqcSamplingPercent(item.getIqcSamplingPercent());
            log.info("[createProductItemsObject] Product Item {}", product);
            return product;
        } catch (Exception e) {
            log.info("[createProductItemsObject] error {}", e.getMessage());
            throw new ApplicationException("Unable to create product details object " + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public PurchaseOrder getPurchaseOrderDetails(String poNum) throws Exception {
        try {
            log.info("[getInvoiceDetails] po num {}", poNum);
            List<PurchaseOrderItem> poItem = purchaseOrderItemEntityService.getPurchaseOrderItemByPoNum(poNum);
            com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder =
                    purchaseOrderEntityService.getPurchaseOrder(poNum);
            PurchaseOrder poItemObject = createPurchaseOrderDetailsFromPo(poNum, purchaseOrder.getInvoiceLevel(), poItem);
            log.info("[getInvoiceDetails] poItemObject {}", poItemObject);
            return poItemObject;
        } catch (Exception e) {
            log.error("Error {}", e.getMessage());
            throw new ApplicationException("Error in getting po details: " + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private PurchaseOrder createPurchaseOrderDetailsFromPo(String poNum, String invoiceLevel, List<PurchaseOrderItem> poItem) {
        try {
            log.info("[createPurchaseOrderDetailsFromPo] poNum {}", poNum);
            PurchaseOrder po = new PurchaseOrder();
            List<POProduct> pIds = new ArrayList<>();
            for (PurchaseOrderItem item : poItem) {
                POProduct poProduct = new POProduct();
                poProduct.setPid(String.valueOf(item.getProductId()));
                poProduct.setQuantity(item.getQuantity());
                poProduct.setPrice(item.getVendorUnitCostPrice());
                poProduct.setTotalVendorCostPrice(item.getTotalVendorCostPrice());
                poProduct.setCgstRate(item.getCgstRate());
                poProduct.setSgstRate(item.getSgstRate());
                poProduct.setIgstRate(item.getIgstRate());
                poProduct.setPriceWithTaxes(item.getPriceWithTaxes());

                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> productAttributesMapping =
                        mapper.readValue(item.getProductAttributes(), new TypeReference<Map<String, Object>>() {
                        });
                if (productAttributesMapping.get("classification") != null)
                    poProduct.setCategoryId((int) productAttributesMapping.get("classification"));

                pIds.add(poProduct);
            }
            po.setPoId(poNum);
            po.setPids(pIds);
            po.setInvoiceLevel(invoiceLevel);
            log.info("[createPurchaseOrderDetailsFromPo] po details {}", po);
            return po;
        } catch (Exception e) {
            log.info("[createPurchaseOrderDetailsFromPo] error {}", e.getMessage());
            throw new ApplicationException("Unable to create purchase order details object " + e.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public com.lenskart.nexs.common.entity.po.PurchaseOrder getPurchaseOrder(String poNum) throws CustomException {
        log.info("[getPurchaseOrder] poNum {}", poNum);
        com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder = purchaseOrderRepositories.findByPoNum(poNum);
        if(purchaseOrder == null){
            log.error("[getPurchaseOrder] Purchase Order does not exist for {}", poNum);
            throw new CustomException("Purchase Order does not exist for " + poNum, 400);
        }
        return purchaseOrder;
    }

}
