package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dto.response.ASNResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ASNService {

    @Autowired
    private CacheDAO cacheDAO;

    @Logging
    public ASNResponseDTO getASN(String poId, String vendorInvoiceNum, String barcode) {
        return cacheDAO.getASN(poId, vendorInvoiceNum, barcode);
    }
}
