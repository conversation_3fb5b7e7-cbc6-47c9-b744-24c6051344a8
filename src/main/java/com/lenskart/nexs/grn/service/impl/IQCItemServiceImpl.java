package com.lenskart.nexs.grn.service.impl;

import com.google.common.collect.Lists;
import com.lenskart.nexs.common.entity.entityService.grn.GrnItemEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnMasterEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.IqcGrnProductEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.IqcInvoiceProductEntityService;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.common.entity.po.grn.IqcGrnProductEntity;
import com.lenskart.nexs.common.entity.po.invoice.IqcInvoiceProductEntity;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.common.model.response.grn.ScanBarcodeResponseModel;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.IQCConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.GRNItemDAO;
import com.lenskart.nexs.grn.dao.IQCItemHDAO;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.request.ScanIQCItemRequestDTO;
import com.lenskart.nexs.grn.dto.response.IqcGrnScanItemResponseDTO;
import com.lenskart.nexs.grn.dto.response.ScanItemResponseDTO;
import com.lenskart.nexs.grn.enums.ActionStatus;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.putaway.service.PutawayService;
import com.lenskart.nexs.grn.service.IMSService;
import com.lenskart.nexs.grn.service.IQCItemService;
import com.lenskart.nexs.grn.util.GRNItemServiceUtils;
import com.lenskart.nexs.ims.request.BarcodeItemRequest;
import com.lenskart.nexs.ims.request.FetchStockDetailsRequest;
import com.lenskart.nexs.ims.request.UpdateStockInwardRequest;
import com.lenskart.nexs.ims.response.FetchStockDetailsResponse;
import com.lenskart.nexs.putaway.model.response.CreatePutawayResponse;
import com.nexs.po.common.enums.GrnImsSyncStatusEnum;
import com.nexs.po.common.enums.IqcGrnProductStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import javax.persistence.Tuple;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IQCItemServiceImpl implements IQCItemService {

    @Autowired
    private IQCItemHDAO iQCItemHDAO;

    @Autowired
    private PutawayService putawayService;

    @Autowired
    private GrnItemEntityService grnItemEntityService;

    @Autowired
    private IqcGrnProductEntityService iqcGrnProductEntityService;

    @Autowired
    private IMSService imsService;

    @Autowired
    GrnMasterEntityService grnMasterEntityService;

    @Autowired
    @Qualifier(QualifierConstants.JPA_GRN_ITEM_DAO)
    private GRNItemDAO gRNItemDAO;

    @Value("${grn.putaway.base.url}")
    public String putawayBaseUrl;

    @Autowired
    IqcInvoiceProductEntityService iqcInvoiceProductEntityService;

    @Autowired
    GRNConfig grnConfig;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public IqcGrnScanItemResponseDTO scanIQCItem(ScanIQCItemRequestDTO scanIQCItemRequestDTO) {
        IqcGrnScanItemResponseDTO iqcGrnScanItemResponseDTO;
        try {
            log.info("Going to update status for IQC barcode {} with grn code: {}", scanIQCItemRequestDTO.getBarcode(), scanIQCItemRequestDTO.getGrnCode());
            String boxCode = scanIQCItemRequestDTO.getBoxCode();
            String grnCode = scanIQCItemRequestDTO.getGrnCode();
            String pid = scanIQCItemRequestDTO.getPid();
            IqcGrnProductEntity iqcGrnProductEntity = iQCItemHDAO.getIqcProductGrnDetails(boxCode, grnCode, pid);
            if (iqcGrnProductEntity == null)
                throw new ApplicationException("Details does not exist in grn iqc table for grnCode " + grnCode +
                        " boxCode " + boxCode + " pid " + pid, GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            validateIqcGrnProductEntity(iqcGrnProductEntity, scanIQCItemRequestDTO.getQcStatus());
            GrnItemEntity grnItemEntity = iQCItemHDAO.updateIQcStatus(scanIQCItemRequestDTO.getBarcode(), scanIQCItemRequestDTO.getQcStatus(), scanIQCItemRequestDTO.getQcReason(), scanIQCItemRequestDTO.getGrnCode(), scanIQCItemRequestDTO.getBoxCode(), scanIQCItemRequestDTO.getPid());
            log.info("Received grnItemEntity details {}" + grnItemEntity.toString());
            List<IqcGrnProductEntity> totalIqcGrnProductEntityList = iQCItemHDAO.findByGrnCode(grnCode);
            log.info("Received iqcGrnProductEntity details {}" + iqcGrnProductEntity.toString());
            IqcInvoiceProductEntity iqcInvoiceProductEntity = iQCItemHDAO.findByInvoiceRefNumber(Integer.parseInt(grnItemEntity.getInvoiceRefNum()), Integer.parseInt(grnItemEntity.getPid()));
            log.info("Received iqcInvoiceProductEntity {}" + iqcInvoiceProductEntity.toString());
            iqcGrnScanItemResponseDTO = new IqcGrnScanItemResponseDTO();
            iqcGrnScanItemResponseDTO.setBarCode(grnItemEntity.getBarcode());
            String apiType = "regular";
            buildIqcInvoiceProductItem(iqcInvoiceProductEntity, scanIQCItemRequestDTO.getQcStatus(), iqcGrnScanItemResponseDTO, apiType);
            iqcGrnScanItemResponseDTO = buildIqcGrnItem(iqcGrnProductEntity, scanIQCItemRequestDTO.getQcStatus(),
                    iqcGrnScanItemResponseDTO, apiType, totalIqcGrnProductEntityList);
            log.info("Result after IQC ScanCount operation {}", iqcGrnScanItemResponseDTO);
        } catch (Exception ex) {
            log.error("Exception: IQC Scanning barcode {} , grn code: {} , error: {}", scanIQCItemRequestDTO.getBarcode(), scanIQCItemRequestDTO.getGrnCode(), ex.getMessage());
            throw new ApplicationException("Exception: IQC Scanning barcode " + scanIQCItemRequestDTO.getBarcode() + " error " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        return iqcGrnScanItemResponseDTO;
    }

    private void validateIqcGrnProductEntity(IqcGrnProductEntity iqcGrnProductEntity, String qcStatus) {
        if (IQCConstants.IQC_PASS.equalsIgnoreCase(qcStatus) &&
                iqcGrnProductEntity.getQcFailQty() + iqcGrnProductEntity.getQcPassQty() + 1 > iqcGrnProductEntity.getSamplingQty())
            throw new ApplicationException("Scanned qty can not be greater than sampling qty",
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        if (IQCConstants.IQC_FAIL.equalsIgnoreCase(qcStatus) &&
                iqcGrnProductEntity.getQcFailQty() + iqcGrnProductEntity.getQcPassQty() > iqcGrnProductEntity.getSamplingQty())
            throw new ApplicationException("Scanned qty can not be greater than sampling qty",
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
    }

    private void buildIqcInvoiceProductItem(IqcInvoiceProductEntity iqcInvoiceProductEntity, String
            qcStatus, IqcGrnScanItemResponseDTO iqcGrnScanItemResponseDTO, String apiType) {
        log.info("[buildIqcInvoiceProductItem] for qcStatus {} and InvoiceRefNum {}", qcStatus, iqcInvoiceProductEntity.getInvoiceRefNumber());
        int sumPassFailQty = iqcInvoiceProductEntity.getQcPassQty() + iqcInvoiceProductEntity.getQcFailQty();
        if ((iqcInvoiceProductEntity.getSamplingQty() >= sumPassFailQty)) {
            if (!apiType.equalsIgnoreCase("delete")) {
                sumPassFailQty = sumPassFailQty + 1;
                if (IQCConstants.IQC_PASS.equalsIgnoreCase(qcStatus)) {
                    if (sumPassFailQty > iqcInvoiceProductEntity.getSamplingQty()) {
                        log.error("[buildIqcInvoiceProductItem]  Sum of pass and fail Qty can't be greater than Sampling quantity for InvoiceRefNum", iqcInvoiceProductEntity.getInvoiceRefNumber());
                        throw new ApplicationException("Sum of pass and fail Qty can't be greater than Sampling quantity for Invoice", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
                    }
                    iqcInvoiceProductEntity.setQcPassQty(iqcInvoiceProductEntity.getQcPassQty() + 1);
                    iqcInvoiceProductEntity.setQcFailQty(iqcInvoiceProductEntity.getQcFailQty());
                } else if (IQCConstants.IQC_FAIL.equalsIgnoreCase(qcStatus)) {
                    iqcInvoiceProductEntity.setQcFailQty(iqcInvoiceProductEntity.getQcFailQty() + 1);
                    iqcInvoiceProductEntity.setQcPassQty(iqcInvoiceProductEntity.getQcPassQty() - 1);
                }

            } else if (apiType.equalsIgnoreCase("delete")) {
                if (IQCConstants.IQC_PASS.equalsIgnoreCase(qcStatus)) {
                    iqcInvoiceProductEntity.setQcPassQty(iqcInvoiceProductEntity.getQcPassQty() - 1);
                    iqcInvoiceProductEntity.setQcFailQty(iqcInvoiceProductEntity.getQcFailQty());
                } else if (IQCConstants.IQC_FAIL.equalsIgnoreCase(qcStatus)) {
                    iqcInvoiceProductEntity.setQcFailQty(iqcInvoiceProductEntity.getQcFailQty() - 1);
                    iqcInvoiceProductEntity.setQcPassQty(iqcInvoiceProductEntity.getQcPassQty());
                }
            }
            iqcInvoiceProductEntity.setUpdatedBy(MDC.get("USER_ID"));
            iqcInvoiceProductEntity.setUpdatedAt(new Date());
            iqcInvoiceProductEntityService.saveOrUpdate(iqcInvoiceProductEntity);
        } else {
            log.error("[buildIqcInvoiceProductItem] Sum of pass and fail quantity can't be greater than the Sampling quantity total for invoiceRefNo {}", iqcInvoiceProductEntity.getInvoiceRefNumber());
            throw new ApplicationException("Sum of pass and fail quantity can't be greater than the Sampling quantity total for Invoice", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    private IqcGrnScanItemResponseDTO buildIqcGrnItem(IqcGrnProductEntity iqcGrnProductEntity, String qcStatus,
                                                      IqcGrnScanItemResponseDTO iqcGrnScanItemResponseDTO,
                                                      String apiType, List<IqcGrnProductEntity> totalIqcGrnProductEntityList) {
        log.info("[buildIqcGrnItem] Going to update qcStatus {} for boxCode{}" + qcStatus, iqcGrnProductEntity.getBoxCode());
        int sumPassFailQty = iqcGrnProductEntity.getQcPassQty() + iqcGrnProductEntity.getQcFailQty();
        int totalQCPass = 0;
        int totalQCFail = 0;
        int totalSamplingQty = 0;

        for (IqcGrnProductEntity iqcGrnProduct : totalIqcGrnProductEntityList) {
            totalQCPass = totalQCPass + iqcGrnProduct.getQcPassQty();
            totalQCFail = totalQCFail + iqcGrnProduct.getQcFailQty();
            totalSamplingQty = totalSamplingQty + iqcGrnProduct.getSamplingQty();
        }

        if ((iqcGrnProductEntity.getSamplingQty() >= sumPassFailQty)) {
            if (!apiType.equalsIgnoreCase("delete")) {
                int samplingQty = iqcGrnProductEntity.getSamplingQty();
                sumPassFailQty = sumPassFailQty + 1;
                if (IQCConstants.IQC_PASS.equalsIgnoreCase(qcStatus)) {
                    if (sumPassFailQty > samplingQty) {
                        log.error("[buildIqcGrnItem]  Sum of pass and fail Qty can't be greater than Sampling quantity for boxCode {} grnCode {}", iqcGrnProductEntity.getBoxCode(), iqcGrnProductEntity.getGrnCode());
                        throw new ApplicationException("Sum of pass and fail Qty can't be greater than Sampling quantity for boxCode", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
                    }
                    iqcGrnProductEntity.setQcPassQty(iqcGrnProductEntity.getQcPassQty() + 1);
                    iqcGrnProductEntity.setQcFailQty(iqcGrnProductEntity.getQcFailQty());
                    totalQCPass++;
                } else if (IQCConstants.IQC_FAIL.equalsIgnoreCase(qcStatus)) {
                    iqcGrnProductEntity.setQcFailQty(iqcGrnProductEntity.getQcFailQty() + 1);
                    iqcGrnProductEntity.setQcPassQty(iqcGrnProductEntity.getQcPassQty() - 1);
                    totalQCPass--;
                    totalQCFail++;
                }

            } else if (apiType.equalsIgnoreCase("delete")) {
                if (IQCConstants.IQC_PASS.equalsIgnoreCase(qcStatus)) {
                    iqcGrnProductEntity.setQcPassQty(iqcGrnProductEntity.getQcPassQty() - 1);
                    iqcGrnProductEntity.setQcFailQty(iqcGrnProductEntity.getQcFailQty());
                    totalQCPass--;
                } else if (IQCConstants.IQC_FAIL.equalsIgnoreCase(qcStatus)) {
                    iqcGrnProductEntity.setQcFailQty(iqcGrnProductEntity.getQcFailQty() - 1);
                    iqcGrnProductEntity.setQcPassQty(iqcGrnProductEntity.getQcPassQty());
                    totalQCFail--;
                }
            }
            if (iqcGrnProductEntity.getQcPassQty() >= 0 || iqcGrnProductEntity.getQcFailQty() >= 0) {
                iqcGrnProductEntity.setUpdatedBy(MDC.get("USER_ID"));
                iqcGrnProductEntity.setUpdatedAt(new Date());
                iqcGrnProductEntityService.saveOrUpdate(iqcGrnProductEntity);
                BeanUtils.copyProperties(iqcGrnProductEntity, iqcGrnScanItemResponseDTO);
                log.info("IQC qcStatus updated for barcode: {}", iqcGrnProductEntity.getGrnCode());
            } else {
                log.error("Wrong value for pass quantity{} or fail quantity{}", iqcGrnProductEntity.getQcPassQty(), iqcGrnProductEntity.getQcFailQty() >= 0);
                throw new ApplicationException("Wrong value for pass quantity or fail quantity", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        } else {
            log.error("Sum of pass and fail quantity can't be greater than the Sampling quantity total for boxCode{}", iqcGrnProductEntity.getBoxCode());
            throw new ApplicationException("Sum of pass and fail quantity can't be greater than the Sampling quantity total for boxCode", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        iqcGrnScanItemResponseDTO.setTotalGRNQcPassQty(totalQCPass);
        iqcGrnScanItemResponseDTO.setTotalGRNQcFailQty(totalQCFail);
        iqcGrnScanItemResponseDTO.setTotalSamplingQty(totalSamplingQty);
        return iqcGrnScanItemResponseDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Logging
    public IqcGrnProductEntity completeIqc(String boxCode, String grnCode, String pid) {
        log.info("[completeIqc] for grnCode {} and boxCode{}", grnCode, boxCode);
        List<GrnItemEntity> grnItems;
        try {
            IqcGrnProductEntity iQCGRNProduct = iQCItemHDAO.getIqcProductGrnDetails(boxCode, grnCode, pid);//pid
            if (iQCGRNProduct == null) {
                log.error("[completeIqc] Received null for IQC Complete boxCode{},pid{} and grnCode{} from grnItems", boxCode, pid, grnCode);
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, " No record found for boxCode: " + boxCode);
            }

            validateIQCTotalCount(iQCGRNProduct);

            if (IqcGrnProductStatusEnum.IQC_DONE.equals(iQCGRNProduct.getStatus())) {
                log.error("[completeIqc] IQC for given boxCode already Done - boxCode{},pid{} and grnCode{} from grnItems", boxCode, pid, grnCode);
                throw new ApplicationException("IQC for given boxCode already Done", GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            }
            if (IqcGrnProductStatusEnum.IQC_COMPLETE.equals(iQCGRNProduct.getStatus())) {
                log.error("[completeIqc] IQC for given boxCode already completed - boxCode{},pid{} and grnCode{} from grnItems", boxCode, pid, grnCode);
                throw new ApplicationException("IQC for given boxCode already completed", GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
            }
            if (boxCode.equalsIgnoreCase("null")) {
                log.info("[completeIqc] Received null for IQC Complete boxCode{} and grnCode{} from grnItems", boxCode, grnCode);
                grnItems = grnItemEntityService.findByGrnCodeAndPidAndQcPassBoxBarcodeIsNull(grnCode, pid);
            } else {
                log.info("[completeIqc] Received not null for IQC Complete boxCode{} and grnCode{} from grnItems", boxCode, grnCode);
                grnItems = grnItemEntityService.findByGrnCodeAndQcPassBoxBarcodeAndPid(grnCode, boxCode, pid);
            }
            //update all remaining barcode qc_status to pass in grn-items whose qc_status is null
            updateImsAndRemainingItemStatus(grnItems, grnCode);

            iQCGRNProduct.setStatus(IqcGrnProductStatusEnum.IQC_DONE);
            iQCGRNProduct.setImsSyncStatusDate(new Date());
            iQCGRNProduct.setUpdatedBy(MDC.get("USER_ID"));
            iQCGRNProduct.setUpdatedAt(new Date());
            iqcGrnProductEntityService.saveOrUpdate(iQCGRNProduct);
            log.info("[completeIqc] for grnCode {}, iqcGrnProductEntity {}. Trigger Mark IQC complete", grnCode, iQCGRNProduct);
            markGrnIqcComplete(grnCode, boxCode, pid);
            log.info("[completeIqc] Triggered IQC complete for grnCode {}, iqcGrnProductEntity {}",
                    grnCode, iQCGRNProduct);
            return iQCGRNProduct;
        } catch (Exception ex) {
            log.error("[completeIqc] Completing IQC failed for boxCode{},pid{} and grnCode{} from grnItems", boxCode, pid, grnCode);
            throw new ApplicationException("Error: Completing IQC failed  " + ex.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }

    private void validateIQCTotalCount(IqcGrnProductEntity iQCGRNProduct) {
        if(iQCGRNProduct.getSamplingQty() != iQCGRNProduct.getQcPassQty() + iQCGRNProduct.getQcFailQty()){
            log.error("[validateIQCTotalCount] Please complete the scanning for all the barcodes before completing IQC" +
                            " - grnCode {}, boxCode {} and pid {}",
                    iQCGRNProduct.getGrnCode(), iQCGRNProduct.getBoxCode(), iQCGRNProduct.getPid());
            throw new ApplicationException("Please complete the scanning for all the barcodes before completing IQC", GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
        }
    }

    private void updateImsAndRemainingItemStatus(List<GrnItemEntity> grnItems, String grnCode) throws Exception {
        if (!grnItems.isEmpty()) {
            List<GrnItemEntity> grnItemsQcStatusList = grnItems.stream().filter(p -> StringUtils.isEmpty(p.getQcStatus())).collect(Collectors.toList());
            if (!grnItemsQcStatusList.isEmpty()) {
                log.info("[completeIqc][updateImsAndRemainingItemStatus] update all barcode qc_status to pass in grn-items whose qc_status is null, for grnCode {}", grnCode);
                grnItemsQcStatusList.stream().forEach(item -> {
                    item.setQcStatus("pass");
                    item.setUpdatedBy(MDC.get("USER_ID"));
                    item.setUpdatedAt(new Date());
                });
                grnItemEntityService.saveOrUpdateAll(grnItemsQcStatusList);
                log.info("[completeIqc] Status update for all barcode in grn-items successful, for grnCode {}", grnCode);
            }

            updateImsItemStatus(grnCode, grnItems);
            for (GrnItemEntity grnItemEntity : grnItems) {
                if (IQCConstants.IQC_FAIL.equalsIgnoreCase(grnItemEntity.getQcStatus())) {
                    log.info("[completeIqc][updateImsAndRemainingItemStatus] Deleteing barcode {}", grnItemEntity.getBarcode());
                    deleteItem(grnItemEntity);
                    log.info("[completeIqc][updateImsAndRemainingItemStatus] Successfully deleted barcode {}", grnItemEntity.getBarcode());
                }
            }
        }
        log.info("[completeIqc][updateImsAndRemainingItemStatus] Mark iqc grn product as iqc done {}", grnCode);
    }

    @Logging
    public boolean deleteItem(GrnItemEntity grnItem) throws Exception {
        log.info("[deleteItem] Deleting barcode from existing putaway for {}, barcode {}, status {}",
                grnItem.getGrnCode(), grnItem.getBarcode(), grnItem.getQcStatus());
        MDC.put("showPutaway", "true");
        String qcStatus = grnItem.getQcStatus();
        grnItem.setQcStatus("pass");
        boolean response = putawayService.dicardItemInPutaway(grnItem);
        log.info("[deleteItem] Response Received after deleting putaway for boxCode{} and grnCode {} from grnItems response {}",
                grnItem.getQcPassBoxBarcode(), grnItem.getGrnCode(), response);
        grnItem.setQcStatus(qcStatus);
        log.info("[deleteItem] Response {}, grnItem {}", response, grnItem);
        if (response) {
            createAndUpdatePutAway(GRNItemServiceUtils.buildGrnItem(grnItem));
            log.info("[deleteItem] PutAway created And updated Successfully");
        }
        return response;
    }

    public void createAndUpdatePutAway(GRNItem grnItem) throws Exception {
        try {
            log.info("[createAndUpdatePutAway] for barcode {}, boxCode {} and grnCode{}",
                    grnItem.getBarcode(), grnItem.getQcPassBoxBarcode(), grnItem.getGrnCode());
            GRNItemDTO grnItemDTO = new GRNItemDTO();
            //buildGrnDto(grnItem,grnItemDTO);
            BeanUtils.copyProperties(grnItem, grnItemDTO);
            grnItemDTO.setFacilityCode(grnItem.getFacility());
            grnItemDTO.setCreatedBy(MDC.get("USER_ID"));
            grnItemDTO.setUpdatedBy(MDC.get("USER_ID"));
            ScanItemResponseDTO response = new ScanItemResponseDTO();
            List<CreatePutawayResponse> putawayResponse = putawayService.createPutAway(grnItemDTO, response, "fail");
            log.info("[createAndUpdatePutAway] boxCode {} and grnCode {}, putawayResponse {}",
                    grnItem.getQcPassBoxBarcode(), grnItem.getGrnCode(), putawayResponse);
            String putawayCode = putawayResponse.get(0).getBarcodeInfo().get(0).getNewPutawayId();
            log.info("[createAndUpdatePutAway] received putawayCode {} for boxCode {} and grnCode{}",
                    putawayCode, grnItem.getQcPassBoxBarcode(), grnItem.getGrnCode());
            if (!StringUtils.isEmpty(putawayCode)) {
                iQCItemHDAO.updatePutawayCodeInGrnItem(putawayResponse.get(0), grnItem.getBarcode());
            }
        } catch (Exception ex) {
            throw ex;
        }
    }


    @Override
    public void updateImsItemStatus(String grnCode, List<GrnItemEntity> grnItems) throws Exception {
        try {
            List<GrnItemEntity> imsBadBarcodeStatusUpdatedList = new ArrayList<>();
            List<GrnItemEntity> imsGoodBarcodeStatusUpdatedList = new ArrayList<>();

            log.info("[updateImsItemStatus] grnCode {}", grnCode);
            List<List<GrnItemEntity>> grnBarcodesList = Lists.partition(grnItems, grnConfig.getImsBatchSize());
            for (List<GrnItemEntity> grnItemList : grnBarcodesList) {
                for(GrnItemEntity grnItem: grnItemList){
                    if (GRNConstants.GRN_ITEM_PASS.equals(grnItem.getQcStatus())) {
                        imsGoodBarcodeStatusUpdatedList.add(grnItem);
                    } else if (GRNConstants.GRN_ITEM_FAIL.equals(grnItem.getQcStatus())) {
                        imsBadBarcodeStatusUpdatedList.add(grnItem);
                    } else {
                        log.error("[updateImsItemStatus] No Good or Bad GRN available to update IMS status for GrnCode {}", grnCode);
                        throw new ApplicationException("No Good or Bad GRN available to update IMS status, status received " +
                                grnItem.getGrnCode(), GRNExceptionStatus.GRN_INTERNAL_EXCEPTION);
                    }
                }
                if (!imsGoodBarcodeStatusUpdatedList.isEmpty()){
                    log.info("[updateImsItemStatus] Updating pass barcode status as success in ims for grncode {}", grnCode);
                    UpdateStockInwardRequest updateStockInwardRequest = imsService.frameUpdateStockRequestForIqcGRNItemV2(imsBadBarcodeStatusUpdatedList,
                            ActionStatus.IMS_UPDATE_GRN_ITEMS, IQCConstants.IQC, IQCConstants.GRN_IQC_PASS);
                    imsService.performStockInward(updateStockInwardRequest);
                }

                if (!imsBadBarcodeStatusUpdatedList.isEmpty()){
                    log.info("[updateImsItemStatus] Updating fail barcode status as success in ims for grncode {}", grnCode);
                    UpdateStockInwardRequest updateStockInwardRequest = imsService.frameUpdateStockRequestForIqcGRNItemV2(imsBadBarcodeStatusUpdatedList,
                            ActionStatus.IMS_UPDATE_GRN_ITEMS, IQCConstants.IQC, IQCConstants.GRN_IQC_FAIL);
                    imsService.performStockInward(updateStockInwardRequest);
                }
            }
            log.info("[updateImsItemStatus] Successfully barcode status updated in ims for grncode {}", grnCode);
        } catch (Exception ex) {
            log.error("[updateImsItemStatus] {}, Error: IQC update stock request failed {}", grnCode, ex.getMessage());
            throw new ApplicationException("Exception : IMS update stock request failed " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ScanBarcodeResponseModel scanBoxOrBarCode(String grnCode, String pid, String barcode, String facility) throws
            Exception {
        log.info("[scanBoxOrBarCode] grnCode {}, pid {}, barcode {}, facility {}",
                grnCode, pid, barcode, facility);
        List<Tuple> grnItemsTupleList = validateNGetGrnItems(grnCode, pid, barcode);
        List<GRNItem> grnItemList = new ArrayList<>();
        List<String> boxCodes = new ArrayList<>();
        ScanBarcodeResponseModel scanBarcodeResponseModel;
        try {
            createGrnItemUsingTuple(grnCode, grnItemsTupleList, grnItemList);
            if (pid == null)
                pid = grnItemList.stream().filter(grnItem -> grnItem.getPid() != null).findFirst().get().getPid();
            gRNItemDAO.updateQcStatusUsingGrnCode(grnCode, "IQC_IN_PROGRESS");
            log.info("[scanBoxOrBarCode] Updated qc status to IQC_IN_PROGRESS using grnCode: {}", grnCode);
            scanBarcodeResponseModel = createScanBarcodeResponseModel(grnItemList, grnCode, barcode, pid);
        } catch (Exception ex) {
            log.error("Exception : {}", ex.getMessage());
            throw new ApplicationException("Exception in [scanBoxOrBarCode]: " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        log.info("Grn Item List in [updateQcStatus]: {}", grnItemList);
        this.updateStatusForBoxCode(grnCode, grnItemList, boxCodes, pid);
        log.info("Updated iqcGrnProduct to IQC_IN_PROGRESS status, Change putaway user grnCode {}, facility {}, user {}",
                grnCode, facility, MDC.get("USER_ID"));
        this.updatePutawayUser(grnCode, facility);
        return scanBarcodeResponseModel;
    }

    private void createGrnItemUsingTuple(String grnCode, List<Tuple> grnItemsTupleList, List<GRNItem> grnItemList) {
        for (Tuple grnItemTuple : grnItemsTupleList) {
            grnItemList.add(createGRNItem(grnItemTuple, grnCode));
        }
    }

    private void updateStatusForBoxCode(String grnCode, List<GRNItem> grnItemList, List<String> boxCodes, String pid) {
        List<IqcGrnProductEntity> iQCGRNProductsList = iQCItemHDAO.findByGrnCode(grnCode);
        grnItemList.stream().filter(grnItem -> grnItem.getQcPassBoxBarcode() != null)
                .forEach(grnItem -> boxCodes.add(grnItem.getQcPassBoxBarcode()));
        for (IqcGrnProductEntity iQCGRNProduct : iQCGRNProductsList) {
            if ((!boxCodes.isEmpty() && boxCodes.contains(iQCGRNProduct.getBoxCode()))
                    || (boxCodes.isEmpty() && pid != null && pid.equalsIgnoreCase(iQCGRNProduct.getPid()))) {
                iQCGRNProduct.setStatus(IqcGrnProductStatusEnum.IQC_IN_PROGRESS);
                iQCGRNProduct.setUpdatedBy(MDC.get("USER_ID"));
                iQCGRNProduct.setUpdatedAt(new Date());
                iqcGrnProductEntityService.saveOrUpdate(iQCGRNProduct);
            }
        }
    }

    private void updatePutawayUser(String grnCode, String facility) throws Exception {
        try {
            log.info("[updatePutawayUser] grnCode {}, facility {}, userId {}", grnCode, facility, MDC.get("USER_ID"));
            Map<String, Object> payload = new HashMap<>();
            payload.put("userId", MDC.get("USER_ID"));
            payload.put("facilityCode", facility);
            payload.put("putawayType", "PUTAWAY_GRN_ITEM");
            payload.put("grnCode", grnCode);
            String url = putawayBaseUrl + "/putaway/update/user";
            log.info("Calling putaway service: url: {}, payload: {}", url, payload);
            RestUtils.putData(url, null, payload, null);
        } catch (Exception ex) {
            log.error("Exception for user update from putaway : " + ex.getMessage());
            throw new Exception("Exception for user update from putaway : " + ex.getMessage());
        }
    }

    private List<Tuple> validateNGetGrnItems(String grnCode, String pid, String barcode) {
        log.info("[validateNGetGrnItems] grnCode {}, pid {}, barcode {}", grnCode, pid, barcode);
        GrnMasterEntity grnMasterEntity = grnMasterEntityService.findByGrnCode(grnCode);
        log.info("[validateNGetGrnItems] grnMasterEntity in [scanBoxOrBarCode] {}", grnMasterEntity);
        List<Tuple> grnItemsTupleList = new ArrayList<>();
        if (GRNConstants.IQC_COMPLETE.equalsIgnoreCase(grnMasterEntity.getGrnStatus())) {
            throw new ApplicationException("Cannot scan box or barcode IQC_COMPLETED",
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        } else {
            grnItemsTupleList = gRNItemDAO.getGrnItemUsingBoxOrBarcode(barcode, grnCode, pid);
            log.info("[validateNGetGrnItems] grnItemsTupleList {}", grnItemsTupleList);

            if (grnItemsTupleList == null || grnItemsTupleList.isEmpty())
                throw new ApplicationException("No data found or Invalid barcode", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);

            IqcGrnProductEntity iqcGrnProductEntity;
            if (grnItemsTupleList.get(0).get("qc_pass_box_barcode") != null)
                iqcGrnProductEntity = iqcGrnProductEntityService.findByGrnCodeAndBoxCode(grnCode,
                        grnItemsTupleList.get(0).get("qc_pass_box_barcode").toString());
            else
                iqcGrnProductEntity = iqcGrnProductEntityService.findByPidAndGrnCode(
                        grnItemsTupleList.get(0).get("pid").toString(), grnCode);

            log.info("[validateNGetGrnItems] IqcGrnProductEntity in [scanBoxOrBarCode] : {}", iqcGrnProductEntity);
            if(iqcGrnProductEntity == null){
                throw new ApplicationException("Details does not exist in iqc grn product", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }

            if (GRNConstants.IQC_DONE.equalsIgnoreCase(String.valueOf(iqcGrnProductEntity.getStatus())) ||
                    GRNConstants.IQC_COMPLETE.equalsIgnoreCase(String.valueOf(iqcGrnProductEntity.getStatus()))) {
                throw new ApplicationException("Cannot scan box or barcode, IQC_DONE OR IQC_COMPLETED ",
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
        }

        log.info("[validateNGetGrnItems] grnItemsTupleList : {}", grnItemsTupleList);
        return grnItemsTupleList;
    }

    private ScanBarcodeResponseModel createScanBarcodeResponseModel(List<GRNItem> grnItemList, String grnCode,
                                                                    String barcode, String pid) {
        log.info("Entered [createScanBarcodeResponseModel]");
        List<IqcGrnProductEntity> result = iqcGrnProductEntityService.findByGrnCode(grnCode);
        log.info("Result of getIqcGrnDetails : {}", result);
        ScanBarcodeResponseModel scanBarcodeResponseModel = new ScanBarcodeResponseModel();
        scanBarcodeResponseModel.setGrnCode(grnCode);
        String vendor = grnItemList.stream().filter(grnItem -> grnItem.getVendorCode() != null).findFirst().get().getVendorCode();
        Optional<GRNItem> boxCodeGrnItem = grnItemList.stream().filter(grnItem -> grnItem.getQcPassBoxBarcode() != null).findFirst();
        scanBarcodeResponseModel.setPid(pid);
        scanBarcodeResponseModel.setVendorId(vendor);
        List<String> passBarcodes = new ArrayList<>();
        List<String> failBarcodes = new ArrayList<>();
        scanBarcodeResponseModel.setQcType("IQC");
        Integer failQty = 0;
        Integer passQty = 0;
        Integer samplingQty = 0;
        Integer totalQty = 0;
        boolean updatePIDLevelQuantity = true;
        for (IqcGrnProductEntity iqcGrnProduct : result) {
            if ((barcode != null && barcode.equalsIgnoreCase(iqcGrnProduct.getBoxCode())) ||
                    (updatePIDLevelQuantity && pid != null && iqcGrnProduct.getPid().equalsIgnoreCase(pid))) {
                setPidLevelQuantities(scanBarcodeResponseModel, iqcGrnProduct);
                updatePIDLevelQuantity = false;
            }
            failQty = failQty + iqcGrnProduct.getQcFailQty();
            passQty = passQty + iqcGrnProduct.getQcPassQty();
            samplingQty = samplingQty + iqcGrnProduct.getSamplingQty();
            totalQty = totalQty + iqcGrnProduct.getTotalQty();
            scanBarcodeResponseModel.setGrnDate(Timestamp.valueOf(iqcGrnProduct.getCreatedAt().toString()));
        }
        scanBarcodeResponseModel.setBoxCode(boxCodeGrnItem.isPresent() ? boxCodeGrnItem.get().getQcPassBoxBarcode() : null);
        scanBarcodeResponseModel.setSamplingQty(samplingQty);
        scanBarcodeResponseModel.setTotalQty(totalQty);
        scanBarcodeResponseModel.setQcPassQty(passQty);
        scanBarcodeResponseModel.setQcFailQty(failQty);
        grnItemList.stream().forEach(grnItem -> {
            if ("pass".equals(grnItem.getQcStatus()))
                passBarcodes.add(grnItem.getBarcode());
            else if ("fail".equals(grnItem.getQcStatus()))
                failBarcodes.add(grnItem.getBarcode());
        });
        Map<String, List<String>> barcodesMap = new HashMap<>();
        barcodesMap.put("qc_pass", passBarcodes);
        barcodesMap.put("qc_fail", failBarcodes);
        scanBarcodeResponseModel.setBarcodesMap(barcodesMap);
        GrnMasterEntity grnMasterEntity = grnMasterEntityService.findByGrnCode(grnCode);
        log.info("grnMasterEntity in [createScanBarcodeResponseModel]", grnMasterEntity);
        if (GRNConstants.IQC_COMPLETE.equalsIgnoreCase(grnMasterEntity.getGrnStatus()))
            scanBarcodeResponseModel.setGrnClosure(true);
        log.info("scanBarcodeResponseModel in [createScanBarcodeResponseModel]", scanBarcodeResponseModel);
        return scanBarcodeResponseModel;

    }

    private void setPidLevelQuantities(ScanBarcodeResponseModel scanBarcodeResponseModel, IqcGrnProductEntity
            iqcGrnProduct) {
        scanBarcodeResponseModel.setPidLevelTotalQty(iqcGrnProduct.getTotalQty());
        scanBarcodeResponseModel.setPidLevelSamplingQty(iqcGrnProduct.getSamplingQty());
        scanBarcodeResponseModel.setPidLevelQcPassQty(iqcGrnProduct.getQcPassQty());
        scanBarcodeResponseModel.setPidLevelQcFailQty(iqcGrnProduct.getQcFailQty());
    }

    private GRNItem createGRNItem(Tuple tuple, String grnCode) {
        GRNItem grnItem = new GRNItem();
        grnItem.setGrnCode(grnCode);
        grnItem.setPid(tuple.get("pid").toString());
        grnItem.setQcStatus(tuple.get("qc_status") == null ? null : tuple.get("qc_status").toString());
        grnItem.setInvoiceRefNum(tuple.get("invoice_ref_num") == null ? null : tuple.get("invoice_ref_num").toString());
        grnItem.setPoId(tuple.get("po_id") == null ? null : tuple.get("po_id").toString());
        grnItem.setStatus(tuple.get("status").toString());
        grnItem.setVendorCode(tuple.get("vendor_code").toString());
        grnItem.setBarcode(tuple.get("barcode").toString());
        grnItem.setQcPassBoxBarcode(tuple.get("qc_pass_box_barcode") == null ? null : tuple.get("qc_pass_box_barcode").toString());
        grnItem.setFacility(tuple.get("facility").toString());
        grnItem.setCreatedAt(Timestamp.valueOf(tuple.get("created_at").toString()));
        grnItem.setPutawayCode(tuple.get("putaway_code") == null ? null : Integer.valueOf(tuple.get("putaway_code").toString()));
        log.info("Grn Item in [createGRNItem] : {}", grnItem);
        return grnItem;
    }

    @Override
    @Async
    public String markGrnIqcComplete(String grnCode, String boxCode, String pid) {
        try {
            log.info("[markGrnIqcComplete] grnCode {}, boxCode {}, pid {}", grnCode, boxCode, pid);
            List<GrnItemEntity> grnItemEntities = new ArrayList<>();
            if (boxCode != null) {
                grnItemEntities = grnItemEntityService.findByGrnCodeAndQcPassBoxBarcodeAndImsSyncStatus
                        (grnCode, boxCode, GrnImsSyncStatusEnum.GRN_DONE.getStatus());
            } else {
                grnItemEntities = grnItemEntityService.findByGrnCodeAndQcPassBoxBarcodeIsNullAndImsSyncStatus
                        (grnCode, GrnImsSyncStatusEnum.GRN_DONE.getStatus());
            }
            List<List<GrnItemEntity>> grnItemPartitionList = ListUtils.partition(grnItemEntities, grnConfig.getFetchBarcodeDetailSize());
            boolean imsErrorOccurred = false, allBarcodesNotIqcComplete = false;
            int barcodeCountLoop = 1;

            for (List<GrnItemEntity> grnItemPartitionEntities : grnItemPartitionList) {
                try {
                    log.info("[markGrnIqcComplete] Call ims grnCode {}, boxCode {}, barcodeCountLoop {}",
                            grnCode, boxCode, barcodeCountLoop);
                    FetchStockDetailsRequest fetchStockDetailsRequest =
                            createFetchStockDetailsRequest(grnItemPartitionEntities);
                    List<BarcodeItemRequest> barcodeItemRequestList = new ArrayList<>();
                    log.info("[markGrnIqcComplete] Call ims grnCode {}, boxCode {}, fetchStockDetailsRequest {}",
                            grnCode, boxCode, fetchStockDetailsRequest);
                    FetchStockDetailsResponse itemResponse = imsService.fetchStockDetails(fetchStockDetailsRequest);
                    barcodeItemRequestList = itemResponse.getBarcodeItemRequestList();
                    log.info("[markGrnIqcComplete] Successfully fetched barcode details from ims " +
                            "grnCode {}, boxCode {}, barcodeCountLoop {}", grnCode, boxCode, barcodeCountLoop);
                    List<String> isIQCCompletedBarcode = new ArrayList<>();
                    for (BarcodeItemRequest barcodeItemRequest : barcodeItemRequestList) {
                        if ("IQC_COMPLETE".equalsIgnoreCase(barcodeItemRequest.getStatus().toString())) {
                            isIQCCompletedBarcode.add(barcodeItemRequest.getBarcode());
                        }
                    }

                    log.info("[markGrnIqcComplete] Check status for all the iqc complete barcodes " +
                            "grnCode {}, boxCode {}, barcodeCountLoop {}", grnCode, boxCode, barcodeCountLoop);
                    Integer totalGrnItem = 0, iqcCompleteGrnItem = 0;
                    for (GrnItemEntity item : grnItemPartitionEntities) {
                        log.info("[markGrnIqcComplete] Update status for barcode {}", item.getBarcode());
                        if (isIQCCompletedBarcode.contains(item.getBarcode())) {
                            item.setImsSyncStatus(GrnImsSyncStatusEnum.IQC_COMPLETE);
                            iqcCompleteGrnItem++;
                        }
                        totalGrnItem++;
                    }

                    log.info("[markGrnIqcComplete] Check if all barcodes are iqc complete or not " +
                            "grnCode {}, boxCode {}, barcodeCountLoop {}", grnCode, boxCode, barcodeCountLoop);
                    if (!iqcCompleteGrnItem.equals(totalGrnItem)) {
                        allBarcodesNotIqcComplete = true;
                    }

                    grnItemEntityService.saveOrUpdateAll(grnItemPartitionEntities);
                } catch (Exception e) {
                    imsErrorOccurred = true;
                    log.error("[markGrnIqcComplete] Error in fetching barcode details from ims {}", e.getMessage());
                }
                barcodeCountLoop++;
            }

            IqcGrnProductEntity iqcGrnProductEntity = new IqcGrnProductEntity();
            if(boxCode == null)
                iqcGrnProductEntity = iqcGrnProductEntityService.findByPidAndGrnCode(pid, grnCode);
            else
                iqcGrnProductEntity = iqcGrnProductEntityService.findByGrnCodeAndBoxCode(grnCode, boxCode);

            log.info("[markGrnIqcComplete] grnCode {}, boxCode {}, imsErrorOccurred {}, allBarcodesNotIqcComplete {}" +
                            ",iqcGrnProductEntity {}",
                    grnCode, boxCode, imsErrorOccurred, allBarcodesNotIqcComplete, iqcGrnProductEntity);
            if (imsErrorOccurred || allBarcodesNotIqcComplete) {
                log.info("[markGrnIqcComplete] All the barcodes are not synced for grnCode {}", grnCode);
                List<GrnItemEntity> unsyncedGrnItemEntities = grnItemEntityService.
                        findByGrnCodeAndQcPassBoxBarcodeAndImsSyncStatus(grnCode,
                                boxCode, GrnImsSyncStatusEnum.GRN_DONE.getStatus());
                updateImsItemStatus(grnCode, unsyncedGrnItemEntities);
                iqcGrnProductEntity.setRetryCount(iqcGrnProductEntity.getRetryCount() + 1);
                iqcGrnProductEntity.setImsSyncStatusDate(new Date());
                iqcGrnProductEntityService.saveOrUpdate(iqcGrnProductEntity);
                log.info("[markGrnIqcComplete] All the barcodes are not synced for grnCode {}, PROCESS_PENDING", grnCode);
                return "PROCESS_PENDING";
            } else {
                log.info("[markGrnIqcComplete] All the barcodes are synced for grnCode {}", grnCode);
                iqcGrnProductEntity.setStatus(IqcGrnProductStatusEnum.IQC_COMPLETE);
                iqcGrnProductEntityService.saveOrUpdate(iqcGrnProductEntity);
            }

            log.info("[markGrnIqcComplete] Check if all iqc grn barcode are IQC_COMPLETE {}", grnCode);
            List<IqcGrnProductEntity> grnIqcCompleteProductList = iqcGrnProductEntityService.findByGrnCode(grnCode);
            for (IqcGrnProductEntity iqcGrnProduct : grnIqcCompleteProductList) {
                if (!IqcGrnProductStatusEnum.IQC_COMPLETE.equals(iqcGrnProduct.getStatus())) {
                    log.info("[markGrnIqcComplete] All iqc grn product for {} are not GRN_ALL_BOX_IQC_NOT_COMPLETE", grnCode);
                    return "GRN_ALL_BOX_IQC_NOT_COMPLETE";
                }
            }

            log.info("[markGrnIqcComplete] Changing grn master status for {}", grnCode);
            GrnMasterEntity grnMaster = grnMasterEntityService.findByGrnCode(grnCode);
            grnMaster.setGrnStatus(GRNConstants.IQC_COMPLETE);
            grnMaster.setUpdatedAt(new Date());
            grnMasterEntityService.saveOrUpdate(grnMaster);
            log.info("[markGrnIqcComplete] Grn master status successfully updated for {}, SUCCESS", grnCode);

            return "SUCCESS";
        } catch (Exception e) {
            log.error("[markGrnIqcComplete] Exception grnCode {}, message {}", grnCode, e.getMessage());
            return "ERROR";
        }
    }

    private FetchStockDetailsRequest createFetchStockDetailsRequest
            (List<GrnItemEntity> grnItemPartitionEntities) {
        try {
            log.info("[createFetchStockDetailsResponse]");
            FetchStockDetailsRequest request = new FetchStockDetailsRequest();
            List<BarcodeItemRequest> barcodeItemRequests = new ArrayList<>();

            for (GrnItemEntity grnItem : grnItemPartitionEntities) {
                BarcodeItemRequest barcodeItemRequest = new BarcodeItemRequest();
                barcodeItemRequest.setBarcode(grnItem.getBarcode());
                barcodeItemRequest.setEnabled(true);
                barcodeItemRequests.add(barcodeItemRequest);
            }
            request.setBarcodeItemRequests(barcodeItemRequests);
            log.info("[createFetchStockDetailsResponse] request {}", request);
            return request;
        } catch (Exception ex) {
            log.error("[createFetchStockDetailsRequest] {}", ex.getMessage());
            throw new ApplicationException("Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IqcGrnScanItemResponseDTO deleteIqcScanItem(String barCode, String grnCode) {
        log.info("Deleting barCode {} for grnCode {}", barCode, grnCode);
        GrnItemEntity grnItemEntity = null;
        try {
            if (StringUtils.isEmpty(barCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Item barcode can not be null or empty");
            if (StringUtils.isEmpty(grnCode))
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Item grn code can not be null or empty");
            grnItemEntity = iQCItemHDAO.getGrnItemDetails(barCode, grnCode);
            String apiType = "delete";
            IqcGrnScanItemResponseDTO iqcGrnScanItemResponseDTO = null;
            if (!StringUtils.isEmpty(grnItemEntity.getQcStatus())) {
                iqcGrnScanItemResponseDTO = new IqcGrnScanItemResponseDTO();
                IqcInvoiceProductEntity iqcInvoiceProductEntity = iQCItemHDAO.findByInvoiceRefNumber(Integer.parseInt(grnItemEntity.getInvoiceRefNum()), Integer.parseInt(grnItemEntity.getPid()));
                log.info("Received iqcInvoiceProductEntity {}" + iqcInvoiceProductEntity);
                buildIqcInvoiceProductItem(iqcInvoiceProductEntity, grnItemEntity.getQcStatus(), iqcGrnScanItemResponseDTO, apiType);
                iqcGrnScanItemResponseDTO.setBarCode(grnItemEntity.getBarcode());
                IqcGrnProductEntity iqcGrnProductEntity = iQCItemHDAO.getIqcGrnDetails(grnItemEntity.getPid(), grnCode, grnItemEntity.getQcPassBoxBarcode());
                List<IqcGrnProductEntity> totalIqcGrnProductEntityList = iQCItemHDAO.findByGrnCode(grnCode);
                log.info("Received iqcGrnProductEntity {}" + iqcGrnProductEntity);
                iqcGrnScanItemResponseDTO = buildIqcGrnItem(iqcGrnProductEntity, grnItemEntity.getQcStatus(),
                        iqcGrnScanItemResponseDTO, apiType, totalIqcGrnProductEntityList);
                //nullify grn-items
                grnItemEntity.setQcStatus("");
                grnItemEntity.setQcReason("");
                grnItemEntityService.saveOrUpdate(grnItemEntity);
            } else {
                log.info("No barcode and grnCode combination available to delete for grnCode{} and boxCode{}", grnItemEntity.getGrnCode(), grnItemEntity.getQcPassBoxBarcode());
                throw new ApplicationException("No barcode and grnCode combination available to delete", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
            }
            return iqcGrnScanItemResponseDTO;
        } catch (Exception ex) {
            log.error("Exception: Deleting barcode PO: {} ,PId: {}, Grn Code: {} ,Barcode: {}, Error: {}", grnItemEntity.getPoId(), grnItemEntity.getPid(),
                    grnItemEntity.getGrnCode(), barCode, ex.getMessage());
            throw new ApplicationException("Exception : " + ex.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public Map<String, Object> getIqcBarcodesDetails(List<IqcGrnProductEntity> iqcGrnProductEntityList,
                                                     String grnCode) {
        log.info("Entered [getIqcBarcodesDetails]");
        Map<String, Object> iqcSummaryMap = new HashMap<>();
        iqcSummaryMap.put("items", iqcGrnProductEntityList);
        iqcSummaryMap.put("grnCode", grnCode);
        GrnMasterEntity grnMaster = grnMasterEntityService.findByGrnCode(grnCode);
        iqcSummaryMap.put("grnDate", grnMaster.getCreatedAt());
        String vendorCode = gRNItemDAO.getVendorIdFromGrnCode(grnCode);
        iqcSummaryMap.put("vendorId", vendorCode);
        iqcSummaryMap.put("qcType", "IQC");
        AtomicInteger totalCount = new AtomicInteger(0);
        AtomicInteger samplingCount = new AtomicInteger(0);
        AtomicInteger qcPassCount = new AtomicInteger(0);
        AtomicInteger qcFailCount = new AtomicInteger(0);
        AtomicBoolean iqcDone = new AtomicBoolean(true);
        this.getCountsFromIqcGrn(iqcGrnProductEntityList, totalCount, samplingCount, qcPassCount, qcFailCount, iqcDone);
        iqcSummaryMap.put("totalCount", totalCount);
        iqcSummaryMap.put("samplingCount", samplingCount);
        iqcSummaryMap.put("qcPassCount", qcPassCount);
        iqcSummaryMap.put("qcFailCount", qcFailCount);
        iqcSummaryMap.put("pendingQc", samplingCount.intValue() - qcPassCount.intValue() - qcFailCount.intValue());
        if (GRNConstants.IQC_COMPLETE.equalsIgnoreCase(grnMaster.getGrnStatus()))
            iqcSummaryMap.put("grnClosure", true);
        else
            iqcSummaryMap.put("grnClosure", false);
        iqcSummaryMap.put("iqcDone", iqcDone.get());
        return iqcSummaryMap;
    }

    private void getCountsFromIqcGrn(List<IqcGrnProductEntity> iqcGrnProductEntityList, AtomicInteger
            totalCount, AtomicInteger samplingCount, AtomicInteger qcPassCount, AtomicInteger qcFailCount, AtomicBoolean
                                             iqcDone) {
        iqcGrnProductEntityList.stream().forEach(iqcGrnProduct -> {
            if (!GRNConstants.IQC_DONE.equalsIgnoreCase(String.valueOf(iqcGrnProduct.getStatus())))
                iqcDone.set(false);
            totalCount.addAndGet(iqcGrnProduct.getTotalQty());
            samplingCount.addAndGet(iqcGrnProduct.getSamplingQty());
            qcPassCount.addAndGet(iqcGrnProduct.getQcPassQty());
            qcFailCount.addAndGet(iqcGrnProduct.getQcFailQty());
        });
    }

    private GRNItem buildGrnItem(GrnItemEntity grnItemEntity) {
        log.info("buildGrnItem stated for barcode: {}", grnItemEntity.getBarcode());
        GRNItem grnItem = new GRNItem();
        grnItem.setBarcode(grnItemEntity.getBarcode());
        grnItem.setGrnCode(grnItemEntity.getGrnCode());
        grnItem.setPid(grnItemEntity.getPid());
        grnItem.setPoId(grnItemEntity.getPoId());
        grnItem.setVendorCode(grnItemEntity.getVendorCode());
        grnItem.setLotNo(grnItemEntity.getLotNo());
        grnItem.setQcMasterSampling(grnItemEntity.getQcMasterSampling());
        grnItem.setFailureThresholdQcMaster(grnItemEntity.getFailureThresholdQcMaster());
        grnItem.setStatus(grnItemEntity.getStatus());
        grnItem.setFacility(grnItemEntity.getFacility());
        grnItem.setGrnEstimatedQuantity(grnItemEntity.getEstimatedQty());
        grnItem.setInvoiceRefNum(grnItemEntity.getInvoiceRefNum());
        grnItem.setQcPassBoxBarcode(grnItemEntity.getQcPassBoxBarcode());
        grnItem.setQcStatus(grnItemEntity.getQcStatus());
        grnItem.setQcFailCode(grnItemEntity.getQcFailCode());
        grnItem.setQcReason(grnItemEntity.getQcReason());
        if (grnItemEntity.getExpiryDate() != null)
            grnItem.setExpiryDate(new Timestamp(grnItemEntity.getExpiryDate().getTime()));
        grnItem.setCreatedAt(new Timestamp(grnItemEntity.getCreatedAt().getTime()));
        grnItem.setUpdatedAt(new Timestamp(grnItemEntity.getUpdatedAt().getTime()));
        grnItem.setCreatedBy(grnItemEntity.getCreatedBy());
        grnItem.setUpdatedBy(grnItemEntity.getUpdatedBy());
        log.info("buildGrnItem completed for barcode: {}", grnItemEntity.getBarcode());
        return grnItem;
    }
}
