package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dao.QcStatusDAO;
import com.lenskart.nexs.grn.model.GRNItem;
//import com.lenskart.nexs.grn.model.QcConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CacheUpdateService implements GRNConstants {

    @Autowired
    @Qualifier(QualifierConstants.JPA_QC_STATUS_DAO)
    private QcStatusDAO qcStatusDAO;

    @Autowired
    private CacheDAO cacheDAO;

    @Logging
    public void updateCacheState(GRNItem grnItem, String status, String action) {
        log.info("[updateCacheState] Po: {}, PId: {}, Grn code: {}, Barcode: {}, QC status: {}, status: {}, action: {}", grnItem.getPoId(),
                grnItem.getPid(), grnItem.getGrnCode(), grnItem.getBarcode(), grnItem.getQcStatus(), status, action);

        if(grnItem.getQcStatus().equals(QC_FAIL)) {
            long decrementFailCount = qcStatusDAO.decrementFailCount(grnItem.getGrnCode(), grnItem.getPid());
            long decrementAllFailed = qcStatusDAO.decrementAllFailed(grnItem.getGrnCode(), grnItem.getPid());
            log.info("[updateCacheState] Po: {}, PId: {}, QC_FAIL decrementFailCount: {}, decrementAllFailed: {}", grnItem.getPoId(),
                    grnItem.getPid(), decrementFailCount, decrementAllFailed);
        }

        if (action != null && (action.equals(DELETE) || action.equals(SAVE))) {
            long invoiceDecrementItemQtyCount = qcStatusDAO.decrementItemQtyCount(grnItem.getInvoiceRefNum(), grnItem.getPid());
            long poDecrementItemQtyCount = qcStatusDAO.decrementItemQtyCount(grnItem.getPoId(), grnItem.getPid(), true);
            log.info("[updateCacheState] Po: {}, PId: {}, {} invoiceDecrementItemQtyCount: {}, poDecrementItemQtyCount: {}", grnItem.getPoId(),
                    grnItem.getPid(), action, invoiceDecrementItemQtyCount, poDecrementItemQtyCount);
        }

        if (action != null && action.equals(DELETE_PO_INVOICE_COUNT)) {
            qcStatusDAO.deleteRedisCountKeyForInvoiceAndPO(grnItem.getInvoiceRefNum(), grnItem.getPoId(), grnItem.getPid());
            log.info("[updateCacheState] Po: {}, PId: {}, DELETE_PO_INVOICE_COUNT", grnItem.getPoId(), grnItem.getPid());
        }

        long decrementScanCount = qcStatusDAO.decrementScanCount(grnItem.getGrnCode(), grnItem.getPid());
        long decrementAllScanned = qcStatusDAO.decrementAllScanned(grnItem.getGrnCode(), grnItem.getPid());
        qcStatusDAO.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), status);

        log.info("[updateCacheState] Po: {}, PId: {}, decrementScanCount: {}, decrementAllScanned: {}", grnItem.getPoId(),
                grnItem.getPid(), decrementScanCount, decrementAllScanned);
    }

    @Logging
    public void updateCacheState(GRNItem grnItem, String status) {
        updateCacheState(grnItem, status, SAVE);
//        cacheDAO.setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), oldInvoiceConfig);
//        cacheDAO.setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), oldGrnConfig);
        // release lock after work done on invoice config
    }

    @Logging
    public void updateRecovery(GRNItem grnItem, String status, long totalFailed, long failed) {
        qcStatusDAO.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), status);
//        cacheDAO.setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), oldInvoiceConfig);
        // release lock after work done on invoice config
//        cacheDAO.setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), oldGrnConfig);
        qcStatusDAO.setTotalFailed(grnItem.getGrnCode(), grnItem.getPid(), totalFailed);
        qcStatusDAO.setAllFailed(grnItem.getGrnCode(), grnItem.getPid(), failed);
    }
}
