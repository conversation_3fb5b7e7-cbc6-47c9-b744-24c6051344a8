package com.lenskart.nexs.grn.service.strategy.impl;

import java.util.*;

import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.fms.model.response.FacilityDetailsResponse;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.model.Barcode;
import com.lenskart.nexs.grn.model.BarcodeValidationResponse;
import com.lenskart.nexs.grn.service.IMSService;
import com.lenskart.nexs.grn.util.FacilityDetailsUtils;
import com.lenskart.nexs.ims.request.StockRequestV2;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.lenskart.nexs.grn.util.RetryUtils;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.config.BarcodeConfig;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.BarcodeDAO;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.response.UnicomResponse;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.BarcodeDetail;
import com.lenskart.nexs.grn.service.strategy.BarcodeValidation;
import com.lenskart.nexs.grn.util.BarcodeUtils;

@Service
@Slf4j
public class BarcodeValidationImpl implements BarcodeValidation, GRNConstants {

    @Autowired
    private BarcodeConfig barcodeConfig;

    @Autowired
    @Qualifier(QualifierConstants.JPA_BARCODE_DAO)
    private BarcodeDAO barcodeDAO;

    @Autowired
    private GRNConfig grnConfig;

    @Autowired
    private FacilityDetailsUtils facilityDetailsUtils;

    @Autowired
    private GrnItemEntityServiceImpl grnItemEntityService;

    @Autowired
    private IMSService imsService;

    @Value("#{'${validate.inward.barcode.type.list}'.split(',')}")
    private Set<String> validateInwardBarcodeTypeList;

    @Value("${validate.inward.barcode.default.facility}")
    private String validateInwardBarcodeDefaultFacility;

    @Value("#{'${nexs.inward.facility.validate.barcodes.in.ims}'.split(',')}")
    private Set<String> validateInwardFacilityValidateBarcodeInIMS;

    @Override
    @Logging
    public BarcodeValidationResponse validateBarcode(GRNItemDTO grnItem, UpdateStocksRequestV2 updateStocksRequestV2) {
        BarcodeValidationResponse response = new BarcodeValidationResponse();
        BarcodeDetail barcodeDetail = BarcodeUtils.getBarcodeDetail(grnItem.getBarcode());
        if(checkDuplicity(barcodeDetail, response, grnItem.getFacilityCode(), updateStocksRequestV2)) {
            if(checkRangeAndLength(barcodeDetail)){
                response.setMessage(SUCCESS);
                response.setStatus(true);
                return response;
            } else {
                response.setMessage(BARCODE_INVALID);
                response.setStatus(false);
                return response;
            }
        } else {
        	if (StringUtils.isBlank(response.getMessage())) {
        		response.setMessage(BARCODE_DUPLICATE);
        	}
            response.setStatus(false);
            return response;
        }
    }

    public boolean checkDuplicity(BarcodeDetail barcodeDetail, BarcodeValidationResponse response, String facilityCode,
                                  UpdateStocksRequestV2 updateStocksRequestV2) {
        log.info("Duplicity check for barcode : " + barcodeDetail);
        return validateBarcodeDetails(barcodeDetail, response, facilityCode, updateStocksRequestV2);
    }

    private boolean validateBarcodeDetails(BarcodeDetail barcodeDetail, BarcodeValidationResponse response, String facilityCode,
                                           UpdateStocksRequestV2 updateStocksRequestV2) {
        boolean barcodeSuccess = true;
        try {
            log.info("[validateBarcodeDetails]  barcodeDetail {}, facilityCode {}, pid {}, operation {}",
                    barcodeDetail.getBarcode(), facilityCode, updateStocksRequestV2.getStockRequestV2List().get(0).getPid(), updateStocksRequestV2.getOperation());
            barcodeSuccess = validateBarcodeSuccess(barcodeDetail.getBarcode(), facilityCode, GRNConstants.REGULAR_TYPE,
                    updateStocksRequestV2.getStockRequestV2List().get(0).getPid(), updateStocksRequestV2.getOperation(),
                    updateStocksRequestV2.getStockRequestV2List().get(0).getLegalOwner());
            log.info("[validateBarcodeDetails]  barcodeDetail {}, facilityCode {}, pid {}, operation {}, barcodeSuccess {}",
                    barcodeDetail.getBarcode(), facilityCode, updateStocksRequestV2.getStockRequestV2List().get(0).getPid(),
                    updateStocksRequestV2.getOperation(), barcodeSuccess);
            return barcodeSuccess;
        } catch (Exception ex) {
            log.error("[validateBarcodeDetails]  barcodeDetail {}, facilityCode {}, error {}",
                    barcodeDetail, facilityCode, ex.getMessage());
            throw new ApplicationException("Barcode Validation Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public boolean validateBarcodeInIMS(String barcode, String facilityCode,
                                         UpdateStocksRequestV2 updateStocksRequestV2) {
        log.info("[validateBarcodeInIMS] barcode {}, facilityCode {}", barcode, facilityCode);
        try {
            UpdateStocksResponseV2 updateStocksResponseV2 = imsService.validateStockUpdateRequest(updateStocksRequestV2);
            log.info("[validateBarcodeInIMS]  barcode {}, updateStocksRequestV2 {}, updateStocksResponseV2 {}", barcode, updateStocksRequestV2, updateStocksResponseV2);
            if (updateStocksResponseV2 != null && updateStocksResponseV2.getItemStockUpdateResponseV2List() != null
                    && !updateStocksResponseV2.getItemStockUpdateResponseV2List().isEmpty()) {
                if(updateStocksResponseV2.getItemStockUpdateResponseV2List().get(0).isSuccess() &&
                        updateStocksResponseV2.getItemStockUpdateResponseV2List().get(0).getStatusCode().equalsIgnoreCase("200")){
                    return true;
                } else {
                    log.error("[validateBarcodeInIMS] barcode {}, 201 case updateStocksResponseV2 {}",
                            barcode, updateStocksResponseV2.getItemStockUpdateResponseV2List().get(0).getErrorMessage());
                    throw new ApplicationException(updateStocksResponseV2.getItemStockUpdateResponseV2List().get(0).getErrorMessage(),
                            GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
                }
            } else {
                return false;
            }
        } catch (Exception ex) {
            log.error("[validateBarcodeInIMS]  barcodeDetail {}, facilityCode {}, error {}",
                    barcode, facilityCode, ex.getMessage());
            throw new ApplicationException("SQL Exception : " + ex.getMessage(),
                    GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
    }

    public boolean checkRangeAndLength(BarcodeDetail barcodeDetail) {
        log.info("Range check for barcode : " + barcodeDetail);
        if(barcodeDetail.getAlpha().isEmpty()){
            for(Barcode barcode : barcodeConfig.getNumericList()) {
                BarcodeDetail fromBarcode = BarcodeUtils.getBarcodeDetail(barcode.getFromSeries());
                BarcodeDetail toBarcode = BarcodeUtils.getBarcodeDetail(barcode.getToSeries());
                if(barcodeDetail.getNumeric() >= fromBarcode.getNumeric() && barcodeDetail.getNumeric() <= toBarcode.getNumeric()
                    && (barcodeDetail.getBarcode().length() == barcode.getFromSeries().length() ||
                        barcodeDetail.getBarcode().length() == barcode.getToSeries().length()))
                    return true;
            }
            return false;
        } else {
            Map<String, Barcode> map = null;
            if(barcodeConfig.getNexsMap().containsKey(barcodeDetail.getAlpha()))
                map = barcodeConfig.getNexsMap();
            else
                map = barcodeConfig.getNonNexsMap();
            if(map.containsKey(barcodeDetail.getAlpha())){
                Barcode barcode = map.get(barcodeDetail.getAlpha());
                BarcodeDetail fromBarcode = BarcodeUtils.getBarcodeDetail(barcode.getFromSeries());
                BarcodeDetail toBarcode = BarcodeUtils.getBarcodeDetail(barcode.getToSeries());
                if(barcodeDetail.getNumeric() >= fromBarcode.getNumeric() && barcodeDetail.getNumeric() <= toBarcode.getNumeric()
                        && barcodeDetail.getBarcode().length() == barcode.getFromSeries().length())
                    return true;
            }
            return false;
        }
    }

    @SuppressWarnings("rawtypes")
    @Override
	public boolean canScanBarcodeInUnicom(BarcodeValidationResponse response, String barcode, String facilityCode) {
        log.info("[canScanBarcodeInUnicom] Check barcode duplicity in unicom system for barcode {}, facilityCode {}", barcode, facilityCode);
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("barcode", barcode);
        uriVariables.put("facility", facilityCode);
        UnicomResponse unicomResponse = null;
        try {
            log.info("[canScanBarcodeInUnicom] Check barcode duplicity in unicom system for uriVariables {}", uriVariables);
            unicomResponse = RetryUtils.getData(grnConfig.getUnicomBaseUrl() +
                    grnConfig.getUnicomCheckBarcode(), null, uriVariables, UnicomResponse.class);
            log.info("[canScanBarcodeInUnicom] Check barcode duplicity in unicom system for unicomResponse {}", unicomResponse);
            if (unicomResponse == null || (unicomResponse != null && !unicomResponse.getSuccessful())) {
            	response.setMessage(barcode + " check failed in unicom facility " + facilityCode);
            }
        } catch (Exception ex) {
            log.error("[canScanBarcodeInUnicom] Exception from unicom API : " + ex.getMessage());
            response.setMessage(barcode + " check failed in unicom facility " + facilityCode);
            throw new ApplicationException("Exception from unicom API : " + ex, GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        log.info("[canScanBarcodeInUnicom] Barcode duplicity check in unicom system was successful with response : " + unicomResponse);
        return unicomResponse != null ? (boolean) unicomResponse.getResult() : false;
    }

    @Override
    public boolean validateBarcodeSuccess(String barcode, String facility,
                                           String type, Integer pid, String operation, String reqLegalOwner) {
        log.info("[validateBarcodeSuccess] Unicom facility barcode {}, facility {}, type {}, pid {}, operation {}",
                barcode, facility, type, pid, operation);
        boolean barcodeSuccess = true;
        if (validateInwardBarcodeTypeList.contains(type)) {
            if (GRNConstants.MANUAL_JIT.equals(type) && grnItemEntityService.findTopByBarcode(barcode) != null) {
            	return false;
            }
            FacilityDetailsResponse facilityDetailsResponse = facilityDetailsUtils.getFacilityDetails(facility);
            log.info("[validateBarcodeSuccess] Barcode {}, facility {}, facilityDetailsResponse {}", barcode, facility, facilityDetailsResponse);
            String legalOwner = getLegalOwner(facilityDetailsResponse, reqLegalOwner);
            log.info("[validateBarcodeSuccess] Barcode {}, facility {}, legalOwner {}", barcode, facility, legalOwner);
            barcodeSuccess = isBarcodeSuccess(barcode, type, pid, operation, facility, validateInwardBarcodeDefaultFacility, barcodeSuccess, legalOwner);
            log.info("[validateBarcodeSuccess] barcode {}, facility {}, barcodeSuccess {}", barcode, facility, barcodeSuccess);
        } else {
            log.error("[validateBarcodeSuccess] Will not check anything barcode {}, facility {}, type {}, validateInwardBarcodeTypeList {}",
                    barcode, facility, type, validateInwardBarcodeTypeList);
            throw new ApplicationException("Not validating barcode for this inward type " + type, GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
        }
        log.info("[validateBarcodeSuccess] barcode {}, facility {}, barcodeSuccess {}", barcode, facility, barcodeSuccess);
        return barcodeSuccess;
    }

    private static String getLegalOwner(FacilityDetailsResponse facilityDetailsResponse, String reqLegalOwner) {
    	if (StringUtils.isNotBlank(reqLegalOwner)) {
    		return reqLegalOwner;
    	}
        if(facilityDetailsResponse == null || facilityDetailsResponse.getFacilityDetails() == null ||
                facilityDetailsResponse.getFacilityDetails().getLegalOwner() == null){
            return GRNConstants.DEFAULT_LEGAL_OWNER;
        } else {
            return facilityDetailsResponse.getFacilityDetails().getLegalOwner();
        }
    }

    private boolean isBarcodeSuccess(String barcode, String type, Integer pid, String operation, String imsFacility,
                                     String unicomFacility, boolean barcodeSuccess, String legalOwner) {
        log.info("[isBarcodeSuccess] barcode {}, type {}, pid {}, operation {}, imsFacility {}, unicomFacility {}, legalOwner {}",
                barcode, type, pid, operation, imsFacility, unicomFacility, legalOwner);
        UpdateStocksRequestV2 updateStocksRequestV2 = createUpdateStocksRequestV2(barcode, imsFacility, pid, operation, legalOwner);
        boolean imsBarcodeSuccess = validateBarcodeInIMS(barcode, imsFacility, updateStocksRequestV2);
        log.info("[validateBarcodeInIms] barcode {}, imsBarcodeSuccess {}", barcode, imsBarcodeSuccess);
        return imsBarcodeSuccess;
    }

    private UpdateStocksRequestV2 createUpdateStocksRequestV2(String barcode, String facility,
                                                              Integer pid, String operation, String legalOwner) {
        log.info("[createUpdateStocksRequestV2] barcode {}, facility {}, pid {}, operation {}, legalOwner {}", barcode, facility, pid, operation, legalOwner);
        UpdateStocksRequestV2 updateStocksRequestV2 = new UpdateStocksRequestV2();

        List<StockRequestV2> stockRequestV2List = new ArrayList<>();
        StockRequestV2 stockRequestV2 = new StockRequestV2();
        stockRequestV2.setBarcode(barcode);
        stockRequestV2.setFacility(facility);
        stockRequestV2.setUpdatedBy(GRN_VALIDATE_UPDATED_BY);
        stockRequestV2.setPid(pid);
        stockRequestV2.setLegalOwner(legalOwner);

        stockRequestV2List.add(stockRequestV2);
        updateStocksRequestV2.setStockRequestV2List(stockRequestV2List);
        updateStocksRequestV2.setDeleteBarcode(false);
        updateStocksRequestV2.setUpdatedBy(GRN_VALIDATE_UPDATED_BY);
        updateStocksRequestV2.setOperation(operation);
        log.info("[createUpdateStocksRequestV2] updateStocksRequestV2 {}", updateStocksRequestV2);
        return updateStocksRequestV2;
    }

    private boolean validateBarcodeInUnicom(String barcode, String facility) {
        log.info("[validateBarcodeInUnicom] barcode {}, facility {}", barcode, facility);
        boolean unicomBarcodeSuccess = canScanBarcodeInUnicom(new BarcodeValidationResponse(),
                barcode, facility);
        log.info("[validateBarcodeInUnicom] barcode {}, facility {}, unicomBarcodeSuccess {}",
                barcode, facility, unicomBarcodeSuccess);
        return unicomBarcodeSuccess;
    }
}
