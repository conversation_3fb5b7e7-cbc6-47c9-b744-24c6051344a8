package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.common.base.entityService.EntityService;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.grn.entity.GrnItemsArchivedEntity;

import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.List;

public interface GrnItemsArchivedEntityService extends EntityService<GrnItemsArchivedEntity, BigInteger> {

    GrnItemsArchivedEntity findByBarcodeAndGrnCode(String barcode, String grnCode);

    List<Tuple> findBarcodeListItemPriceByFacility(List<String> barcodeList, String facilityCode);

    List<GrnItemsArchivedEntity> findByBarcodeIn(List<String> barcodeList);

    GrnItemsArchivedEntity findTopByBarcode(String barCode);

    GrnItemsArchivedEntity getTopGrnItemDetailForBarcodeAndFacility(String barcode, String facilityCode);
}
