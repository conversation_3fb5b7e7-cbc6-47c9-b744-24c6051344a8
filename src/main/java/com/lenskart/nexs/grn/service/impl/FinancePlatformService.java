package com.lenskart.nexs.grn.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.entity.entityService.grn.GrnMasterEntityService;
import com.lenskart.nexs.common.entity.po.grn.GRND365Sync;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.grn.client.NexsClient;
import com.lenskart.platform.fl.utils.dto.FinanceEventDto;
import com.lenskart.platform.fl.utils.dto.grn.Grn;
import com.lenskart.platform.fl.utils.enums.EventChannel;
import com.lenskart.platform.fl.utils.finance.service.FacilityDetailsService;
import com.lenskart.platform.fl.utils.finance.service.FinanceService;
import com.lenskart.platform.fl.utils.jpa.dto.FLEventsTrackerDto;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FinancePlatformService {
    @Autowired
    private NexsClient nexsClient;
    @Autowired
    private FinanceService financeService;
    @Autowired
    GrnMasterEntityService grnMasterEntityService;
    @Autowired
    private FacilityDetailsService facilityDetailsService;
    @Autowired
    private ObjectMapper objectMapper;

    public void publishGrnToFinancePlatform(GRND365Sync grnd365Sync) {
        log.info("[publishGrnToFinancePlatform] processing grn : {}",grnd365Sync.getGrnCode());
        GrnMasterEntity grnMaster = grnMasterEntityService.findByGrnCode(grnd365Sync.getGrnCode());
        try {
            Grn grn = nexsClient.getGrnPayload(grnd365Sync.getGrnCode());
            Long clientEventId = createFinanceEvent(grnd365Sync.getGrnCode(), "GRN");
            grn.setType("grn");
            log.info("[publishGrnToFinancePlatform] publishing grn : {}",grnd365Sync.getGrnCode());
            financeService.saveEvent(buildFinanceEventDto(grn,grnMaster.getFacility(),clientEventId));
        } catch (Exception e) {
            log.error("Error occurred while processing grn :{}", grnd365Sync.getGrnCode(), e);
        }
    }

    @SneakyThrows
    private FinanceEventDto buildFinanceEventDto(Grn grn, String facility, Long clientEventId) {
        grn.setEventCorrelationIdentifier(null);
        grn.setEventUniqueIdentifier(grn.getReceiptHeader().getProductReceipt());
        return FinanceEventDto.builder()
                .legalEntity(grn.getReceiptHeader().legalEntity)
                .country(facilityDetailsService.getCountryCodeForFacility(facility))
                .source("NEXS")
                .eventType("GRN")
                .eventData(grn)
                .clientEventId(String.valueOf(clientEventId))
                .channel(EventChannel.KAFKA)
                .rawEventData(objectMapper.writeValueAsString(grn))
                .createdBy("NEXS")
                .build();
    }

    private Long createFinanceEvent(String grnCode, String eventType) throws Exception {
        FLEventsTrackerDto flEventsTrackerDto = FLEventsTrackerDto.builder()
        .eventType("GRN")
        .eventIdentifierType("GRN_CODE")
        .eventIdentifierValue(grnCode)
        .createdBy("NEXS")
        .build();
        return financeService.createEvent(flEventsTrackerDto);
    }
}
