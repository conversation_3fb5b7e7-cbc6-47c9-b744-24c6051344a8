package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.PurchaseOrder;

public interface PoInvoiceService {
    Invoice getInvoiceDetails(String invoiceRefNum) throws Exception;

    Invoice getInvoiceDetailsByVendorInvoiceNum(String vendorInvoiceNum) throws Exception;

    PurchaseOrder getPurchaseOrderDetails(String poNum) throws Exception;

    public com.lenskart.nexs.common.entity.po.PurchaseOrder getPurchaseOrder(String poNum) throws CustomException;
}
