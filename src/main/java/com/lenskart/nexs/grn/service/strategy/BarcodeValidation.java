package com.lenskart.nexs.grn.service.strategy;

import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.model.BarcodeValidationResponse;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;

public interface BarcodeValidation {

    public BarcodeValidationResponse validateBarcode(GRNItemDTO grnItem, UpdateStocksRequestV2 updateStocksRequestV2);

    public boolean validateBarcodeInIMS(String barcode, String facilityCode, UpdateStocksRequestV2 updateStocksRequestV2);

    public boolean canScanBarcodeInUnicom(BarcodeValidationResponse response, String barcode, String facilityCode);

    public boolean validateBarcodeSuccess(String barcode, String facility,
                                          String type, Integer pid, String operation, String facLegalOwner);
}