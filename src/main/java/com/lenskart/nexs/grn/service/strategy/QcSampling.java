//package com.lenskart.nexs.grn.service.strategy;
//
//
//import com.lenskart.nexs.grn.exception.InvalidRequestException;
//import com.lenskart.nexs.grn.model.GRNItem;
//
//public interface QcSampling {
//
//    public boolean performSampling(GRNItem grnItem) throws InvalidRequestException;
//
//    public void loadConfig(GRNItem grnItem);
//
//    public void itemQuantityCheck(GRNItem grnItem) throws InvalidRequestException;
//}
