package com.lenskart.nexs.grn.service.impl;

import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.exception.ValidationException;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.service.WmConnector;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class WmConnectorImpl implements WmConnector {

    @CustomLogger
    private Logger logger;

    @Value("${athena.wm.base.url}")
    private String wmBaseUrl;

    private static final String GET_GRN_ITEM_BY_BARCODE = "/grnItem/barcode/";
    static final int connectTimeout = 3000;
    static final int readTimeout = 3000;

    public GRNItem getGrnItemByBarcode(String barcode) throws Exception {
        try {
            logger.info("[getGrnItemByBarcode] calling WM service to get GRN Item for the barcode {}", barcode);
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

            String url = wmBaseUrl + GET_GRN_ITEM_BY_BARCODE + barcode;
            logger.info("[getGrnItemByBarcode] calling WM service to get GRN Item with URL {}", url);
            GRNItem response = RestUtils.getData(url, httpHeaders, null, GRNItem.class, connectTimeout, readTimeout);

            logger.info("[getGrnItemByBarcode] response from WM service for get GRN Item call {}", response);
            return response;
        }catch (Exception exception){
            logger.error("[getGrnItemByBarcode] get GRN Item from WM Service call failed for barcode {} with exception {}", barcode, exception);
            throw new ValidationException("get GRN Item from WM Service call failed for barcode " + barcode + " with exception " + exception);
        }
    }
}
