//package com.lenskart.nexs.grn.service.strategy.impl;
//
//import com.lenskart.nexs.grn.constants.QualifierConstants;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.stereotype.Service;
//
//import com.lenskart.nexs.commonlogger.annotations.Logging;
//import com.lenskart.nexs.grn.constants.GRNConstants;
//import com.lenskart.nexs.grn.dao.CacheDAO;
//import com.lenskart.nexs.grn.dao.GRNPIDDAO;
//import com.lenskart.nexs.grn.dao.QcStatusDAO;
//import com.lenskart.nexs.grn.exception.ApplicationException;
//import com.lenskart.nexs.grn.exception.InvalidRequestException;
//import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
//import com.lenskart.nexs.grn.model.GRNItem;
//import com.lenskart.nexs.grn.model.QcConfig;
//import com.lenskart.nexs.grn.service.strategy.QcSampling;
//
//import lombok.extern.slf4j.Slf4j;
//
//@Service
//@Slf4j
//public class QcSamplingImpl implements QcSampling, GRNConstants {
//
//    @Autowired
//    private CacheDAO cacheDAO;
//
//    @Autowired
//    @Qualifier(QualifierConstants.JPA_QC_STATUS_DAO)
//    private QcStatusDAO qcStatusDAO;
//
//    @Autowired
//    @Qualifier(QualifierConstants.JPA_GRN_PID_DAO)
//	private GRNPIDDAO grnPIDMasterDAO;
//
//    @Logging
//    @Override
//    public boolean performSampling(GRNItem grnItem) {
//        qcStatusDAO.incrementAllScanned(grnItem.getGrnCode(), grnItem.getPid());
//        long totalScanned = qcStatusDAO.incrementScanCount(grnItem.getGrnCode(), grnItem.getPid());
//        long totalFailed = 0;
//        if(QC_FAIL.equals(grnItem.getQcStatus().toLowerCase())) {
//            totalFailed = qcStatusDAO.incrementFailCount(grnItem.getGrnCode(), grnItem.getPid());
//            qcStatusDAO.incrementAllFailed(grnItem.getGrnCode(), grnItem.getPid());
//        } else
//            totalFailed = qcStatusDAO.getTotalFailed(grnItem.getGrnCode(), grnItem.getPid());
//        log.info(grnItem.getGrnCode() + " : Total scanned : " + totalScanned + ", Total failed : " + totalFailed);
//
//        QcConfig grnQcConfig = cacheDAO.getGrnQcConfig(grnItem);
//        if(qcStatusDAO.isGRNManualOverriden(grnItem.getGrnCode(), grnItem.getPid())){
//        	log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : manual override is set");
//            setItemParams(grnItem, PASSED, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 1);
//            return true;
//        }
//        if(grnItem.getGrnEstimatedQuantity() == 0 && grnQcConfig.getSamplingPercent() != 0 && grnQcConfig.getSamplingPercent() != 100){
//            throw new ApplicationException("In case of 0 estimated quantity, sampling percent should be 0 or 100", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//        } else if(grnItem.getGrnEstimatedQuantity() == 0 && grnQcConfig.getSamplingPercent() == 0) {
//            qcStatusDAO.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), PASSED);
//            grnPIDMasterDAO.updateGRNPIDMasterStatus(PASSED, grnItem.getGrnCode(), grnItem.getPid());
//        	log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : estimated quantity and sampling percent is 0");
//            setItemParams(grnItem, PASSED, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 1);
//            return true;
//        } else if (grnItem.getGrnEstimatedQuantity() == 0 && grnQcConfig.getSamplingPercent() == 100) {
//            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : estimated quantity is 0 and sampling percent is 100");
//            setItemParams(grnItem, PENDING, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 0);
//            return true;
//        }
//        if(qcStatusDAO.isQcStatusFailed(grnItem.getGrnCode(), grnItem.getPid())) {
//        	log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : Qc status : fail");
//            return false;
//        }
//
//        if(qcStatusDAO.isChannelGreen(grnItem.getGrnCode(), grnItem.getPid())) {
//        	log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : in : green channel");
//            setItemParams(grnItem, PASSED, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 1);
//            return true;
//        }
//
//        if(totalScanned >= grnQcConfig.getSamplingQuantity() && (totalFailed < grnQcConfig.getFailureQuantity() ||
//                (totalFailed == 0 && grnQcConfig.getFailureQuantity() == 0))) {
//
//            qcStatusDAO.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), PASSED);
//            grnPIDMasterDAO.updateGRNPIDMasterStatus(PASSED, grnItem.getGrnCode(), grnItem.getPid());
//            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : moved to : green channel");
//            setItemParams(grnItem, PASSED, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 0);
//
//        } else if(totalScanned >= grnQcConfig.getSamplingQuantity() && totalFailed >= grnQcConfig.getFailureQuantity()) {
//            if (!cacheDAO.setNextGradientLevel(grnItem)) {
//                qcStatusDAO.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), FAILED);
//                grnPIDMasterDAO.updateGRNPIDMasterStatus(FAILED, grnItem.getGrnCode(), grnItem.getPid());
//                setItemParams(grnItem, FAILED, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 2);
//                log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : moved to : failed");
//                return false;
//            }
//            grnItem.setGradientShift(1);
//            setItemParams(grnItem, PENDING, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 0);
//        } else
//            setItemParams(grnItem, PENDING, grnQcConfig.getSamplingPercent(), grnQcConfig.getFailurePercent(), 0);
//        return true;
//    }
//
//
//
//    @Logging
//    public void itemQuantityCheck(GRNItem grnItem) throws InvalidRequestException {
//        log.info("[itemQuantityCheck] Po: {}, pId: {}, grn code: {}, barcode: {}", grnItem.getPoId(),
//                grnItem.getPid(), grnItem.getGrnCode(), grnItem.getBarcode());
//
//        long invoiceQty = qcStatusDAO.incrementItemQtyCount(grnItem.getInvoiceRefNum(), grnItem.getPid());
//        long poQty = qcStatusDAO.incrementItemQtyCount(grnItem.getPoId(), grnItem.getPid(), true);
//
//        log.info("[itemQuantityCheck] Po: {}, pId: {}, grn code: {}, invoiceQty: {},  grn invoiceQty: {}, poQty: {}, grn poQty: {}", grnItem.getPoId(),
//                grnItem.getPid(), grnItem.getGrnCode(), invoiceQty, grnItem.getInvoiceQuantity(), poQty, grnItem.getPoQuantity());
//
//        if(invoiceQty > grnItem.getInvoiceQuantity() || poQty > grnItem.getPoQuantity()) {
//            log.error("[itemQuantityCheck] Po: {}, pId: {}, DECREMENT COUNT grn code: {}, invoiceQty: {}, grn invoiceQty: {}, poQty: {}, grn poQty: {},",
//                    grnItem.getPoId(), grnItem.getPid(), grnItem.getGrnCode(), invoiceQty, grnItem.getInvoiceQuantity(), poQty, grnItem.getPoQuantity());
//
//            long decrInvoiceQty = qcStatusDAO.decrementItemQtyCount(grnItem.getPoId(), grnItem.getPid(), true);
//            long decrPoQty = qcStatusDAO.decrementItemQtyCount(grnItem.getInvoiceRefNum(), grnItem.getPid());
//
//            String errorMessage = "Invoice quantity reached for pid : " + grnItem.getPid();
//            if (poQty > grnItem.getPoQuantity())
//            	errorMessage = "PO quantity reached for pid : " + grnItem.getPid();
//
//            log.error("[itemQuantityCheck] Po: {}, pId: {}, DECREMENT COUNT AFTER grn code: {}, decrInvoiceQty: {}, decrPoQty :{}",
//                    grnItem.getPoId(), grnItem.getPid(), grnItem.getGrnCode(), decrInvoiceQty, decrPoQty);
//			throw new InvalidRequestException(errorMessage, GRNExceptionStatus.GRN_BAD_REQUEST);
//        }
//    }
//
//    @Override
//    @Logging
//    public void loadConfig(GRNItem grnItem) {
//        if(!qcStatusDAO.hasReferenceKey(grnItem.getInvoiceRefNum(), grnItem.getPid()))
//            cacheDAO.loadConfig(grnItem, true);
//        if(!qcStatusDAO.hasGrnKey(grnItem.getGrnCode(), grnItem.getPid())) {
//            cacheDAO.loadConfig(grnItem, false);
//            QcConfig qc = cacheDAO.getInvoiceQcConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode());
//            qc.setQuantity(grnItem.getGrnEstimatedQuantity());
//            long samplingQty = (long)Math.ceil(qc.getSamplingPercent() * grnItem.getGrnEstimatedQuantity() / 100.0);
//            qc.setSamplingQuantity(samplingQty);
//            long failureQty = (long)Math.ceil(qc.getSamplingQuantity() * qc.getFailurePercent() / 100.0);
//            qc.setFailureQuantity(failureQty);
//            cacheDAO.setConfigByEstimatedQty(grnItem, qc, 0, 0);
//            cacheDAO.initializeKeys(grnItem, 0 ,0);
//        }
//    }
//
//    public void setItemParams(GRNItem grnItem, String status, int samplingPct, int failurePct, int channelStatus) {
//        grnItem.setStatus(status);
//        grnItem.setQcMasterSampling(samplingPct);
//        grnItem.setFailureThresholdQcMaster(failurePct);
//        grnItem.setChannelStatus(channelStatus);
//        log.info("Grn item after setting params : " + grnItem);
//    }
//
//    //----//
//
////    public void setItemParam(GRNItem grnItem, String status, int channelStatus) {
////        grnItem.setStatus(status);
////        grnItem.setChannelStatus(channelStatus);
////        log.info("Grn item after setting params : " + grnItem);
////    }
////
////
////    public boolean updateCache(GRNItem grnItem) {
////        qcStatusDAO.incrementAllScanned(grnItem.getGrnCode(), grnItem.getPid());
////        long totalScanned = qcStatusDAO.incrementScanCount(grnItem.getGrnCode(), grnItem.getPid());
////        long totalFailed = 0;
////        if(QC_FAIL.equals(grnItem.getQcStatus().toLowerCase())) {
////            totalFailed = qcStatusDAO.incrementFailCount(grnItem.getGrnCode(), grnItem.getPid());
////            qcStatusDAO.incrementAllFailed(grnItem.getGrnCode(), grnItem.getPid());
////        } else
////            totalFailed = qcStatusDAO.getTotalFailed(grnItem.getGrnCode(), grnItem.getPid());
////        log.info(grnItem.getGrnCode() + " : Total scanned : " + totalScanned + ", Total failed : " + totalFailed);
////        if(qcStatusDAO.isGRNManualOverriden(grnItem.getGrnCode(), grnItem.getPid())){
////            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : manual override is set");
////            setItemParam(grnItem, PASSED, 1);
////            return true;
////        }
////
////        if(qcStatusDAO.isQcStatusFailed(grnItem.getGrnCode(), grnItem.getPid())) {
////            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : Qc status : fail");
////            return false;
////        }
////
////        if(qcStatusDAO.isChannelGreen(grnItem.getGrnCode(), grnItem.getPid())) {
////            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : in : green channel");
////            setItemParam(grnItem, PASSED, 1);
////            return true;
////        }
////
////        if(grnItem.getStatus().equals(PASSED)) {
////            qcStatusDAO.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), PASSED);
////            grnPIDMasterDAO.updateGRNPIDMasterStatus(PASSED, grnItem.getGrnCode(), grnItem.getPid());
////            log.info(grnItem.getGrnCode() + " : " + grnItem.getPid() + " : moved to : green channel");
////            setItemParam(grnItem, PASSED, 0);
////            return true;
////        }
////        setItemParam(grnItem, PENDING, 0);
////
////        return true;
////
////    }
//
//}
//
