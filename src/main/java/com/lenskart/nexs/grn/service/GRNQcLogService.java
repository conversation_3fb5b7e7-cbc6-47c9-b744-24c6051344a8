package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.grn.constants.QualifierConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNQcLogEvents;
import com.lenskart.nexs.grn.dao.GRNQcLogDAO;
import com.lenskart.nexs.grn.dto.request.GetPIDDTO;
import com.lenskart.nexs.grn.dto.request.ProductMismatchReqDTO;
import com.lenskart.nexs.grn.dto.response.PIDResponseDTO;
import com.lenskart.nexs.grn.model.GRNQcLog;

@Component
public class GRNQcLogService {

	@Autowired
	private GRNPIDMasterService grnPidMasterService;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_QC_LOG_DAO)
	private GRNQcLogDAO grnQcLogDAO;

	@Logging
	public boolean productMismatch(ProductMismatchReqDTO productMismatchReq) {

		if (checkProductMismatch(productMismatchReq)) {
			GRNQcLog grnQcLog = new GRNQcLog(productMismatchReq.getGrnCode(), productMismatchReq.getPid(), productMismatchReq.getBarcode(),
					productMismatchReq.getAction(), productMismatchReq.getReason());

			grnQcLogDAO.saveLog(grnQcLog);
			return true;
		}
		return false;
	}

	@Logging
	public void logGRNQcAction(String grnCode, String pid, String barcode, GRNQcLogEvents event) {
		GRNQcLog grnQcLog = new GRNQcLog(grnCode, pid, barcode, event.action(), event.reason());
		grnQcLogDAO.saveLog(grnQcLog);
	}

	private boolean checkProductMismatch(ProductMismatchReqDTO productMismatchReq) {

		GetPIDDTO getPidDTO = new GetPIDDTO();
		getPidDTO.setMeta(productMismatchReq.getMeta());
		getPidDTO.setScannedId(productMismatchReq.getScannedPid());
		
		PIDResponseDTO response = null;
		try {
			response = grnPidMasterService.getPID(getPidDTO);
		} catch (Exception ex) {
			return true;
		}

		return !productMismatchReq.getPid().equalsIgnoreCase(response.getPid());
	}
}
