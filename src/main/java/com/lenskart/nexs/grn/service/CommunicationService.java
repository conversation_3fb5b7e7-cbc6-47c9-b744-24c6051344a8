package com.lenskart.nexs.grn.service;

import com.lenskart.communication.client.model.Email;
import com.lenskart.communication.client.service.MailSender;
import com.sendgrid.SendGrid;
import com.sendgrid.SendGridException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

@Service
@Slf4j
public class CommunicationService {

    private static MailSender mailSender;

    @Autowired
    private MailSender mailSend;

    @PostConstruct
    private void initStaticDao () {
        mailSender = this.mailSend;
    }

    public void sendMail(String message, String fromEmailId, String[] toEmailId,
                         String emailSubject) throws IOException, SendGridException {

        Email email =new Email();
        email.setFrom(fromEmailId);

        String emailId = MDC.get("USER_MAIL");
        if(toEmailId==null || toEmailId.length == 0)
           email.setTo(new String[]{emailId});
        else
            email.setTo(toEmailId);

        email.setSubject(emailSubject);
        email.setHtml(message);

        log.info("Email details : "+email.toString());
        SendGrid.Response response= mailSender.sendEmailWithAttachment(email,null);
        log.info("Response from SendGrid : "+response);
    }
}
