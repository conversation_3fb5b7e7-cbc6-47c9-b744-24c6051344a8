package com.lenskart.nexs.grn.service.strategy.impl;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.model.Product;
import com.lenskart.nexs.grn.service.strategy.GRNQCValidation;
import com.lenskart.nexs.grn.util.CommonUtils;

@Service
public class GRNQCValidationImpl implements GRNQCValidation, GRNConstants {

	private static final String VALIDATION_STATUS = "validation_status";
	private static final String REASON = "reason";

	@Override
	@Logging
	public Map<String, Object> validateGRNItem(GRNItemDTO grnItemDTO) {
		Map<String, Object> result = new HashMap<>();
		result.put(VALIDATION_STATUS, true);

		Product productDetails = grnItemDTO.getProduct();

		if ((productDetails.getCategoryId().equals(CONTACT_LENS) || productDetails.getCategoryId().equals(CONTACT_LENS_SOLUTION))
				&& grnItemDTO.getQcStatus().equalsIgnoreCase(QC_PASS)) {

			LocalDate futureDate = LocalDate.now().plusDays(grnItemDTO.getExpiryDateThreshold());
			LocalDate expiryDate = grnItemDTO.getExpiryDate() != null ? grnItemDTO.getExpiryDate().toLocalDateTime().toLocalDate() : null;

			if (expiryDate != null && expiryDate.isBefore(futureDate)) {
				result.put(VALIDATION_STATUS, false);
				result.put(REASON, "Item expiry date is not within range");
			}

		}
		return result;
	}
}
