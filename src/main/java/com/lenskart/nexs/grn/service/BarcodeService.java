package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.config.BarcodeConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.BarcodeDAO;
import com.lenskart.nexs.grn.model.Barcode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class BarcodeService implements GRNConstants {

    @Autowired
    @Qualifier(QualifierConstants.JPA_BARCODE_DAO)
    private BarcodeDAO barcodeDAO;

    @Logging
    public List<Barcode> getBarcodeSeries() {

        List<Barcode> barcodeSeries = barcodeDAO.getBarcodeSeries();
        for(Barcode barcode : barcodeSeries)
            barcode.setType(barcode.getIsBox() == 1 ? BOX : ITEM);

        return barcodeSeries;
    }
}
