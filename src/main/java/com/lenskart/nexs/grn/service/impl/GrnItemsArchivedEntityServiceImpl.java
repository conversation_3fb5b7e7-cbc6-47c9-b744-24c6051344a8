package com.lenskart.nexs.grn.service.impl;

import com.lenskart.nexs.common.base.EntitiesRepository;
import com.lenskart.nexs.common.base.entityServiceImpl.EntityServiceImpl;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.grn.repo.GrnItemsArchivedRepository;
import com.lenskart.nexs.grn.entity.GrnItemsArchivedEntity;
import com.lenskart.nexs.grn.service.GrnItemsArchivedEntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.List;

@Service
public class GrnItemsArchivedEntityServiceImpl extends EntityServiceImpl<GrnItemsArchivedEntity, BigInteger>
        implements GrnItemsArchivedEntityService {
    
	@Autowired
    private GrnItemsArchivedRepository grnItemsArchivedRepository;

    @Override
    public EntitiesRepository<GrnItemsArchivedEntity, BigInteger> getDao() {
        return this.grnItemsArchivedRepository;
    }

    @Override
    public GrnItemsArchivedEntity findByBarcodeAndGrnCode(String barcode, String grnCode) {
        return grnItemsArchivedRepository.findByBarcodeAndGrnCode(barcode, grnCode);
    }

    @Override
    public List<Tuple> findBarcodeListItemPriceByFacility(List<String> barcodeList, String facilityCode) {
        return this.grnItemsArchivedRepository.findBarcodeListItemPriceByFacility(barcodeList, facilityCode);
    }

    @Override
    public List<GrnItemsArchivedEntity> findByBarcodeIn(List<String> barcodeList) {
        return this.grnItemsArchivedRepository.findByBarcodeIn(barcodeList);
    }

    @Override
    public GrnItemsArchivedEntity findTopByBarcode(String barCode) {
        return this.grnItemsArchivedRepository.findTopGrnItemByBarcode(barCode);
    }

    @Override
    public GrnItemsArchivedEntity getTopGrnItemDetailForBarcodeAndFacility(String barcode, String facilityCode) {
        return this.grnItemsArchivedRepository.getTopGrnItemDetailForBarcodeAndFacility(barcode, facilityCode);
    }
}
