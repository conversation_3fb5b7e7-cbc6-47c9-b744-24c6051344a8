package com.lenskart.nexs.grn.config;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

@ExtendWith(MockitoExtension.class)
public class ExpiryMonthMappingTest {

    @InjectMocks
    private ExpiryMonthMapping expiryMonthMapping;

    @Mock
    GRNConfig grnConfig;

    @Test
    public void fetchExpiryMonths() throws Exception {
        Mockito.when(grnConfig.getExpiryMonthMappingJson()).thenReturn("{\"LKIN\": {\"BULK\": {\"BULK_SO\": {\"DEFAULT\": 24}}, \"CL\": {\"DEFAULT\": {\"19153\": 12, \"24148\": 12, \"DEFAULT\": 18}, \"JIT\": {\"DEFAULT\": 12}}}, \"LKSG\": {\"CL\": {\"DEFAULT\": {\"DEFAULT\": 24}}}}\n");
        expiryMonthMapping.fetchExpiryMonths("LKIN", "BULK", "BULK_SO", "DEFAULT");
        expiryMonthMapping.fetchExpiryMonths("LKIN", "BULK", "DEFAULT", "DEFAULT");
        expiryMonthMapping.fetchExpiryMonths("LKIN", "BULK", null, "11356");

        Assertions.assertEquals("ABC", "ABC","Id should be set to default value");
    }


}
