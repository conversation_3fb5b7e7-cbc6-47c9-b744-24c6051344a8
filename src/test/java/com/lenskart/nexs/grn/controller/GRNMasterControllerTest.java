package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.grn.dto.request.CreateGRNMasterDTO;
import com.lenskart.nexs.grn.dto.GRNMasterDTO;
import com.lenskart.nexs.grn.dto.request.CreateGRNPIDTO;
import com.lenskart.nexs.grn.dto.request.GRNUpdateDTO;
import com.lenskart.nexs.grn.dto.response.*;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.UserActivity;
import com.lenskart.nexs.grn.service.GRNMasterService;
import com.lenskart.nexs.grn.service.GRNPIDMasterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class GRNMasterControllerTest {

    @Mock
    private GRNMasterService grnMasterService;

    @Mock
    private GRNPIDMasterService grnpidMasterService;

    @InjectMocks
    private GRNMasterController grnMasterController;

    @BeforeEach
    public void setUp() {

    }

    //@Test
    public void testCreateGRN() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        CreateGRNResponseDTO createGRNResponseDTO = new CreateGRNResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        CreateGRNMasterDTO grnMasterCreateRequestDTO = new CreateGRNMasterDTO();
        Mockito.when(grnMasterService.createGRN(grnMasterCreateRequestDTO,"","")).thenReturn(createGRNResponseDTO);
        ResponseDTO responseDTO1 = grnMasterController.createGRN("LKH03",grnMasterCreateRequestDTO);
        assertNotNull(responseDTO1);
    }

    //@Test
    public void testCreateGRNException() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        CreateGRNMasterDTO grnMasterCreateRequestDTO = new CreateGRNMasterDTO();
        Mockito.when(grnMasterService.createGRN(grnMasterCreateRequestDTO,"","")).thenThrow(Exception.class);
        assertThrows(ApplicationException.class, () -> grnMasterController.createGRN("LKH03",grnMasterCreateRequestDTO));
    }

    //@Test
    public void testGetGRN() throws Exception {
    	GetGRNResponseDTO responseDTO = new GetGRNResponseDTO();
        responseDTO.setGrnCode("GRN1");
        Mockito.when(grnMasterService.getGRN("grncode")).thenReturn(responseDTO);
        ResponseDTO responseDTO1 = grnMasterController.getGRN("grncode");
        assertNotNull(responseDTO1);
    }

    //@Test
    public void testGetGRNException() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        CreateGRNMasterDTO grnMasterCreateRequestDTO = new CreateGRNMasterDTO();
        Mockito.when(grnMasterService.getGRN("grncode")).thenThrow(Exception.class);
        assertThrows(ApplicationException.class, () -> grnMasterController.getGRN("grncode"));
    }

    //@Test
    public void testUpdateGRN() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        UpdateGRNResponseDTO updateGRNResponseDTO = new UpdateGRNResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        GRNUpdateDTO grnMasterDTO = new GRNUpdateDTO();
        Mockito.doNothing().when(grnMasterService).editGRN(grnMasterDTO, "LKH03");
        Mockito.verify(grnMasterService, Mockito.times(1)).editGRN(grnMasterDTO, "LKH03");
    }

    //@Test
    public void testUpdateGRNException() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        GRNUpdateDTO grnMasterDTO = new GRNUpdateDTO();
        Mockito.doThrow(Exception.class).when(grnMasterService).editGRN(grnMasterDTO, "LKH03");
        assertThrows(ApplicationException.class, () -> grnMasterController.editGRN(grnMasterDTO, "LKH03"));
    }

    //@Test
    public void testCloseGRN() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        Mockito.doNothing().when(grnMasterService).closeGRN("grncode", "LKH03", null, true);
//        ResponseDTO responseDTO1 = grnMasterController.closeGRN("grncode", "LKH03", null, null, true);
        // assertNotNull(responseDTO1);
    }

    //@Test
    public void testCloseGRNException() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        Mockito.doThrow(RuntimeException.class).when(grnMasterService).closeGRN("grncode", "LKH03", null, true);
//        assertThrows(ApplicationException.class, () -> grnMasterController.closeGRN("grncode", "LKH03", null,null));
    }

//    //@Test
//    public void testUpdateEstimatedQuantity() throws Exception {
//        Mockito.doNothing().when(grnMasterService).updateEstimatedQty(Mockito.any());
//        grnMasterController.updateEstimatedQuantity(Mockito.any());
//        Mockito.verify(grnMasterService, Mockito.times(1)).updateEstimatedQty(Mockito.any());
//    }
//
//    //@Test
//    public void testUpdateEstimatedQuantityException() throws Exception {
//        Mockito.doThrow(RuntimeException.class).when(grnMasterService).updateEstimatedQty(Mockito.any());
//        assertThrows(ResponseStatusException.class, () -> grnMasterController.updateEstimatedQuantity(Mockito.any()));
//    }
//
//    //@Test
//    public void testUpdateEstimatedQuantityResponseStatusException() throws Exception {
//        Mockito.doThrow(ResponseStatusException.class).when(grnMasterService).updateEstimatedQty(Mockito.any());
//        assertThrows(ResponseStatusException.class, () -> grnMasterController.updateEstimatedQuantity(Mockito.any()));
//    }

    //@Test
    public void testAssignGRNException1() {
        UserActivity userActivity = new UserActivity();
        assertThrows(ResponseStatusException.class, () -> grnMasterController.assignGRN("LKH03",userActivity));
    }

    //@Test
    public void testAssignGRNException2() throws Exception {
        UserActivity userActivity = new UserActivity();
        userActivity.setAssignedTo("User1");
        Mockito.doThrow(RuntimeException.class).when(grnMasterService).assignGRN(userActivity);
        assertThrows(ApplicationException.class, () -> grnMasterController.assignGRN("LKH03",userActivity));
    }

    //@Test
    public void testSetManualOverrideException1() {
        assertThrows(ResponseStatusException.class, () -> grnMasterController.setManualOverride(null, null,false));
    }

    //@Test
    public void testSetManualOverrideException2() throws Exception {
        Mockito.doThrow(RuntimeException.class).when(grnpidMasterService).setManualOverride(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean());
        assertThrows(ApplicationException.class, () -> grnMasterController.setManualOverride("grn", "pid", true));
    }

    //@Test
    public void testSetManualOverrideResponseStatusException() throws Exception {
        Mockito.doThrow(ResponseStatusException.class).when(grnpidMasterService).setManualOverride(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean());
        assertThrows(ResponseStatusException.class, () -> grnMasterController.setManualOverride("grn", "pid", true));
    }

    //@Test
    public void testAddGrnPID() throws Exception {
    	GRNAddPidResponseDTO responseDTO = new GRNAddPidResponseDTO();
        responseDTO.setGrnCode("GRN1");
        CreateGRNPIDTO createGRNPIDTO = new CreateGRNPIDTO();
        Mockito.when(grnMasterService.addGrnPID(createGRNPIDTO,"")).thenReturn(responseDTO);
        ResponseDTO responseDTO1 = grnMasterController.addGrnPID(createGRNPIDTO,"");
        assertNotNull(responseDTO1);
    }

    //@Test
    public void testAddGrnPIDException() throws Exception {
        CreateGRNPIDTO createGRNPIDTO = new CreateGRNPIDTO();
        Mockito.when(grnMasterService.addGrnPID(createGRNPIDTO,"")).thenThrow(Exception.class);
        assertThrows(ApplicationException.class, () -> grnMasterController.addGrnPID(createGRNPIDTO,""));
    }

    //@Test
    public void testGetGRNSummary() throws Exception {
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setDisplayMessage("Successful");
        Mockito.when(grnMasterService.getGRNSummary("grn", "LKH03")).thenReturn(responseDTO);
        ResponseDTO responseDTO1 = grnMasterController.getGRNSummary("LKH03","grn");
        assertNotNull(responseDTO1);
    }

    //@Test
    public void testGetGRNSummaryException() throws Exception {
        Mockito.when(grnMasterService.getGRNSummary("grn", "LKH03")).thenThrow(Exception.class);
        assertThrows(ApplicationException.class, () -> grnMasterController.getGRNSummary("LKH03","grn"));
    }
}
