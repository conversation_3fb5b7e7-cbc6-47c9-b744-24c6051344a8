package com.lenskart.nexs.grn.controller;

import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.GRNItemResponseDTO;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.dto.response.Result;
import com.lenskart.nexs.grn.dto.response.ScanItemResponseDTO;
import com.lenskart.nexs.grn.exception.InvalidRequestException;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.service.GRNItemService;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class GRNItemControllerTest {

    @Mock
    private GRNItemService grnItemService;

    @InjectMocks
    private GRNItemController grnItemController;

    @BeforeEach
    public void setUp() {

    }

    //@Test
    public void testScanGrnItem() throws Exception {
        GRNItemDTO grnItemDTO = new GRNItemDTO();
        GRNItemResponseDTO grnItemResponseDTO = new GRNItemResponseDTO();
        grnItemResponseDTO.setBarcode("Barcode1");
        grnItemResponseDTO.setGrnCode("grn");
        ResponseDTO<Result<Map<String, Object>>> response = new ResponseDTO<>();
        Result<Map<String, Object>> result = new Result<>();
        ScanItemResponseDTO scanItemResponseDTO = new ScanItemResponseDTO();
        Mockito.when(grnItemService.scanGRNItem(grnItemDTO)).thenReturn(scanItemResponseDTO);
        ResponseDTO<Result<ScanItemResponseDTO>> responseDTO = grnItemController.scanGrnItem("LKH03", grnItemDTO,"false");
        assertNotNull(responseDTO);
    }

    //@Test
    public void testUpdateGrnItem() throws Exception {
        UpdateGRNItemDTO updateGRNItemDTO = new UpdateGRNItemDTO();
        //ResponseDTO<GRNMasterMetaDTO> responseDTO = new ResponseDTO<>();
        ResponseDTO<Result<Map<String, Object>>> responseDTO = new ResponseDTO<>();
        Mockito.when(grnItemService.updateItem(updateGRNItemDTO)).thenReturn(responseDTO);
        ResponseDTO<Result<Map<String, Object>>> response = grnItemController.updateGrnItem("LKH03",updateGRNItemDTO,"false");
        assertNotNull(response);
    }

    //@Test
    public void testSearchGrnItem() throws Exception {
        ResponseDTO<Result<Map<String, Object>>> responseDTO = new ResponseDTO<>();
        GRNItem grnItem = new GRNItem();
        Mockito.when(grnItemService.searchItem("barcode","facilityCode", false)).thenReturn(grnItem);
        ResponseDTO<Result<GRNItem>> response = grnItemController.searchGrnItem("barcode", "facilityCode");
        assertNotNull(response);
    }

    //@Test
    public void testDeleteGrnItem() throws Exception {
        ResponseDTO<Result<Map<String, Object>>> responseDTO = new ResponseDTO<>();
        Mockito.doNothing().when(grnItemService).deleteItem("barcode", null, InvoiceLevelEnum.ITEM.name(),"poId");
        ResponseDTO<Result<Object>> response = grnItemController.deleteGrnItem("barcode","poId", null,InvoiceLevelEnum.ITEM.name(),"false");
        assertNotNull(response);
    }
}
