package com.lenskart.nexs.grn.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import com.lenskart.nexs.grn.model.GRNMaster;
import org.hamcrest.CoreMatchers;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.grn.constants.GRNConstants;

@ExtendWith(MockitoExtension.class)
@WebMvcTest(GRNMasterController.class)
public class GRNControllerTest {

	@Autowired
	private MockMvc mvc;

	/*//@Test
	public void testSuccessWithMandatoryParams() throws Exception {
		GRNMaster grnMaster = new GRNMaster();
		
		HashMap<String, Object> estQty1 = new HashMap<String, Object>();

		estQty1.put("12345", 10);
		estQty1.put("6789", 100);
		
		masterGRN.setEstimatedTotalQuantity(estQty1);
		masterGRN.setFacility("LK-0001");
		masterGRN.setGrnCode("GRN001");
		masterGRN.setGrnStatus(GRNConstants.GRN_STATUS_QC_PENDING);
		masterGRN.setGrnSyncedToNAV(0);
		masterGRN.setGrnSyncedToUnicom(0);
		HashMap<String, Object> poMap1 = new HashMap<String, Object>();
		poMap1.put("qty", 10);
		poMap1.put("pid", "12345");
		HashMap<String, Object> poMap2 = new HashMap<String, Object>();
		poMap2.put("qty", 100);
		poMap2.put("pid", "6789");

		@SuppressWarnings("rawtypes")
		ArrayList<Map> arrayList = new ArrayList<Map>();
		arrayList.add(poMap1);
		arrayList.add(poMap2);
		grnMaster.setPurchaseOrder(arrayList);
		grnMaster.setInvoice(arrayList);
		grnMaster.setPoId("PO/2021/123456");
		grnMaster.setInvoiceId("Invoice/2021/123456");
		grnMaster.setCreatedBy("<EMAIL>");
		grnMaster.setUpdatedBy("<EMAIL>");
		grnMaster.setUnicomGrnCode("UNICOM-GRN-0001");
		
		mvc.perform(MockMvcRequestBuilders.post("/api/grn/v1/master/create").content(new ObjectMapper().writeValueAsString(masterGRN)).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andDo(MockMvcResultHandlers.print())
				.andExpect(MockMvcResultMatchers.jsonPath("$.result", Matchers.hasSize(1)))
				.andExpect(MockMvcResultMatchers.jsonPath("$.result.[0].grn_code", CoreMatchers.notNullValue()))
				.andReturn();
	}*/

}
