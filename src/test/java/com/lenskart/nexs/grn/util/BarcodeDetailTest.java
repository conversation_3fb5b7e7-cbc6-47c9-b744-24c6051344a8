package com.lenskart.nexs.grn.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class BarcodeDetailTest {

    //@Test
    public void testGetAlpha() {
        assertEquals("AAA", BarcodeUtils.getAlpha("AAA00001"));
    }
}
