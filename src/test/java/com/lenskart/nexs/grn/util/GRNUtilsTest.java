package com.lenskart.nexs.grn.util;

import com.lenskart.nexs.common.entity.po.grn.GrnNumGenEntity;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.CreateGRNPIDTO;
import com.lenskart.nexs.grn.model.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import org.springframework.web.server.ResponseStatusException;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class GRNUtilsTest {

    /**
     * Method under test: {@link GRNUtils#getGRNSequence(String, String, GrnNumGenEntity)}
     */
    @Test
    void testGetGRNSequence() throws Exception {
        GrnNumGenEntity grnNumGenEntity = new GrnNumGenEntity();
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        grnNumGenEntity.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        grnNumGenEntity.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        grnNumGenEntity.setEnabled(true);
        grnNumGenEntity.setFacilityCode("Facility Code");
        grnNumGenEntity.setGrnNumGen("Grn Num Gen");
        grnNumGenEntity.setId(123L);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        grnNumGenEntity.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        grnNumGenEntity.setUpdatedBy("2020-03-01");
        assertThrows(ResponseStatusException.class,
                () -> GRNUtils.getGRNSequence("Env Prefix", "Key Prefix", grnNumGenEntity));
    }

    //@Test
    public void testGetMasterResponseDTO() {
        GRNMasterMetaDTO grnMasterMetaDTO = new GRNMasterMetaDTO();
        Invoice invoice = new Invoice();
        List<Product> pids = new ArrayList<>();
        Product pid = new Product();
        pids.add(pid);
        invoice.setPids(pids);
        invoice.setVendor("vendor2");
        invoice.setInvoiceRefNum("INV1234");
        invoice.setPoId("PO1234");
        invoice.setInvoiceDate("2020-06-19T09:08:50.160Z");
        grnMasterMetaDTO.setInvoice(invoice);
        GRNMaster grnMaster = new GRNMaster();
        assertNotNull(GRNUtils.getMasterResponseDTO(grnMasterMetaDTO, grnMaster, null, "Success", "Success", "NA"));
    }

    //@Test
    public void testMapToUnicomGRNDTO() {
        GRNMaster grnMaster = new GRNMaster();
        Invoice invoice = new Invoice();
        invoice.setInvoiceDate("2020-06-11");
        grnMaster.setInvoice(invoice);
        assertNotNull(GRNUtils.mapToUnicomGRNDTO(grnMaster));
    }

    //@Test
    public void testGetGRNPIDMaster() {
        CreateGRNPIDTO createGRNPIDTO = new CreateGRNPIDTO();
        GRNMasterMetaDTO meta = new GRNMasterMetaDTO();
        createGRNPIDTO.setGrnCode("grn1");
        Invoice invoice = new Invoice();
        List<Product> products = new ArrayList<>();
        Product product = new Product();
        product.setPid("P1");
        product.setBrand("VC");
        product.setCategoryId(1111);
        products.add(product);
        invoice.setPids(products);
        invoice.setVendor("Vendor-1");
        meta.setInvoice(invoice);
        PurchaseOrder po = new PurchaseOrder();
        List<POProduct> poProducts = new ArrayList<>();
        POProduct poProduct = new POProduct();
        poProduct.setPid("P1");
        poProducts.add(poProduct);
        po.setPids(poProducts);
        meta.setPo(po);
        createGRNPIDTO.setMeta(meta);
        PIDEstimatedQty pid = new PIDEstimatedQty("P1", 10L);
        createGRNPIDTO.setPid(pid);
        assertNotNull(GRNUtils.getGRNPIDMaster(createGRNPIDTO, (short) 0, null));
    }

    //@Test
    public void testGetVendorInfoList() {
        GRNMaster grnMaster = new GRNMaster();
        Invoice invoice = new Invoice();
        List<Product> products = new ArrayList<>();
        Product product = new Product();
        product.setPid("P1");
        product.setBrand("VC");
        product.setCategoryId(1111);
        products.add(product);
        invoice.setPids(products);
        invoice.setVendor("Vendor-1");
        grnMaster.setInvoice(invoice);
        List<PIDEstimatedQty> estimatedTotalQuantity = new ArrayList<>();
        PIDEstimatedQty pidEstimatedQty = new PIDEstimatedQty("P1", 10L);
        estimatedTotalQuantity.add(pidEstimatedQty);
//        grnMaster.setEstimatedTotalQuantity(estimatedTotalQuantity);
//        assertNotNull(GRNUtils.getVendorInfoList(grnMaster));
    }
}
