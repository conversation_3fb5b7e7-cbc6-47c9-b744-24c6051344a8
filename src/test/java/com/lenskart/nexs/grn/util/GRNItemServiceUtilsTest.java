package com.lenskart.nexs.grn.util;

import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.response.ScanItemResponseDTO;
import com.lenskart.nexs.grn.dto.response.VendorSTDResponseDTO;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.Product;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class GRNItemServiceUtilsTest {

    //@Test
    public void testConvertDtoToGRNItem() {
        GRNItemDTO grnItemDTO = new GRNItemDTO();
        GRNMasterMetaDTO meta = new GRNMasterMetaDTO();
        Invoice invoice = new Invoice();
        meta.setInvoice(invoice);
        List<Product> products = new ArrayList<>();
        Product product = new Product();
        product.setPid("P1");
        product.setBrand("VC");
        product.setCategoryId(1111);
        product.setQuantity(100L);
        products.add(product);
        invoice.setPids(products);
        invoice.setVendor("Vendor-1");
        List<Map<String, VendorSTDResponseDTO>> grnPids = new ArrayList<>();
        Map<String, VendorSTDResponseDTO> map = new HashMap<>();
        VendorSTDResponseDTO std = new VendorSTDResponseDTO();
//        std.setEstimatedQty(100L);
        map.put("P1", std);
        grnPids.add(map);
        //meta.setGrnPIds(grnPids);
//        grnItemDTO.setMeta(meta);
        grnItemDTO.setPid("P1");
        assertNotNull(GRNItemServiceUtils.convertDtoToGRNItem(grnItemDTO));
    }

    //@Test
    public void testConvertToResponseDTO() {
        GRNItem grnItem = new GRNItem();
        assertNotNull(GRNItemServiceUtils.convertToResponseDTO(grnItem, 0, 0, 0, new ScanItemResponseDTO()));
    }
}
