package com.lenskart.nexs.grn.util;

import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.model.QCMaster;
import com.lenskart.nexs.grn.model.QcConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

import java.util.HashSet;
import java.util.Set;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class CacheUtilsTest implements GRNConstants {

    GRNItem grnItem = new GRNItem();

    @BeforeEach
    public void setUp() {
        grnItem.setCategoryId(1111);
        grnItem.setVendorCode("vendor");
        grnItem.setBrand("brand");
        grnItem.setPid("pid");
    }

    //@Test
    public void testGetMasterKeyException() {
        grnItem.setCategoryId(0);
        assertThrows(ApplicationException.class, () -> CacheUtils.getMasterKey("local:nexs:grn:", grnItem));
    }

    //@Test
    public void testGetMasterKey() {
        StringBuilder build = new StringBuilder();
        build.append("local:nexs:grn:").append(QC_MASTER_4).append(grnItem.getCategoryId()).append(DEL)
                .append(grnItem.getBrand()).append(DEL).append(grnItem.getVendorCode()).append(DEL).append(grnItem.getPid());
        assertEquals(build.toString(), CacheUtils.getMasterKey("local:nexs:grn:", grnItem));
    }

    //@Test
    public void testGetGradientKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getGradientKey("local:nexs:grn:", 0));
    }

    //@Test
    public void testGetGradientKey() {
        StringBuilder build = new StringBuilder().append("local:nexs:grn:").append(QC_GRADIENT).append("1111");
        assertEquals(build.toString(), CacheUtils.getGradientKey("local:nexs:grn:", 1111));
    }

    //@Test
    public void testGetGrnConfigKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getGrnConfigKey("local:nexs:grn:","grn", ""));
    }

    //@Test
    public void testGetGrnConfigKey() {
        StringBuilder build = new StringBuilder().append("local:nexs:grn:").append("grn1").append(DEL).append("P1").append(DEL).append(QC_CONFIG);
        assertEquals(build.toString(), CacheUtils.getGrnConfigKey("local:nexs:grn:","grn1", "P1"));
    }

    //@Test
    public void testGetInvoiceConfigKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getItemConfigKey("local:nexs:grn:", "grn", ""));
    }

    //@Test
    public void testGetInvoiceConfigKey() {
        StringBuilder build = new StringBuilder().append("local:nexs:grn:").append(INVOICE).append(DEL).append("I1").append(DEL).append("P1").append(DEL).append(QC_CONFIG);
        assertEquals(build.toString(), CacheUtils.getItemConfigKey("local:nexs:grn:", "I1", "P1"));
    }

    //@Test
    public void testGetScanCountKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getScanCountKey("local:nexs:grn:", "grn", ""));
    }

    //@Test
    public void testGetScanCountKey() {
        StringBuilder build = new StringBuilder().append("local:nexs:grn:").append("I1").append(DEL).append("P").append(DEL).append(TOTAL_SCANNED);
        assertEquals(build.toString(), CacheUtils.getScanCountKey("local:nexs:grn:", "I1", "P"));
    }

    //@Test
    public void testGetFailCountKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getFailCountKey("local:nexs:grn:", "grn", ""));
    }

    //@Test
    public void testGetFailCountKey() {
        StringBuilder build = new StringBuilder().append("local:nexs:grn:").append("I1").append(DEL).append("P").append(DEL).append(TOTAL_FAILED);
        assertEquals(build.toString(), CacheUtils.getFailCountKey("local:nexs:grn:", "I1", "P"));
    }

    //@Test
    public void testGetStatusKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getStatusKey("local:nexs:grn:", "grn", ""));
    }

    //@Test
    public void testGetStatusKey() {
        StringBuilder build = new StringBuilder().append("local:nexs:grn:").append("I1").append(DEL).append("P").append(DEL).append(STATUS);
        assertEquals(build.toString(), CacheUtils.getStatusKey("local:nexs:grn:", "I1", "P"));
    }

    //@Test
    public void testGetQcConfigForInvoice() {
        Set<QCMaster> set = new HashSet<>();
        QCMaster master = new QCMaster();
//        master.setSamplingPercent(15);
        master.setFailurePercent(30);
        set.add(master);
//        QcConfig qcConfig = CacheUtils.getQcConfig(set, 100);
//        assertEquals(15, qcConfig.getSamplingPercent());
//        assertEquals(30, qcConfig.getFailurePercent());
    }

    //@Test
    public void testGetQcConfigForGrn() {
        Set<QCMaster> set = new HashSet<>();
        QCMaster master = new QCMaster();
//        master.setSamplingPercent(15);
        master.setFailurePercent(30);
        set.add(master);
//        QcConfig qcConfig = CacheUtils.getQcConfig(set, 100);
//        assertEquals(15, qcConfig.getSamplingPercent());
//        assertEquals(30, qcConfig.getFailurePercent());
    }

    //@Test
    public void testGetGRNManualOverrideKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getGRNManualOverrideKey("local:nexs:grn:", "", ""));
    }

    //@Test
    public void testGetGRNManualOverrideKey() {
        assertEquals("local:nexs:grn:g1:p1:MANUAL_OVERRIDE", CacheUtils.getGRNManualOverrideKey("local:nexs:grn:", "g1", "p1"));
    }

    //@Test
    public void testGetInvoiceScanCountKeyException() {
        assertThrows(ApplicationException.class, () -> CacheUtils.getItemScanCountKey("local:nexs:grn:", "", ""));
    }

    //@Test
    public void testGetInvoiceScanCountKey() {
        assertEquals("local:nexs:grn:invoice:i1:p1:TOTAL_SCANNED", CacheUtils.getItemScanCountKey("local:nexs:grn:", "i1", "p1"));
    }

    //@Test
    public void testGetASNKeyException() {
        assertThrows(ResponseStatusException.class, () -> CacheUtils.getASNKey("local:nexs:grn:", "", ""));
    }

    //@Test
    public void testGetASNKey() {
        assertEquals("local:nexs:grn:asn:po1:vendor1", CacheUtils.getASNKey("local:nexs:grn:", "po1", "vendor1"));
    }
}
