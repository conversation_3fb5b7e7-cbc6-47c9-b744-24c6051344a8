package com.lenskart.nexs.grn.util;

import static org.junit.Assert.assertEquals;

import java.util.Map;

import com.lenskart.nexs.fms.model.response.FacilityDetailsResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.lenskart.nexs.grn.config.GRNConfig;

@ExtendWith(MockitoExtension.class)
public class FacilityDetailsUtilsTest {
	
	@Mock
	private GRNConfig grnConfig;

	@InjectMocks
	private FacilityDetailsUtils facilityDetailsutils;
	
	//@Test
	public void testGetFacilityDetails() {
		String facilityCode = "LKH03";
		Mockito.when(grnConfig.getInvoiceBaseUrl()).thenReturn("https://nexs-qa.lenskart.com");
		Mockito.when(grnConfig.getFacilityDetailsUrl()).thenReturn("/nexs/api/facilities/v1/facility");
		
		FacilityDetailsResponse facilityDetailsResponse = facilityDetailsutils.getFacilityDetails(facilityCode);
		assertEquals(facilityCode, facilityDetailsResponse.getFacilityDetails().getFacilityCode());
		
	}
}
