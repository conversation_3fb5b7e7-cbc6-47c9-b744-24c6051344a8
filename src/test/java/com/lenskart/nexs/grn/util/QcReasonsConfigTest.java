package com.lenskart.nexs.grn.util;

import static org.junit.Assert.assertEquals;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import com.lenskart.nexs.grn.config.QcReasonsConfig;

@ExtendWith(MockitoExtension.class)
public class QcReasonsConfigTest {
	
	private QcReasonsConfig qcReasonsConfig = new QcReasonsConfig();
	
	@BeforeEach
	public void setQcReasons() {
		Map<String, String> qcReasons = new HashMap<>();
		qcReasons.put("qc_code_1", "Item is expired");
		qcReasons.put("qc_code_2", "Item is broken");
		qcReasonsConfig.setReasons(qcReasons);
	}
	
	//@Test
	public void testGetQcReasonForQcCodeFound() {
		
		assertEquals("Item is expired", qcReasonsConfig.getQcReason("qc_code_1"));
		assertEquals("Item is broken", qcReasonsConfig.getQcReason("qc_code_2"));
	}
	
	//@Test
	public void testGetQcReasonForQcCodeNotFound() {
		
		assertEquals("qc_code_3", qcReasonsConfig.getQcReason("qc_code_3"));
		assertEquals("NA", qcReasonsConfig.getQcReason(""));
	}
}
