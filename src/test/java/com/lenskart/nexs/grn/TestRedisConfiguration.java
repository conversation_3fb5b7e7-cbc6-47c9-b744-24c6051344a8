package com.lenskart.nexs.grn;

import java.util.Arrays;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@TestConfiguration
public class TestRedisConfiguration {
 
    
    @Value("${spring.redis.sentinel.master}") 
    private String master;
 
    @Value("${spring.redis.sentinel.nodes}") 
    private String nodes;
    
    @Bean
    public RedisProperties setRedisProps() {
    	RedisProperties redisProperties = new RedisProperties();
    	RedisProperties.Sentinel sentinel = new RedisProperties.Sentinel();
    	sentinel.setMaster(master);
    	sentinel.setNodes(Arrays.asList(nodes.split(",")));
    	redisProperties.setSentinel(sentinel);
		return redisProperties;
    }
    
    
}