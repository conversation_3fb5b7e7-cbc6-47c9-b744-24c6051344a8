package com.lenskart.nexs.grn.dao.impl;

import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class BoxDAOImplTest {

    @Mock
    private DBConfig dbConfig;

    @InjectMocks
    private BoxDAOImpl boxDAO;

//    //@Test
//    public void testCheckBoxExists() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true);
//        assertTrue(boxDAO.checkBoxExists("GRN1", "P1", "BXAL0001"));
//    }
//
//    //@Test
//    public void testCheckBoxExistsException() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> boxDAO.checkBoxExists("GRN1", "P1", "BXAL0001"));
//    }
//
//    //@Test
//    public void testCheckBoxExistsException1() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> boxDAO.checkBoxExists("GRN1", "P1", "BXAL0001"));
//    }
}
