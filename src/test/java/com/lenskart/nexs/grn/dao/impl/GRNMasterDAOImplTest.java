package com.lenskart.nexs.grn.dao.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.dto.request.GRNSearchDTO;
import com.lenskart.nexs.grn.dto.request.GRNUpdateDTO;
import com.lenskart.nexs.grn.dto.response.GRNDetailsDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.*;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class GRNMasterDAOImplTest {

    @Mock
    private DBConfig dbConfig;

    @Mock
    private GRNConfig grnConfig;

    @Mock
    private UserActivityDAOImpl userActivityDAO;

    @InjectMocks
    private GRNMasterDAOImpl grnMasterDAO;

    GRNMaster grnMaster = new GRNMaster();

    GRNUpdateDTO grnUpdateDTO = new GRNUpdateDTO();

    @BeforeEach
    public void setUp() {
        grnMaster.setGrnCode("grncode");
        grnMaster.setGrnStatus("created");
        grnMaster.setUnicomGrnCode("grncode");
    }

//    //@Test
//    public void testCreateGRNMaster() throws Exception {
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.execute()).thenReturn(true);
//        assertTrue(grnMasterDAO.createGRNMaster(grnMaster, connection));
//    }
//
//    //@Test
//    public void testCreateGRNMasterException() throws SQLException {
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.execute()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.createGRNMaster(grnMaster, connection));
//    }
//
//    //@Test
//    public void testUpdateGRNMaster() throws Exception {
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
//        assertEquals(1, grnMasterDAO.editGRNMaster(grnUpdateDTO, connection));
//    }
//
//    //@Test
//    public void testUpdateGRNMasterException() throws SQLException {
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.editGRNMaster(grnUpdateDTO, connection));
//    }
//
//    //@Test
//    public void testCloseGRNMaster() throws SQLException, JsonProcessingException {
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
//        assertEquals(1, grnMasterDAO.closeGRNMaster(grnMaster.getGrnCode(), grnMaster.getUpdatedBy(), connection));
//    }
//
//    //@Test
//    public void testCloseGRNMasterException() throws SQLException {
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.closeGRNMaster(grnMaster.getGrnCode(), grnMaster.getUpdatedBy(), connection));
//    }
//
//    //@Test
//    public void testGetGRNMaster() throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        Invoice invoice = new Invoice();
//        PurchaseOrder purchaseOrder = new PurchaseOrder();
//        Mockito.when(resultSet.getString("grn_code")).thenReturn("grn1");
//        Mockito.when(resultSet.getString("invoice")).thenReturn(mapper.writeValueAsString(invoice));
//        Mockito.when(resultSet.getString("po")).thenReturn(mapper.writeValueAsString(purchaseOrder));
//        Mockito.when(resultSet.getString("po_id")).thenReturn("po1");
//        Mockito.when(resultSet.getString("invoice_id")).thenReturn("in1");
//        Mockito.when(resultSet.getString("unicom_grn_code")).thenReturn("un1");
//        Mockito.when(resultSet.getString("grn_status")).thenReturn("created");
//        Mockito.when(resultSet.getString("pid")).thenReturn("pid");
//        GRNMaster grnMaster1 = grnMasterDAO.getGRNMaster(grnMaster.getGrnCode(), "User1");
//        assertNotNull(grnMaster1);
//    }
//
//    //@Test
//    public void testGetGRNMasterException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMaster(grnMaster.getGrnCode(), "User1"));
//    }
//
//    //@Test
//    public void testGetGRNMasterException2() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMaster(grnMaster.getGrnCode(), "User1"));
//    }
//    
//    //@Test
//    public void testUpdateEstimatedQtyException() throws SQLException {
//    	
//    	List<PIDEstimatedQty> pidEstimatedQtyList = new ArrayList<>();
//    	List<PIDEstimatedQtyHistory> pidEstimatedQtyHistory = new ArrayList<>();
//    	
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
////        assertThrows(ApplicationException.class, () -> grnMasterDAO.updateEstimatedQty("LKG123", pidEstimatedQtyList, pidEstimatedQtyHistory));
//    }
//    
//    //@Test
//    public void testUpdateEstimatedQtySucess() throws Exception {
//    	
//    	List<PIDEstimatedQty> pidEstimatedQtyList = new ArrayList<>();
//    	List<PIDEstimatedQtyHistory> pidEstimatedQtyHistory = new ArrayList<>();
//    	
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
////        assertTrue(grnMasterDAO.updateEstimatedQty("LKG123", pidEstimatedQtyList, pidEstimatedQtyHistory));
//    }
//
//    //@Test
//    public void testUpdateEstimatedQtyFailure() throws Exception {
//
//        List<PIDEstimatedQty> pidEstimatedQtyList = new ArrayList<>();
//        List<PIDEstimatedQtyHistory> pidEstimatedQtyHistory = new ArrayList<>();
//
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenReturn(0);
////        assertFalse(grnMasterDAO.updateEstimatedQty("LKG123", pidEstimatedQtyList, pidEstimatedQtyHistory));
//    }
//
//    //@Test
//    public void testGetGRNMasterGrnCode() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.first()).thenReturn(true);
//        ResultSetMetaData resultSetMetaData = Mockito.mock(ResultSetMetaData.class);
//        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
//        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(0);
//        GRNMaster grnMaster1 = grnMasterDAO.getGRNMaster(grnMaster.getGrnCode());
//        assertNotNull(grnMaster1);
//    }
//
//    //@Test
//    public void testGetGRNMasterGrnCodeException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMaster(grnMaster.getGrnCode()));
//    }
//
//    //@Test
//    public void testGetGRNMasterGrnCodeException2() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMaster(grnMaster.getGrnCode()));
//    }
//
//    //@Test
//    public void testGetGRNMasterNP() throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true);
//        Invoice invoice = new Invoice();
//        PurchaseOrder purchaseOrder = new PurchaseOrder();
//        Mockito.when(resultSet.getString("grn_code")).thenReturn("grn1");
//        Mockito.when(resultSet.getString("invoice")).thenReturn(mapper.writeValueAsString(invoice));
//        Mockito.when(resultSet.getString("po")).thenReturn(mapper.writeValueAsString(purchaseOrder));
//        Mockito.when(resultSet.getString("po_id")).thenReturn("po1");
//        Mockito.when(resultSet.getString("invoice_id")).thenReturn("in1");
//        Mockito.when(resultSet.getString("unicom_grn_code")).thenReturn("un1");
//        Mockito.when(resultSet.getString("grn_status")).thenReturn("created");
//        GRNMaster grnMaster1 = grnMasterDAO.getGRNMasterNP(grnMaster.getGrnCode(), "User1");
//        assertNotNull(grnMaster1);
//    }
//
//    //@Test
//    public void testGetGRNMasterNPException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMasterNP(grnMaster.getGrnCode(), "User1"));
//    }
//
//    //@Test
//    public void testGetGRNMasterNPException2() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMasterNP(grnMaster.getGrnCode(), "User1"));
//    }
//
//    //@Test
//    public void testCreateGRNException() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.createGRN(grnMaster));
//    }
//
//    //@Test
//    public void testCreateGRN() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.execute()).thenReturn(true);
//        Mockito.doNothing().when(userActivityDAO).save(Mockito.any(), Mockito.any());
//        assertTrue(grnMasterDAO.createGRN(grnMaster));
//    }
//
//    //@Test
//    public void testUpdateGRNException() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.editGRN(grnUpdateDTO));
//    }
//
//    //@Test
//    public void testUpdateGRN() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
//        Mockito.doNothing().when(userActivityDAO).save(Mockito.any(), Mockito.any());
//        assertEquals(1, grnMasterDAO.editGRN(grnUpdateDTO));
//    }
//
//    //@Test
//    public void testCloseGRNException() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.closeGRN("grnCode", "User1", "LKH03"));
//    }
//
//    //@Test
//    public void testCloseGRN() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
//        Mockito.doNothing().when(userActivityDAO).save(Mockito.any(), Mockito.any());
//        assertEquals(1, grnMasterDAO.closeGRN("grnCode", "User1", "LKH03"));
//    }
//
//    //@Test
//    public void testGRNMasterSummary() throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        Invoice invoice = new Invoice();
//        Mockito.when(resultSet.getString("grn_code")).thenReturn("grn1");
//        Mockito.when(resultSet.getString("invoice")).thenReturn(mapper.writeValueAsString(invoice));
//        Mockito.when(resultSet.getString("po_id")).thenReturn("po1");
//        Mockito.when(resultSet.getString("invoice_id")).thenReturn("in1");
//        Mockito.when(resultSet.getString("grn_status")).thenReturn("created");
//        Mockito.when(resultSet.getString("created_by")).thenReturn("User1");
//        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
//        GRNMaster grnMaster1 = grnMasterDAO.getGRNMasterSummary(grnMaster.getGrnCode(), "User1");
//        assertNotNull(grnMaster1);
//    }
//
//    //@Test
//    public void testGetGRNMasterSummaryException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMasterSummary(grnMaster.getGrnCode(), "User1"));
//    }
//
//    //@Test
//    public void testGetGRNMasterSummaryException2() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNMasterSummary(grnMaster.getGrnCode(), "User1"));
//    }
//
//    //@Test
//    public void testGetGRNByInvoiceAndUser() throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        ResultSetMetaData meta = Mockito.mock(ResultSetMetaData.class);
//        Mockito.when(resultSet.getMetaData()).thenReturn(meta);
//        Mockito.when(meta.getColumnCount()).thenReturn(0);
//        List<GRNMaster> grnMaster1 = grnMasterDAO.getGRNByInvoiceAndUser(grnMaster.getInvoiceId(), "User1", true);
//        assertNotNull(grnMaster1);
//    }
//
//    //@Test
//    public void testGetGRNByInvoiceAndUserNext() throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        ResultSetMetaData meta = Mockito.mock(ResultSetMetaData.class);
//        Mockito.when(resultSet.getMetaData()).thenReturn(meta);
//        Mockito.when(meta.getColumnCount()).thenReturn(0);
//        List<GRNMaster> grnMaster1 = grnMasterDAO.getGRNByInvoiceAndUser(grnMaster.getInvoiceId(), "User1", false);
//        assertNotNull(grnMaster1);
//    }
//
//    //@Test
//    public void testGetGRNByInvoiceAndUserException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNByInvoiceAndUser(grnMaster.getInvoiceId(), "User1", true));
//    }
//
//    //@Test
//    public void testGetGRNByInvoiceAndUserException2() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNByInvoiceAndUser(grnMaster.getInvoiceId(), "User1", true));
//    }
//
//    //@Test
//    public void testGetGRNLISTByInvoiceAndUser() throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        List<GRNDetailsDTO> grnMaster1 = grnMasterDAO.getGRNLISTByInvoiceAndUser(grnMaster.getInvoiceId(), "User1");
//        assertNotNull(grnMaster1);
//    }
//
//    //@Test
//    public void testGetGRNLISTByInvoiceAndUserException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNLISTByInvoiceAndUser(grnMaster.getInvoiceId(), "User1"));
//    }
//
//    //@Test
//    public void testGetGRNLISTByInvoiceAndUserException2() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getGRNLISTByInvoiceAndUser(grnMaster.getInvoiceId(), "User1"));
//    }
//
//    //@Test
//    public void testGetNotClosedGRNCount() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.first()).thenReturn(true);
//        int grnMaster1 = grnMasterDAO.getNotClosedGRNCount(grnMaster.getInvoiceId());
//        assertTrue(grnMaster1 == 0);
//    }
//
//    //@Test
//    public void testGetNotClosedGRNCountNext() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.first()).thenReturn(false);
//        int grnMaster1 = grnMasterDAO.getNotClosedGRNCount(grnMaster.getInvoiceId());
//        assertTrue(grnMaster1 == 0);
//    }
//
//    //@Test
//    public void testGetNotClosedGRNCountException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getNotClosedGRNCount(grnMaster.getInvoiceId()));
//    }
//
//    //@Test
//    public void testGetNotClosedGRNCountException2() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.getNotClosedGRNCount(grnMaster.getInvoiceId()));
//    }
//
//    //@Test
//    public void testSearchGRN() throws SQLException, JsonProcessingException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        ResultSetMetaData meta = Mockito.mock(ResultSetMetaData.class);
//        Mockito.when(resultSet.getMetaData()).thenReturn(meta);
//        Mockito.when(meta.getColumnCount()).thenReturn(0);
//        Mockito.when(grnConfig.getPageSize()).thenReturn(1);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        GRNSearchDTO grnSearchDTO = new GRNSearchDTO();
//        grnSearchDTO.setGrnCode("");
//        grnSearchDTO.setFacility("LKH03");
//        grnSearchDTO.setGrnStatus("PENDING");
//        grnSearchDTO.setPo("po");
//        grnSearchDTO.setInvoiceRefNumber("INV123");
//        grnSearchDTO.setVendor("vendor");
//        grnSearchDTO.setVendorInvoice("VI");
//        grnSearchDTO.setCreatedBy("User1");
//        List<GRNMasterDetails> grnList = grnMasterDAO.searchGRN(grnSearchDTO, true);
//        assertNotNull(grnList);
//    }
//
//    //@Test
//    public void testSearchGRN1() throws SQLException, JsonProcessingException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        ResultSetMetaData meta = Mockito.mock(ResultSetMetaData.class);
//        Mockito.when(resultSet.getMetaData()).thenReturn(meta);
//        Mockito.when(meta.getColumnCount()).thenReturn(0);
//        Mockito.when(grnConfig.getPageSize()).thenReturn(1);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        GRNSearchDTO grnSearchDTO = new GRNSearchDTO();
//        grnSearchDTO.setGrnCode("GRN1");
//        List<GRNMasterDetails> grnList = grnMasterDAO.searchGRN(grnSearchDTO, false);
//        assertNotNull(grnList);
//    }
//
//    //@Test
//    public void testSearchGRNException() throws SQLException, JsonProcessingException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        GRNSearchDTO grnSearchDTO = new GRNSearchDTO();
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.searchGRN(grnSearchDTO, true));
//    }
//
//    //@Test
//    public void testSearchGRNException1() throws SQLException, JsonProcessingException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
//        GRNSearchDTO grnSearchDTO = new GRNSearchDTO();
//        assertThrows(ApplicationException.class, () -> grnMasterDAO.searchGRN(grnSearchDTO, true));
//    }
}
