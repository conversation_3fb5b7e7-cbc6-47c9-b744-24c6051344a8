package com.lenskart.nexs.grn.dao.impl;

import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.Barcode;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class BarcodeDAOImplTest {

    @Mock
    private DBConfig dbConfig;

    @InjectMocks
    private BarcodeDAOImpl barcodeDAO;

//    //@Test
//    public void testGetBarcode() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSourceInventory()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        Mockito.when(resultSet.getString("from_series")).thenReturn("AAA00001");
//        Mockito.when(resultSet.getString("to_series")).thenReturn("AAA99999");
//        List<Barcode> barcodeList = barcodeDAO.getBarcode(true);
//        assertNotNull(barcodeList);
//        assertEquals("AAA00001", barcodeList.get(0).getFromSeries());
//    }
//
//    //@Test
//    public void testGetBarcodeException() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSourceInventory()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> barcodeDAO.getBarcode(true));
//    }
//
//    //@Test
//    public void testGetBarcodeException1() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSourceInventory()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> barcodeDAO.getBarcode(true));
//    }
//
//    //@Test
//    public void testGetBarcodeCount() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true);
//        Mockito.when(resultSet.getInt("COUNT(1)")).thenReturn(1);
//        int count = barcodeDAO.getBarcodeCount("AAA00001");
//        assertTrue(count == 1);
//    }
//
//    //@Test
//    public void testGetBarcodeCountZero() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(false);
//        int count = barcodeDAO.getBarcodeCount("AAA00001");
//        assertTrue(count == 0);
//    }
//
//    //@Test
//    public void testGetBarcodeCountException() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> barcodeDAO.getBarcodeCount("AAA00001"));
//    }
//
//    //@Test
//    public void testGetBarcodeCountException1() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> barcodeDAO.getBarcodeCount("AAA00001"));
//    }
//
//    //@Test
//    public void testGetBarcodeSeries() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        ResultSet resultSet = Mockito.mock(ResultSet.class);
//        Mockito.when(dbConfig.getDataSourceInventory()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
//        Mockito.when(resultSet.next()).thenReturn(true, false);
//        Mockito.when(resultSet.getString("from_series")).thenReturn("AAA00001");
//        Mockito.when(resultSet.getString("to_series")).thenReturn("AAA99999");
//        List<Barcode> barcodeList = barcodeDAO.getBarcodeSeries();
//        assertNotNull(barcodeList);
//        assertEquals("AAA00001", barcodeList.get(0).getFromSeries());
//    }
//
//    //@Test
//    public void testGetBarcodeSeriesException() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSourceInventory()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> barcodeDAO.getBarcodeSeries());
//    }
//
//    //@Test
//    public void testGetBarcodeSeriesException1() throws Exception {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSourceInventory()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> barcodeDAO.getBarcodeSeries());
//    }
}
