package com.lenskart.nexs.grn.dao.impl;

import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.service.RedisHandler;

import mockit.MockUp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class QcStatusDAOImplTest implements GRNConstants {

    @Mock
    private GRNConfig grnConfig;

    @InjectMocks
    private QcStatusDAOImpl qcStatusDAO;

    GRNItem grnItem = new GRNItem();

    @BeforeEach
    public void setUp() {
        grnItem.setInvoiceId("invoice1");
        grnItem.setGrnCode("grn1");
        grnItem.setPid("P1");
    }

//    //@Test
//    public void testHasInvoiceKey() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertFalse(qcStatusDAO.hasReferenceKey(grnItem.getInvoiceId(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testHasGrnKey() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertFalse(qcStatusDAO.hasGrnKey(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIncrementFailCountException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> qcStatusDAO.incrementFailCount(grnItem.getGrnCode(),
//                grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIncrementFailCount() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.incrementFailCount(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIncrementScanCountException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> qcStatusDAO.incrementScanCount(grnItem.getGrnCode(),
//                grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIncrementScanCount() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.incrementScanCount(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testGetTotalFailedNoKey() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> qcStatusDAO.getTotalFailed(grnItem.getGrnCode(),
//                grnItem.getPid()));
//    }
//
//    //@Test
//    public void testGetTotalFailed() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return 5;
//            }
//        };
//        assertEquals(5, qcStatusDAO.getTotalFailed(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testGetTotalFailedException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.getTotalFailed(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testSetGrnStatusException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> qcStatusDAO.setGrnStatus(grnItem.getGrnCode(),
//                grnItem.getPid(), PASSED));
//    }
//
//    //@Test
//    public void testSetGrnStatus() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), PASSED));
//    }
//
//    //@Test
//    public void testGetGrnStatusException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> qcStatusDAO.getGrnStatus(grnItem.getGrnCode(),
//                grnItem.getPid()));
//    }
//
//    //@Test
//    public void testGetGrnStatus() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.getGrnStatus(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIsChannelGreen() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return "PASSED";
//            }
//        };
//        assertTrue(qcStatusDAO.isChannelGreen(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIsQcStatusFailed() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return "FAILED";
//            }
//        };
//        assertTrue(qcStatusDAO.isQcStatusFailed(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIsGRNManualOverriden() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//        };
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        assertTrue(qcStatusDAO.isGRNManualOverriden(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIncrementInvoiceQtyCount() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.incrementItemQtyCount(grnItem.getInvoiceId(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testDecrementScanCount() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.decrementScanCount(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testDecrementScanCountException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> qcStatusDAO.decrementScanCount(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testDecrementInvoiceQtyCount() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.decrementItemQtyCount(grnItem.getInvoiceId(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testDecrementFailCount() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> qcStatusDAO.decrementFailCount(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testDecrementFailCountException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> qcStatusDAO.decrementFailCount(grnItem.getGrnCode(), grnItem.getPid()));
//    }
}
