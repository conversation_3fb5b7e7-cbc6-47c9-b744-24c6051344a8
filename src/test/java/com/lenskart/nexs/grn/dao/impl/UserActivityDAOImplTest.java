package com.lenskart.nexs.grn.dao.impl;

import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.UserActivity;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class UserActivityDAOImplTest {

    @Mock
    private DBConfig dbConfig;

    @InjectMocks
    private UserActivityDAOImpl userActivityDAO;

    UserActivity userActivity = new UserActivity();

//    //@Test
//    public void testAssignGRN() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.execute()).thenReturn(true);
//        userActivityDAO.assignGRN(userActivity);
//        Mockito.verify(preparedStatement, Mockito.times(1)).execute();
//    }
//
//    //@Test
//    public void testAssignGRNException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> userActivityDAO.assignGRN(userActivity));
//    }
//
//    //@Test
//    public void testSave() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.execute()).thenReturn(true);
//        userActivityDAO.save(userActivity);
//        Mockito.verify(preparedStatement, Mockito.times(1)).execute();
//    }
//
//    //@Test
//    public void testSaveException() throws SQLException {
//        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
//        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> userActivityDAO.save(userActivity));
//    }
//
//    //@Test
//    public void testSaveCon() throws SQLException {
//        Connection connection = Mockito.mock(Connection.class);
//        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
//        Mockito.when(preparedStatement.execute()).thenReturn(true);
//        userActivityDAO.save(userActivity, connection);
//        Mockito.verify(preparedStatement, Mockito.times(1)).execute();
//    }
//
//    //@Test
//    public void testSaveConException() throws SQLException {
//        Connection connection = Mockito.mock(Connection.class);
//        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> userActivityDAO.save(userActivity, connection));
//    }
}
