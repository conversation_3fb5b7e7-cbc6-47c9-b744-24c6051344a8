package com.lenskart.nexs.grn.dao.impl;

import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.GRNPidBarcodeDetailsDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.Box;
import com.lenskart.nexs.grn.model.BoxMapping;
import com.lenskart.nexs.grn.model.GRNItem;
//import com.lenskart.nexs.grn.model.QcConfig;
import com.lenskart.nexs.grn.service.CacheUpdateService;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class GRNItemDAOImplTest implements GRNConstants {

    @Mock
    private DBConfig dbConfig;

    @Mock
    private CacheUpdateService cacheUpdateService;

    @Mock
    private CacheDAO cacheDAO;

    @InjectMocks
    private GRNItemDAOImpl itemGrnDAO;

    GRNItem grnItem = new GRNItem();

    @BeforeEach
    public void setUp() {
        grnItem.setInvoiceRefNum("INV123");
        grnItem.setPid("P1");
        grnItem.setGrnCode("GRN1");
    }

    //@Test
    public void testSave() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.execute()).thenReturn(true);
        Mockito.when(cacheDAO.hasLockKey(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(cacheDAO).signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SAVE);
        Mockito.when(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())).thenReturn(grnItem.getGrnCode());
//        QcConfig qcConfig = new QcConfig();
        itemGrnDAO.save(grnItem, PENDING);
        Mockito.verify(preparedStatement, Mockito.times(1)).execute();
    }

    //@Test
    public void testSaveException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.execute()).thenThrow(SQLException.class);
        Mockito.when(cacheDAO.hasLockKey(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(cacheDAO).signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), SAVE_EX);
        Mockito.when(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())).thenReturn(grnItem.getGrnCode());
//        QcConfig qcConfig = new QcConfig();
        assertThrows(ApplicationException.class, () -> itemGrnDAO.save(grnItem, PENDING));
    }

    //@Test
    public void testGetGrnItem() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true);
        Mockito.when(resultSet.getString("qc_status")).thenReturn("created");
        GRNItem grnItem = itemGrnDAO.getGrnItem("grncode", "poId");
        assertNotNull(grnItem);
    }

    //@Test
    public void testGetGrnItemNull() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(false);
        GRNItem grnItem = itemGrnDAO.getGrnItem("grncode", "poId1");
        assertNull(grnItem);
    }

    //@Test
    public void testGetGrnItemException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGrnItem("grncode", "poId1"));
    }

    //@Test
    public void testGetGrnItemException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGrnItem("grncode", "poId1"));
    }

    //@Test
    public void testUpdateQcStatus() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
        itemGrnDAO.updateQcStatus("barcode", "pass", "poId1");
        Mockito.verify(preparedStatement, Mockito.times(1)).executeUpdate();
    }

    //@Test
    public void testUpdateQcStatusException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.updateQcStatus("barcode","pass", "poId1"));
    }

    //@Test
    public void testUpdateGRNItem() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
        Mockito.when(cacheDAO.hasLockKey(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(cacheDAO).signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), UPDATE);
        Mockito.when(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())).thenReturn(grnItem.getGrnCode());
//        QcConfig qcConfig = new QcConfig();
        UpdateGRNItemDTO updateGRNItemDTO = new UpdateGRNItemDTO();
        itemGrnDAO.updateGRNItem(updateGRNItemDTO, "PENDING", 10L, 10L, grnItem);
        Mockito.verify(preparedStatement, Mockito.times(1)).executeUpdate();
    }

    //@Test
    public void testUpdateGRNItemException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
        Mockito.when(cacheDAO.hasLockKey(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(cacheDAO).signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), UPDATE_EX);
        Mockito.when(cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid())).thenReturn(grnItem.getGrnCode());
//        QcConfig qcConfig = new QcConfig();
        Mockito.doNothing().when(cacheUpdateService).updateRecovery(grnItem, "PENDING", 10L, 10L);
        UpdateGRNItemDTO updateGRNItemDTO = new UpdateGRNItemDTO();
        assertThrows(ApplicationException.class, () -> itemGrnDAO.updateGRNItem(updateGRNItemDTO, "PENDING", 10L, 10L, grnItem));
    }

    //@Test
    public void testDeleteItem() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
        itemGrnDAO.deleteItem("barcode", "poId");
        Mockito.verify(preparedStatement, Mockito.times(1)).executeUpdate();
    }

    //@Test
    public void testDeleteItemException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.deleteItem("barcode", "POId2"));
    }

    //@Test
    public void testGetGrnItemDetails() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true);
        Mockito.when(resultSet.getString("grn_code")).thenReturn("GRN1");
        Mockito.when(resultSet.getString("po_id")).thenReturn("PO1");
        Mockito.when(resultSet.getString("vendor_code")).thenReturn("vendor1");
        Mockito.when(resultSet.getString("lot_no")).thenReturn("lot_no");
        Mockito.when(resultSet.getInt("qc_master_sampling")).thenReturn(10);
        Mockito.when(resultSet.getInt("failure_threshold_qc_master")).thenReturn(30);
        Mockito.when(resultSet.getString("status")).thenReturn("status");
        Mockito.when(resultSet.getString("facility")).thenReturn("facility");
        Mockito.when(resultSet.getString("pid")).thenReturn("pid");
        Mockito.when(resultSet.getInt("estimated_qty")).thenReturn(100);
        Mockito.when(resultSet.getString("invoice_ref_num")).thenReturn("invoice_ref_num");
        Mockito.when(resultSet.getTimestamp("created_at")).thenReturn(new Timestamp(System.currentTimeMillis()));
        Mockito.when(resultSet.getTimestamp("updated_at")).thenReturn(new Timestamp(System.currentTimeMillis()));
        Mockito.when(resultSet.getString("qc_pass_box_barcode")).thenReturn("pass_box");
        Mockito.when(resultSet.getString("barcode")).thenReturn("barcode1");
        Mockito.when(resultSet.getString("qc_status")).thenReturn("pass");
        Mockito.when(resultSet.getString("qc_reason")).thenReturn("na");
        Mockito.when(resultSet.getString("qc_fail_code")).thenReturn("101");
        Mockito.when(resultSet.getString("created_by")).thenReturn("User1");
        Mockito.when(resultSet.getString("updated_by")).thenReturn("User1");
        Mockito.when(resultSet.getTimestamp("expiry_date")).thenReturn(new Timestamp(System.currentTimeMillis()));
        GRNItem grnItem = itemGrnDAO.getGrnItemDetails("barcode1", "poId1");
        assertNotNull(grnItem);
    }

    //@Test
    public void testGetGrnItemDetailsNull() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(false);
        GRNItem grnItem = itemGrnDAO.getGrnItemDetails("barcode1", "poId1");
        assertNull(grnItem);
    }

    //@Test
    public void testGetGrnItemDetailsException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGrnItemDetails("barcode", "poId1"));
    }

    //@Test
    public void testGetGrnItemDetailsException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGrnItemDetails("barcode", "poId1"));
    }

    //@Test
    public void testGetTotalScanned() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("pid");
        Mockito.when(resultSet.getInt("COUNT(1)")).thenReturn(1);
        String[] strArray = new String[3];
        Map<String, Integer> map = itemGrnDAO.getTotalScanned("barcode1", strArray);
        assertNotNull(map);
    }

    //@Test
    public void testGetTotalScannedException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        String[] strArray = new String[3];
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getTotalScanned("barcode1", strArray));
    }

    //@Test
    public void testGetTotalScannedException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        String[] strArray = new String[3];
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getTotalScanned("barcode1", strArray));
    }

    //@Test
    public void testGetTotalFailed() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("pid");
        Mockito.when(resultSet.getInt("COUNT(1)")).thenReturn(1);
        String[] strArray = new String[3];
        Map<String, Integer> map = itemGrnDAO.getTotalFailed("barcode1", strArray);
        assertNotNull(map);
    }

    //@Test
    public void testGetTotalFailedException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        String[] strArray = new String[3];
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getTotalFailed("barcode1", strArray));
    }

    //@Test
    public void testGetTotalFailedException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        String[] strArray = new String[3];
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getTotalFailed("barcode1", strArray));
    }

    //@Test
    public void testGetCountByPIDStatus() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("pid");
        Mockito.when(resultSet.getInt("COUNT(1)")).thenReturn(1);
        List<String> pids = new ArrayList<>();
        pids.add("P1");
        Map<String, Integer> map = itemGrnDAO.getCountByPIDStatus("grn1", pids, QC_FAIL);
        assertNotNull(map);
    }

    //@Test
    public void testGetCountByPIDStatusException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        List<String> pids = new ArrayList<>();
        pids.add("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getCountByPIDStatus("grn1", pids, QC_FAIL));
    }

    //@Test
    public void testGetCountByPIDStatusException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        List<String> pids = new ArrayList<>();
        pids.add("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getCountByPIDStatus("grn1", pids, QC_FAIL));
    }
    
    //@Test
    public void testGRNItemScannedCount() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        Mockito.when(resultSet.getInt("total_count")).thenReturn(5);
        Mockito.when(resultSet.getInt("fail_count")).thenReturn(1);
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        List<String> pids = Arrays.asList("P1");
        Map<String, Object> map = itemGrnDAO.getGRNItemScannedCount("GRN1", pids);
        assertNotNull(map);
        assertTrue(map.containsKey("P1"));
    }

    //@Test
    public void testGRNItemScannedCountException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        List<String> pids = Arrays.asList("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGRNItemScannedCount("GRN1", pids));
    }

    //@Test
    public void testGRNItemScannedCountException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        List<String> pids = Arrays.asList("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGRNItemScannedCount("GRN1", pids));
    }

    //@Test
    public void testGetQcPassBoxWithItems() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("box_barcode")).thenReturn("NO_BOX");
        Mockito.when(resultSet.getString("barcode")).thenReturn("barcode1");
        Mockito.when(resultSet.getInt("channel_status")).thenReturn(1);
        Map<String, List<Map<String, Object>>> map = itemGrnDAO.getQcPassBoxWithItems("GRN1", "P1", true);
        assertNotNull(map);
        assertTrue(map.containsKey("NO_BOX"));
    }

    //@Test
    public void testGetQcPassBoxWithItemsException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getQcPassBoxWithItems("GRN1", "P1", true));
    }

    //@Test
    public void testGetQcPassBoxWithItemsException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getQcPassBoxWithItems("GRN1", "P1", true));
    }

    //@Test
    public void testGetQcFailBoxWithItems() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("qc_fail_code")).thenReturn("101");
        Mockito.when(resultSet.getString("qc_reason")).thenReturn("101");
        Mockito.when(resultSet.getString("barcode")).thenReturn("barcode1");
        Mockito.when(resultSet.getInt("channel_status")).thenReturn(1);
        List<Map<String, Object>> list = itemGrnDAO.getQcFailBoxWithItems("GRN1", "P1");
        assertNotNull(list);
        assertTrue(list.size() > 0);
    }

    //@Test
    public void testGetQcFailBoxWithItemsException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getQcFailBoxWithItems("GRN1", "P1"));
    }

    //@Test
    public void testGetQcFailBoxWithItemsException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getQcFailBoxWithItems("GRN1", "P1"));
    }

    //@Test
    public void testGetMisc() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("lot_no")).thenReturn("lot1");
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        Mockito.when(resultSet.getTimestamp("expiry_date")).thenReturn(new Timestamp(System.currentTimeMillis()));
        Mockito.when(resultSet.getInt("sample")).thenReturn(1);
        List<String> pids = Arrays.asList("P1");
        Map<String, Map<String, Object>> map = itemGrnDAO.getMisc("GRN1", pids);
        assertNotNull(map);
    }

    //@Test
    public void testGetMiscException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        List<String> pids = Arrays.asList("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getMisc("GRN1", pids));
    }

    //@Test
    public void testGetMiscException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        List<String> pids = Arrays.asList("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getMisc("GRN1", pids));
    }

    //@Test
    public void testGetFailedMap() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        Mockito.when(resultSet.getTimestamp("failed_on")).thenReturn(new Timestamp(System.currentTimeMillis()));
        List<String> pids = Arrays.asList("P1");
        Map<String, Timestamp> map = itemGrnDAO.getFailedMap("GRN1", pids);
        assertNotNull(map);
    }

    //@Test
    public void testGetFailedMapException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        List<String> pids = Arrays.asList("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getFailedMap("GRN1", pids));
    }

    //@Test
    public void testGetFailedMapException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        List<String> pids = Arrays.asList("P1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getFailedMap("GRN1", pids));
    }

    //@Test
    public void testGetBoxCount() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true);
        Mockito.when(resultSet.getInt("box_count")).thenReturn(1);
        int count = itemGrnDAO.getBoxCount("GRN1");
        assertTrue(count > 0);
    }

    //@Test
    public void testGetBoxCountFalse() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(false);
        int count = itemGrnDAO.getBoxCount("GRN1");
        assertTrue(count == 0);
    }

    //@Test
    public void testGetBoxCountException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getBoxCount("GRN1"));
    }

    //@Test
    public void testGetBoxCountException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getBoxCount("GRN1"));
    }

    //@Test
    public void testGetTotalScanByInvoice() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        Mockito.when(resultSet.getInt("total_scanned")).thenReturn(1);
        Mockito.when(resultSet.getString("grn_code")).thenReturn("GRN1");
        List<Map<String, Object>> list = itemGrnDAO.getTotalScanByInvoice("INV123", "P1");
        assertNotNull(list);
    }

    //@Test
    public void testGetTotalScanByInvoiceEmptyPid() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        Mockito.when(resultSet.getInt("total_scanned")).thenReturn(1);
        Mockito.when(resultSet.getString("grn_code")).thenReturn("GRN1");
        List<Map<String, Object>> list = itemGrnDAO.getTotalScanByInvoice("INV123", "");
        assertNotNull(list);
    }

    //@Test
    public void testGetTotalScanByInvoiceException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getTotalScanByInvoice("INV123", "P1"));
    }

    //@Test
    public void testGetTotalScanByInvoiceException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getTotalScanByInvoice("INV123", "P1"));
    }

    //@Test
    public void testGetPIDTotalScanByInvoice() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        Mockito.when(resultSet.getInt("total_scanned")).thenReturn(1);
        Map<String, Object> map = itemGrnDAO.getPIDTotalScanByInvoice("INV123", "P1");
        assertNotNull(map);
    }

    //@Test
    public void testGetPIDTotalScanByInvoiceEmptyPid() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        Mockito.when(resultSet.getInt("total_scanned")).thenReturn(1);
        Map<String, Object> map = itemGrnDAO.getPIDTotalScanByInvoice("INV123", "");
        assertNotNull(map);
    }

    //@Test
    public void testGetPIDTotalScanByInvoiceException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getPIDTotalScanByInvoice("INV123", "P1"));
    }

    //@Test
    public void testGetPIDTotalScanByInvoiceException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getPIDTotalScanByInvoice("INV123", "P1"));
    }

    //@Test
    public void testGetQcPassItems() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        Mockito.when(resultSet.getString("barcode")).thenReturn("NXA00001");
        Mockito.when(resultSet.getInt("channel_status")).thenReturn(1);
        Map<String, List<Map<String, Object>>> map = itemGrnDAO.getQcPassItems("GRN1", "");
        assertNotNull(map);
    }

    //@Test
    public void testGetQcPassItemsException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getQcPassItems("GRN1", "P1"));
    }

    //@Test
    public void testGetQcPassItemsException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getQcPassItems("GRN1", "P1"));
    }

    //@Test
    public void testGetEmptyBoxMappings() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        Mockito.when(resultSet.getString("barcode")).thenReturn("NXA00001");
        Mockito.when(resultSet.getString("pid")).thenReturn("p1");
        List<BoxMapping> list = itemGrnDAO.getEmptyBoxMappings("GRN1");
        assertNotNull(list);
    }

    //@Test
    public void testGetEmptyBoxMappingsException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getEmptyBoxMappings("GRN1"));
    }

    //@Test
    public void testGetEmptyBoxMappingsException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getEmptyBoxMappings("GRN1"));
    }

    //@Test
    public void testReleaseEmptyBoxes() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
        List<BoxMapping> emptyBoxes = new ArrayList<>();
        BoxMapping boxMapping = new BoxMapping("B1", "G1", "P1", "User1");
        boxMapping.setCreatedBy("User1");
        emptyBoxes.add(boxMapping);
        List<Box> boxes = new ArrayList<>();
        assertTrue(itemGrnDAO.releaseEmptyBoxes(emptyBoxes, boxes));
    }

    //@Test
    public void testReleaseEmptyBoxesException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        List<BoxMapping> emptyBoxes = new ArrayList<>();
        List<Box> boxes = new ArrayList<>();
        assertThrows(ApplicationException.class, () -> itemGrnDAO.releaseEmptyBoxes(emptyBoxes, boxes));
    }

    //@Test
    public void testUpdateBox() throws SQLException {
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
        List<BoxMapping> emptyBoxes = new ArrayList<>();
        assertThrows(ApplicationException.class, () -> itemGrnDAO.updateBox(emptyBoxes, connection));
    }

    //@Test
    public void testGetGRNScanDetails() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        List<String> grnList = new ArrayList<>();
        grnList.add("GRN1");
        Map<String,Map<String,Object>> map = itemGrnDAO.getGRNScanDetails(grnList);
        assertNotNull(map);
    }

    //@Test
    public void testGetGRNScanDetailsException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
        List<String> grnList = new ArrayList<>();
        grnList.add("GRN1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGRNScanDetails(grnList));
    }

    //@Test
    public void testGetGRNScanDetailsException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        List<String> grnList = new ArrayList<>();
        grnList.add("GRN1");
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGRNScanDetails(grnList));
    }

    //@Test
    public void testSaveBoxHistory() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        List<Box> grnList = new ArrayList<>();
        Box box = new Box();
        box.setBarcode("B1");
        box.setFacilityCode("LKH03");
        box.setLocation("L");
        box.setStatus("InUse");
        box.setEnabled(true);
        box.setUpdatedBy("User1");
        box.setCreatedBy("User1");
        grnList.add(box);
        assertTrue(itemGrnDAO.saveBoxHistory(grnList, connection));
    }

    //@Test
    public void testSaveBoxHistoryException() throws SQLException {
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        List<Box> grnList = new ArrayList<>();
        assertThrows(ApplicationException.class, () -> itemGrnDAO.saveBoxHistory(grnList, connection));
    }

    //@Test
    public void testSaveBoxHistoryException1() throws SQLException {
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLIntegrityConstraintViolationException.class);
        List<Box> grnList = new ArrayList<>();
        assertThrows(ResponseStatusException.class, () -> itemGrnDAO.saveBoxHistory(grnList, connection));
    }

    //@Test
    public void testGetGrnPidBarcodeDetails() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        ResultSetMetaData meta = Mockito.mock(ResultSetMetaData.class);
        Mockito.when(resultSet.getMetaData()).thenReturn(meta);
        Mockito.when(meta.getColumnCount()).thenReturn(0);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        List<GRNPidBarcodeDetailsDTO> map = itemGrnDAO.getGrnPidBarcodeDetails("GRN1");
        assertNotNull(map);
    }

    //@Test
    public void testGetGrnPidBarcodeDetailsException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGrnPidBarcodeDetails("GRN1"));
    }

    //@Test
    public void testGetGrnPidBarcodeDetailsException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> itemGrnDAO.getGrnPidBarcodeDetails("GRN1"));
    }
}
