package com.lenskart.nexs.grn.dao.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.dto.request.GRNPidSearchDTO;
import com.lenskart.nexs.grn.dto.response.PidSearchResponse;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.GRNPIDMaster;
import com.zaxxer.hikari.HikariDataSource;

import mockit.MockUp;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class GRNPIDDAOImplTest {

    @Mock
    private DBConfig dbConfig;
    
    @Mock
    private GRNConfig grnConfig;

    @InjectMocks
    private GRNPIDDAOImpl grnpiddao;

    GRNPIDMaster grnpidMaster = new GRNPIDMaster();

    @BeforeEach
    public void setUp() {
        grnpidMaster.setGrnCode("grn1");
        grnpidMaster.setPid("P1");
        grnpidMaster.setInvoiceId("invoiceId");
        grnpidMaster.setInvoiceReferenceNum("invoiceRefNum");
        grnpidMaster.setVendor("vendor-1");
        grnpidMaster.setBrand("VC");
        grnpidMaster.setCategoryId(1111);
        grnpidMaster.setCreatedBy("User1");
        grnpidMaster.setUpdatedBy("User1");
        grnpidMaster.setManualOverride((short)0);
        grnpidMaster.setEstimatedQuantity(10L);
        grnpidMaster.setEstimatedQuantityList(new ArrayList<>());
    }

    //@Test
    public void testSetManualOverride() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
        grnpiddao.setManualOverride("grncode", "pid", true);
        Mockito.verify(preparedStatement, Mockito.times(1)).executeUpdate();
    }

    //@Test
    public void testSetManualOverrideException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.setManualOverride("grncode", "pid", true));
    }

    //@Test
    public void testUpdatePidEstimatedQty() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenReturn(1);
//        grnpiddao.updatePidEstimatedQty("grncode", "pid", 1L, new ArrayList<>());
        Mockito.verify(preparedStatement, Mockito.times(1)).executeUpdate();
    }

    //@Test
    public void testUpdatePidEstimatedQtyException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeUpdate()).thenThrow(SQLException.class);
//        assertThrows(ApplicationException.class, () -> grnpiddao.updatePidEstimatedQty("grncode", "pid",1L, new ArrayList<>()));
    }

    //@Test
    public void testUpdatePidEstimatedQtyException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(RuntimeException.class);
//        assertThrows(ApplicationException.class, () -> grnpiddao.updatePidEstimatedQty("grncode", "pid",1L, new ArrayList<>()));
    }

    //@Test
    public void testUpdateGRNPIDMasterStatus() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.execute()).thenReturn(true);
        grnpiddao.updateGRNPIDMasterStatus("pending","grncode", "pid");
        Mockito.verify(preparedStatement, Mockito.times(1)).execute();
    }

    //@Test
    public void testUpdateGRNPIDMasterStatusException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.execute()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.updateGRNPIDMasterStatus("pending", "grncode", "pid"));
    }

    //@Test
    public void testCreateGRNPIDMaster() throws SQLException, JsonProcessingException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.execute()).thenReturn(true);
        assertTrue(grnpiddao.createGRNPIDMaster(grnpidMaster));
    }

    //@Test
    public void testCreateGRNPIDMasterException() throws SQLException, JsonProcessingException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.createGRNPIDMaster(grnpidMaster));
    }

    //@Test
    public void testCountManualOverride() throws SQLException, JsonProcessingException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true);
        Mockito.when(resultSet.getInt("COUNT(1)")).thenReturn(2);
        assertEquals(2, grnpiddao.countManualOverride("INV1234", "P1"));
    }

    //@Test
    public void testCountManualOverrideFalse() throws SQLException, JsonProcessingException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(false);
        assertEquals(0, grnpiddao.countManualOverride("INV1234", "P1"));
    }

    //@Test
    public void testCountManualOverrideException() throws SQLException, JsonProcessingException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.countManualOverride("INV1234", "P1"));
    }

    //@Test
    public void testCountManualOverrideException2() throws SQLException, JsonProcessingException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.countManualOverride("INV1234", "P1"));
    }

    //@Test
    public void testGetGRNPIDMaster() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        ResultSetMetaData resultSetMetaData = Mockito.mock(ResultSetMetaData.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.first()).thenReturn(true);
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(0);
        assertNotNull(grnpiddao.getGRNPIDMaster("grn1", "P1"));
    }

    //@Test
    public void testGetGRNPIDMasterException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.getGRNPIDMaster("grn1", "P1"));
    }

    //@Test
    public void testGetGRNPIDMasterException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.getGRNPIDMaster("grn1", "P1"));
    }

    //@Test
    public void testGetGRNPIDS() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        Mockito.when(resultSet.getString("grn_code")).thenReturn("grn1");
        Mockito.when(resultSet.getString("pid")).thenReturn("P1");
        Mockito.when(resultSet.getString("invoice_ref_num")).thenReturn("INV1234");
        assertNotNull(grnpiddao.getGRNPIDS("grn1"));
    }

    //@Test
    public void testGetGRNPIDSException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.getGRNPIDS("grn1"));
    }

    //@Test
    public void testGetGRNPIDSException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnpiddao.getGRNPIDS("grn1"));
    }

    //@Test
    public void testGetGRNPIDDetails() throws Exception {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, false);
        ResultSetMetaData meta = Mockito.mock(ResultSetMetaData.class);
        Mockito.when(resultSet.getMetaData()).thenReturn(meta);
        Mockito.when(meta.getColumnCount()).thenReturn(0);
        List<String> list = new ArrayList<>();
        list.add("GRN1");
        assertNotNull(grnpiddao.getGRNPIDDetails(list));
    }

    //@Test
    public void testGetGRNPIDDetailsException() throws Exception {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        List<String> list = new ArrayList<>();
        list.add("GRN1");
        assertThrows(ApplicationException.class, () -> grnpiddao.getGRNPIDDetails(list));
    }

    //@Test
    public void testGetGRNPIDDetailsException2() throws Exception {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenThrow(SQLException.class);
        List<String> list = new ArrayList<>();
        list.add("GRN1");
        assertThrows(ApplicationException.class, () -> grnpiddao.getGRNPIDDetails(list));
    }
    
	//@Test
	public void testgrnPidSearch() throws Exception {
		HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
		Connection connection = Mockito.mock(Connection.class);
		PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
		Mockito.when(grnConfig.getPageSize()).thenReturn(1);
		Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
		Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
		Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
		ResultSet resultSet = Mockito.mock(ResultSet.class);
		Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
		Mockito.when(resultSet.next()).thenAnswer(new Answer() {

			private boolean flag = false;

			public Object answer(InvocationOnMock invocation) {

				if (!flag) {
					flag = true;
					return true;
				}
				return false;
			}
		});

		new MockUp<DBUtils>() {
			@mockit.Mock
			public PidSearchResponse resultSetToPidSearchResponse(ResultSet rs) throws SQLException, JsonMappingException, JsonProcessingException {
				PidSearchResponse pidSearchResponse = new PidSearchResponse();

				pidSearchResponse.setGrnCode("GRN1");
				pidSearchResponse.setPid("P1");
				pidSearchResponse.setCreatedBy("User1");
//				pidSearchResponse.setEstimatedQty(100);
//				pidSearchResponse.setSamplingPercent(30);
				pidSearchResponse.setFailurePercent(30);
				pidSearchResponse.setQcPass(10);
				pidSearchResponse.setQcFail(5);
				pidSearchResponse.setInvoiceQty(500L);
				pidSearchResponse.setFailedSince("2 Days");
				return pidSearchResponse;
			}
		};
		List<PidSearchResponse> result = grnpiddao.grnPidSearch(new GRNPidSearchDTO());
		assertNotNull(result);
		assertTrue(result.size() > 0);
		assertNotNull(result.get(0));

	}
}
