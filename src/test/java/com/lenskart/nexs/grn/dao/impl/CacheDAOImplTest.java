package com.lenskart.nexs.grn.dao.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.dto.response.ASNResponseDTO;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.model.QCGradient;
import com.lenskart.nexs.grn.model.QCMaster;
//import com.lenskart.nexs.grn.model.QcConfig;
import com.lenskart.nexs.service.RedisHandler;
import mockit.MockUp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.server.ResponseStatusException;

import java.util.HashSet;
import java.util.Set;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class CacheDAOImplTest {

    @Mock
    private GRNConfig grnConfig;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @InjectMocks
    private CacheDAOImpl cacheDAO;

    GRNItem grnItem = new GRNItem();

    ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    public void setUp() {
        grnItem.setInvoiceId("invoice1");
        grnItem.setInvoiceRefNum("invoice1");
        grnItem.setGrnCode("grn1");
        grnItem.setCategoryId(1111);
        grnItem.setBrand("brand");
        grnItem.setVendorCode("vendor1");
        grnItem.setPid("P1");
//        grnItem.setGrnEstimatedQuantity(10);
        grnItem.setInvoiceQuantity(100);
    }

//    //@Test
//    public void testGetQcMaster() throws Exception {
//        QCMaster qcMaster = new QCMaster();
////        qcMaster.setSamplingPercent(15);
//        qcMaster.setFailurePercent(30);
//        Set<QCMaster> set = new HashSet<>();
//        set.add(qcMaster);
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
////        QcConfig qcConfig = cacheDAO.getQcMaster(grnItem, true);
////        QcConfig qcConfig1 = cacheDAO.getQcMaster(grnItem, false);
////        assertEquals(15, qcConfig.getSamplingPercent());
////        assertEquals(15, qcConfig1.getSamplingPercent());
//    }
//
//    //@Test
//    public void testGetQcMasterEmptySet() throws Exception {
//        Set<QCMaster> set = new HashSet<>();
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
////        assertNull(cacheDAO.getQcMaster(grnItem, true));
//    }
//
//    //@Test
//    public void testGetQcMasterException() {
//        Set<QCMaster> set = new HashSet<>();
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception("Exception");
//            }
//        };
////        assertThrows(ApplicationException.class, () -> cacheDAO.getQcMaster(grnItem, true));
//    }
//
//    //@Test
//    public void testGetQcGradient() throws Exception {
//        QCGradient qcGradient1 = new QCGradient();
////        qcGradient1.setSamplingPercent(15);
//        qcGradient1.setFailurePercent(30);
//        Set<QCGradient> set = new HashSet<>();
//        set.add(qcGradient1);
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        QCGradient qcGradient = cacheDAO.getQcGradient(grnItem, qcConfig);
////        assertEquals(15, qcGradient.getSamplingPercent());
//    }
//
//    //@Test
//    public void testGetQcGradientEmptySet() throws Exception {
//        Set<QCMaster> set = new HashSet<>();
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        assertNull(cacheDAO.getQcGradient(grnItem, qcConfig));
//    }
//
//    //@Test
//    public void testGetQcGradientException() {
//        Set<QCMaster> set = new HashSet<>();
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception("Exception");
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        assertThrows(ApplicationException.class, () -> cacheDAO.getQcGradient(grnItem, qcConfig));
//    }
//
//    //@Test
//    public void testLoadInvoiceConfig() throws Exception {
//        QCMaster qcMaster = new QCMaster();
////        qcMaster.setSamplingPercent(15);
//        qcMaster.setFailurePercent(30);
//        Set<QCMaster> set = new HashSet<>();
//        set.add(qcMaster);
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
//        cacheDAO.loadConfig(grnItem, true);
//    }
//
//    //@Test
//    public void testLoadGrnConfig() throws Exception {
//        QCMaster qcMaster = new QCMaster();
////        qcMaster.setSamplingPercent(15);
//        qcMaster.setFailurePercent(30);
//        Set<QCMaster> set = new HashSet<>();
//        set.add(qcMaster);
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
//        cacheDAO.loadConfig(grnItem, false);
//    }
//
//    //@Test
//    public void testGetInvoiceQcConfigException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
////        assertThrows(ApplicationException.class, () -> cacheDAO.getInvoiceQcConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode()));
//    }
//
//    //@Test
//    public void testGetInvoiceQcConfig() throws Exception {
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        Mockito.when(grnConfig.getLockTTL()).thenReturn(1000L);
//        ValueOperations<String, String> valueOperation = Mockito.mock(ValueOperations.class);
//        Mockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueOperation);
//        Mockito.when(valueOperation.setIfAbsent(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), Mockito.any())).thenReturn(true);
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//        };
////        QcConfig qcConfig = new QcConfig();
////        qcConfig.setSamplingPercent(15);
////        qcConfig.setFailurePercent(30);
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Object redisOps(String opsName, Object... args) throws Exception {
////                return mapper.writeValueAsString(qcConfig);
////            }
////        };
////        QcConfig qcConfig1 = cacheDAO.getInvoiceQcConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode());
////        assertEquals(15, qcConfig1.getSamplingPercent());
//    }
//
////    //@Test
////    public void testGetGrnQcConfigException() {
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Boolean hasKey(String key) { return false; }
////        };
////        assertThrows(ApplicationException.class, () -> cacheDAO.getGrnQcConfig(grnItem));
////    }
//
////    //@Test
////    public void testGetGrnQcConfigException2() {
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Boolean hasKey(String key) { return true; }
////        };
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Object redisOps(String opsName, Object... args) throws Exception {
////                return mapper.writeValueAsString("qcConfig");
////            }
////        };
////        assertThrows(ApplicationException.class, () -> cacheDAO.getGrnQcConfig(grnItem));
////    }
//
////    //@Test
////    public void testGetGrnQcConfig() throws Exception {
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Boolean hasKey(String key) { return true; }
////        };
////        QcConfig qcConfig = new QcConfig();
////        qcConfig.setSamplingPercent(15);
////        qcConfig.setFailurePercent(30);
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Object redisOps(String opsName, Object... args) throws Exception {
////                return mapper.writeValueAsString(qcConfig);
////            }
////        };
////        QcConfig qcConfig1 = cacheDAO.getGrnQcConfig(grnItem);
////        assertEquals(15, qcConfig1.getSamplingPercent());
////    }
//
//    //@Test
//    public void testSetConfigByEstimatedQty() {
////        QcConfig qcConfig = new QcConfig();
////        qcConfig.setSamplingPercent(15);
////        qcConfig.setFailurePercent(30);
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Object redisOps(String opsName, Object... args) throws Exception {
////                return mapper.writeValueAsString(qcConfig);
////            }
////        };
//        cacheDAO.initializeKeys("grnCode", "P1");
//    }
//
//    //@Test
//    public void testSetConfigByEstimatedQtyException() {
////        QcConfig qcConfig = new QcConfig();
////        qcConfig.setSamplingPercent(15);
////        qcConfig.setFailurePercent(30);
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception();
//            }
//        };
//        assertThrows(ApplicationException.class, () -> cacheDAO.initializeKeys("grnCode", "P1"));
//    }
//
//    //@Test
//    public void testSetConfigByEstimatedQtyNext() {
////        QcConfig qcConfig = new QcConfig();
////        qcConfig.setSamplingPercent(15);
////        qcConfig.setFailurePercent(30);
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Object redisOps(String opsName, Object... args) throws Exception {
////                return mapper.writeValueAsString(qcConfig);
////            }
////        };
//        cacheDAO.initializeKeys(grnItem, 0, 0);
//    }
//
//    //@Test
//    public void testSetConfigByEstimatedQtyNextException() {
////        QcConfig qcConfig = new QcConfig();
////        qcConfig.setSamplingPercent(15);
////        qcConfig.setFailurePercent(30);
////        new MockUp<RedisHandler>() {
////            @mockit.Mock
////            public Object redisOps(String opsName, Object... args) throws Exception {
////                throw new Exception();
////            }
////        };
//        assertThrows(ApplicationException.class, () -> cacheDAO.initializeKeys(grnItem, 0, 0));
//    }
//
//    //@Test
//    public void testGetConfig() {
//        Set<QCMaster> set = new HashSet<>();
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
////        QcConfig qcConfig = cacheDAO.getConfig(grnItem, true);
////        assertNull(qcConfig);
//    }
//
//    //@Test
//    public void testLoadConfigException() {
//        Set<QCMaster> set = new HashSet<>();
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
//        assertThrows(ApplicationException.class, () -> cacheDAO.loadConfig(grnItem, true));
//    }
//
//    //@Test
//    public void testSetNextGradientLevel() {
//        QCMaster qcMaster = new QCMaster();
////        qcMaster.setSamplingPercent(15);
//        qcMaster.setFailurePercent(30);
//        Set<QCMaster> set = new HashSet<>();
//        set.add(qcMaster);
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return mapper.writeValueAsString(set);
//            }
//        };
////        assertThrows(ApplicationException.class, () -> cacheDAO.setNextGradientLevel(grnItem));
//    }
//
//    //@Test
//    public void testGetASN() throws JsonProcessingException {
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        HashOperations hashOperations = Mockito.mock(HashOperations.class);
//        Mockito.when(stringRedisTemplate.opsForHash()).thenReturn(hashOperations);
//        ASNResponseDTO responseDTO = new ASNResponseDTO();
//        responseDTO.setExpiry("2022-05-01");
//        responseDTO.setLot("LOT123");
//        responseDTO.setProduct("131932");
//        Mockito.when(hashOperations.get("local:nexs:grn:asn:po1:vendor1", "barcode1")).thenReturn(mapper.writeValueAsString(responseDTO));
//        ASNResponseDTO responseDTO1 = cacheDAO.getASN("po1", "vendor1", "barcode1");
//        assertNotNull(responseDTO1);
//    }
//
//    //@Test
//    public void testGetASNException1() {
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        HashOperations hashOperations = Mockito.mock(HashOperations.class);
//        Mockito.when(stringRedisTemplate.opsForHash()).thenReturn(hashOperations);
//        ASNResponseDTO responseDTO = new ASNResponseDTO();
//        responseDTO.setExpiry("2022-05-01");
//        responseDTO.setLot("LOT123");
//        responseDTO.setProduct("131932");
//        Mockito.when(hashOperations.get("local:nexs:grn:asn:po1:vendor1", "barcode1")).thenReturn(null);
//        assertThrows(ResponseStatusException.class, () -> cacheDAO.getASN("po1", "vendor1", "barcode1"));
//    }
//
//    //@Test
//    public void testGetASNException2() {
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        HashOperations hashOperations = Mockito.mock(HashOperations.class);
//        Mockito.when(stringRedisTemplate.opsForHash()).thenReturn(hashOperations);
//        ASNResponseDTO responseDTO = new ASNResponseDTO();
//        responseDTO.setExpiry("2022-05-01");
//        responseDTO.setLot("LOT123");
//        responseDTO.setProduct("131932");
//        Mockito.when(hashOperations.get("local:nexs:grn:asn:po1:vendor1", "barcode1")).thenThrow(RuntimeException.class);
//        assertThrows(ApplicationException.class, () -> cacheDAO.getASN("po1", "vendor1", "barcode1"));
//    }
//
//    //@Test
//    public void testHasLockKey() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//        };
//        assertTrue(cacheDAO.hasLockKey(grnItem.getInvoiceRefNum(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testGetLockOwnerGRN() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) {
//                return "invoiceKey";
//            }
//        };
//        assertEquals("invoiceKey", cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testGetLockOwnerGRNException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return false; }
//        };
//        assertThrows(ResponseStatusException.class, () -> cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testGetLockOwnerGRNException1() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception("");
//            }
//        };
//        assertThrows(ApplicationException.class, () -> cacheDAO.getLockOwnerGRN(grnItem.getInvoiceRefNum(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testSignal() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception("");
//            }
//        };
//        assertThrows(ApplicationException.class, () -> cacheDAO.signal(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), "m1"));
//    }
//
//    //@Test
//    public void testWait() {
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        ValueOperations<String, String> valueOperation = Mockito.mock(ValueOperations.class);
//        Mockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueOperation);
//        Mockito.when(valueOperation.setIfAbsent(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), Mockito.any())).thenReturn(false, true);
//        cacheDAO.wait(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode(), "m1");
//        Mockito.verify(stringRedisTemplate, Mockito.times(2)).opsForValue();
//    }
//
//    //@Test
//    public void testIsBoxRequired() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) { return true; }
//        };
//        assertTrue(cacheDAO.isBoxRequired(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testIsBoxRequiredException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) throws Exception {
//                throw new Exception("");
//            }
//        };
//        assertThrows(ApplicationException.class, () -> cacheDAO.isBoxRequired(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testSetBoxRequired() {
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                return true;
//            }
//        };
//        cacheDAO.setBoxRequired(grnItem.getGrnCode(), grnItem.getPid());
//        Mockito.verify(grnConfig, Mockito.times(1)).getKeyPrefix();
//    }
//
//    //@Test
//    public void testSetBoxRequiredException() {
//        Mockito.when(grnConfig.getKeyPrefix()).thenReturn("local:nexs:grn:");
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception("");
//            }
//        };
//        assertThrows(ApplicationException.class, () -> cacheDAO.setBoxRequired(grnItem.getGrnCode(), grnItem.getPid()));
//    }
//
//    //@Test
//    public void testSetGRNConfig() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) {
//                return true;
//            }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) {
//                return true;
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        cacheDAO.setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), qcConfig);
//        Mockito.verify(grnConfig, Mockito.times(2)).getKeyPrefix();
//    }
//
//    //@Test
//    public void testSetGRNConfigException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) {
//                return false;
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        assertThrows(ResponseStatusException.class, () -> cacheDAO.setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), qcConfig));
//    }
//
//    //@Test
//    public void testSetGRNConfigException1() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) {
//                return true;
//            }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception("");
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        assertThrows(ApplicationException.class, () -> cacheDAO.setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), qcConfig));
//    }
//
//    //@Test
//    public void testSetInvoiceConfig() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) {
//                return true;
//            }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) {
//                return true;
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        cacheDAO.setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), qcConfig);
//        Mockito.verify(grnConfig, Mockito.times(2)).getKeyPrefix();
//    }
//
//    //@Test
//    public void testSetInvoiceConfigException() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) {
//                return false;
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        assertThrows(ResponseStatusException.class, () -> cacheDAO.setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), qcConfig));
//    }
//
//    //@Test
//    public void testSetInvoiceConfigException1() {
//        new MockUp<RedisHandler>() {
//            @mockit.Mock
//            public Boolean hasKey(String key) {
//                return true;
//            }
//            @mockit.Mock
//            public Object redisOps(String opsName, Object... args) throws Exception {
//                throw new Exception("");
//            }
//        };
////        QcConfig qcConfig = new QcConfig();
////        assertThrows(ApplicationException.class, () -> cacheDAO.setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), qcConfig));
//    }
}
