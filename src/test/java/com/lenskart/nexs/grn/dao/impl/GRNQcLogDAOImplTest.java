package com.lenskart.nexs.grn.dao.impl;

import com.lenskart.nexs.grn.db.DBConfig;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.GRNQcLog;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class GRNQcLogDAOImplTest {

    @Mock
    private DBConfig dbConfig;

    @InjectMocks
    private GRNQcLogDAOImpl grnQcLogDAO;

    GRNQcLog log = new GRNQcLog();

    //@Test
    public void testSaveLog() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.execute()).thenReturn(true);
        grnQcLogDAO.saveLog(log);
        Mockito.verify(preparedStatement, Mockito.times(1)).execute();
    }

    //@Test
    public void testSaveLogException() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.execute()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnQcLogDAO.saveLog(log));
    }

    //@Test
    public void testGetGRNQcLogByGRNAndPid() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        ResultSet resultSet = Mockito.mock(ResultSet.class);
        Mockito.when(preparedStatement.executeQuery()).thenReturn(resultSet);
        Mockito.when(resultSet.next()).thenReturn(true, true, false);
        Mockito.when(resultSet.getString("barcode")).thenReturn("barcode1");
        Mockito.when(resultSet.getString("reason")).thenReturn("101");
        List<Map<String, Object>> map = grnQcLogDAO.getGRNQcLogByGRNAndPid("GRN1", "");
        assertNotNull(map);
    }

    //@Test
    public void testGetGRNQcLogByGRNAndPidException1() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Connection connection = Mockito.mock(Connection.class);
        PreparedStatement preparedStatement = Mockito.mock(PreparedStatement.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenReturn(connection);
        Mockito.when(connection.prepareStatement(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(preparedStatement);
        Mockito.when(preparedStatement.executeQuery()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnQcLogDAO.getGRNQcLogByGRNAndPid("GRN1", "P1"));
    }

    //@Test
    public void testGetGRNQcLogByGRNAndPidException2() throws SQLException {
        HikariDataSource hikariDataSource = Mockito.mock(HikariDataSource.class);
        Mockito.when(dbConfig.getDataSource()).thenReturn(hikariDataSource);
        Mockito.when(hikariDataSource.getConnection()).thenThrow(SQLException.class);
        assertThrows(ApplicationException.class, () -> grnQcLogDAO.getGRNQcLogByGRNAndPid("GRN1", "P1"));
    }
}
