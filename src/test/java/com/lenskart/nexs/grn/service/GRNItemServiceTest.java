package com.lenskart.nexs.grn.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.GRNQcLogEvents;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dao.GRNMasterDAO;
import com.lenskart.nexs.grn.dto.response.Result;
import com.lenskart.nexs.grn.model.*;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;
import com.lenskart.nexs.grn.dao.GRNItemDAO;
import com.lenskart.nexs.grn.dao.QcStatusDAO;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.ResponseDTO;
import com.lenskart.nexs.grn.dto.response.VendorSTDResponseDTO;
import com.lenskart.nexs.grn.service.strategy.BarcodeValidation;
//import com.lenskart.nexs.grn.service.strategy.QcSampling;

@ExtendWith(MockitoExtension.class)
public class GRNItemServiceTest {

    @Mock
    private BarcodeValidation barcodeValidation;

//    @Mock
//    private QcSampling qcSampling;

	@Mock
	private GRNCacheService grnCacheService;

    @Mock
    private GRNItemDAO grnItemDAO;

	@Mock
	private GRNMasterDAO grnMasterDAO;

	@Mock
	private QcStatusDAO qcStatusDAO;

	@Mock
	private CacheDAO cacheDAO;

    @Mock
	private GRNQcLogService grnQcLogService;

    @Mock
	private CacheUpdateService cacheUpdateService;

    @InjectMocks
    private GRNItemService grnItemService;

    GRNItem grnItem = new GRNItem();

    @BeforeEach
    public void setUp() {
    	
    	GRNItemDTO grnItemDTO = new GRNItemDTO();
    	grnItemDTO.setBarcode("Barcode123");
    	grnItemDTO.setCreatedBy("test");
    	grnItemDTO.setExpiryDate(new Timestamp(System.currentTimeMillis()));
    	grnItemDTO.setGrnCode("LKG123");
    	grnItemDTO.setPid("PRO123");
    	grnItemDTO.setQcPassBoxBarcode("QCPASSBOX123");
    	
    	Product product = new Product();
    	product.setBrand("Rayban");
    	product.setCategoryId(1111);
    	product.setPid("PRO123");
    	
    	List<Product> productList = new ArrayList<>();
    	productList.add(product);
    	
    	Invoice invoice = new Invoice();
    	invoice.setVendor("GKB");
    	invoice.setPids(productList);
    	invoice.setInvoiceRefNum("INVOICE123");
    	
    	Map<String, VendorSTDResponseDTO> grnPids = new HashMap<>();
    	VendorSTDResponseDTO vendorSTDResponseDTO = new VendorSTDResponseDTO();
    	vendorSTDResponseDTO.setExpiryOffset(4);
    	vendorSTDResponseDTO.setExpiryFormat("yyyyMMdd");
//    	vendorSTDResponseDTO.setEstimatedQty(100L);
    	grnPids.put("PRO123", vendorSTDResponseDTO);
    	
    	List<Map<String, VendorSTDResponseDTO>> grnPidsll = new ArrayList<>();
    	grnPidsll.add(grnPids);
    	
    	GRNMasterMetaDTO meta = new GRNMasterMetaDTO();
    	//meta.setGrnPIds(grnPidsll);
    	meta.setInvoice(invoice);
//    	grnItemDTO.setMeta(meta);

    }

    //@Test
	public void testDeleteItemEmptyBarcodeEmpty() {
    	assertThrows(ResponseStatusException.class, () -> grnItemService.deleteItem("", null, InvoiceLevelEnum.ITEM.name(),"poId"));
	}

	//@Test
	public void testDeleteItemEmptyBarcode() throws Exception {
    	GRNItem grnItem1 = new GRNItem();
    	grnItem1.setGrnCode("GRN1");
    	grnItem1.setPid("P1");
    	grnItem1.setBarcode("AAA00001");
		Mockito.when(grnItemDAO.deleteItem("AAA00001", "POId1")).thenReturn(1);
		Mockito.when(grnItemDAO.getGrnItemDetails("AAA00001", "POId2")).thenReturn(grnItem1);
		Mockito.doNothing().when(grnQcLogService).logGRNQcAction("GRN1", "P1", "AAA00001", GRNQcLogEvents.DELETE);
		grnItemService.deleteItem("AAA00001", null, InvoiceLevelEnum.ITEM.name(), "poId");
		Mockito.verify(grnItemDAO, Mockito.times(1)).getGrnItemDetails("AAA00001", "POId3");
	}

	//@Test
	public void testDeleteItemEmptyBarcodeException() {
		Mockito.when(grnItemDAO.deleteItem("AAA00001", "POId1")).thenReturn(0);
		assertThrows(ResponseStatusException.class, () -> grnItemService.deleteItem("AAA00001", null, InvoiceLevelEnum.ITEM.name(), "poId"));
	}

	//@Test
	public void testSearchItemEmpty() {
		assertThrows(ResponseStatusException.class, () -> grnItemService.searchItem("","facilityCode", false));
	}

	//@Test
	public void testSearchItem() throws Exception {
    	GRNItem grnItem1 = new GRNItem();
		grnItem1.setGrnCode("GRN1");
		grnItem1.setPid("P1");
		GRNMaster grnMaster = new GRNMaster();
		Invoice invoice = new Invoice();
		List<Product> pids = new ArrayList<>();
		invoice.setPids(pids);
		grnMaster.setInvoice(invoice);
		Mockito.when(grnItemDAO.getGrnItemDetails("AAA00001", "poId1")).thenReturn(grnItem1);
		Mockito.when(grnMasterDAO.getGRNMaster(grnItem1.getGrnCode())).thenReturn(grnMaster);
		GRNItem grnItem = grnItemService.searchItem("AAA00001", "NXS1", false);
		assertNotNull(grnItem);
	}

	//@Test
	public void testSearchItemException() {
		Mockito.when(grnItemDAO.getGrnItemDetails("AAA00001", "poId1")).thenReturn(null);
		assertThrows(ResponseStatusException.class, () -> grnItemService.searchItem("AAA00001", "NXS1", false));
	}

	//@Test
	public void testUpdateItem() throws Exception {
    	GRNItem grnItem1 = new GRNItem();
    	grnItem1.setGrnCode("GRN1");
		UpdateGRNItemDTO updateGRNItemDTO = new UpdateGRNItemDTO();
		updateGRNItemDTO.setQcStatus("PENDING");
		updateGRNItemDTO.setBarcode("AAA00001");
//		QcConfig qcConfig = new QcConfig();
		GRNMaster grnMaster = new GRNMaster();
		Invoice invoice = new Invoice();
		List<Product> pids = new ArrayList<>();
		Product product = new Product();
		product.setPid("P1");
		product.setBrand("brand1");
		product.setCategoryId(1111);
		product.setQuantity(100L);
		pids.add(product);
		invoice.setPids(pids);
		grnMaster.setInvoice(invoice);
		Mockito.when(grnItemDAO.getGrnItemDetails("AAA00001", "poId1")).thenReturn(grnItem1);
		Mockito.when(grnMasterDAO.getGRNMaster(grnItem1.getGrnCode())).thenReturn(grnMaster);
		Mockito.doNothing().when(cacheUpdateService).updateCacheState(grnItem1, GRNConstants.PENDING, GRNConstants.UPDATE);
//		Mockito.when(cacheDAO.getInvoiceQcConfig(grnItem1.getInvoiceRefNum(), grnItem1.getPid(), grnItem1.getGrnCode())).thenReturn(qcConfig);
		Mockito.when(grnCacheService.updateCache(grnItem1)).thenReturn(true);
		ResponseDTO<Result<Map<String, Object>>> responseDTO = grnItemService.updateItem(updateGRNItemDTO);
		assertNotNull(responseDTO);
	}

	//@Test
	public void testUpdateItemNext() throws Exception {
		GRNItem grnItem1 = new GRNItem();
		grnItem1.setGrnCode("GRN1");
		grnItem1.setPid("P1");
		UpdateGRNItemDTO updateGRNItemDTO = new UpdateGRNItemDTO();
		updateGRNItemDTO.setQcStatus("PENDING");
		updateGRNItemDTO.setBarcode("AAA00001");
		updateGRNItemDTO.setBoxBarcode("BX00001");
		GRNMaster grnMaster = new GRNMaster();
		Invoice invoice = new Invoice();
		List<Product> pids = new ArrayList<>();
		Product product = new Product();
		product.setPid("P1");
		product.setBrand("brand1");
		product.setCategoryId(1111);
		product.setQuantity(100L);
		pids.add(product);
		invoice.setPids(pids);
		grnMaster.setInvoice(invoice);
//		QcConfig qcConfig = new QcConfig();
		Mockito.when(grnItemDAO.getGrnItemDetails("AAA00001", "poId1")).thenReturn(grnItem1);
		Mockito.when(grnMasterDAO.getGRNMaster(grnItem1.getGrnCode())).thenReturn(grnMaster);
		Mockito.doNothing().when(cacheUpdateService).updateCacheState(grnItem1, GRNConstants.PENDING, GRNConstants.UPDATE);
		Mockito.when(qcStatusDAO.getGrnStatus(grnItem1.getGrnCode(), "P1")).thenReturn("PENDING");
//		Mockito.when(cacheDAO.getInvoiceQcConfig(grnItem1.getInvoiceRefNum(), grnItem1.getPid(), grnItem1.getGrnCode())).thenReturn(qcConfig);
		Mockito.when(grnCacheService.updateCache(grnItem1)).thenReturn(true);
		ResponseDTO<Result<Map<String, Object>>> responseDTO = grnItemService.updateItem(updateGRNItemDTO);
		assertNotNull(responseDTO);
	}

	//@Test
	public void testUpdateItemException() {
		UpdateGRNItemDTO updateGRNItemDTO = new UpdateGRNItemDTO();
		assertThrows(ResponseStatusException.class, () -> grnItemService.updateItem(updateGRNItemDTO));
	}

	//@Test
	public void testUpdateItemException1() throws Exception {
		GRNItem grnItem1 = new GRNItem();
		grnItem1.setGrnCode("GRN1");
		UpdateGRNItemDTO updateGRNItemDTO = new UpdateGRNItemDTO();
		updateGRNItemDTO.setBarcode("AAA00001");
		Mockito.when(grnItemDAO.getGrnItemDetails("AAA00001", "poId1")).thenReturn(grnItem1);
		Mockito.when(grnMasterDAO.getGRNMaster("GRN1")).thenReturn(null);
		assertThrows(ResponseStatusException.class, () -> grnItemService.updateItem(updateGRNItemDTO));
	}

}
