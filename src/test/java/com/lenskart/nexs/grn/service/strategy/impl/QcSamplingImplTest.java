//package com.lenskart.nexs.grn.service.strategy.impl;
//
//import com.lenskart.nexs.grn.constants.GRNConstants;
//import com.lenskart.nexs.grn.dao.CacheDAO;
//import com.lenskart.nexs.grn.dao.GRNPIDDAO;
//import com.lenskart.nexs.grn.dao.QcStatusDAO;
//import com.lenskart.nexs.grn.exception.InvalidRequestException;
//import com.lenskart.nexs.grn.model.GRNItem;
////import com.lenskart.nexs.grn.model.QcConfig;
//import com.lenskart.nexs.grn.service.GRNCacheService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@ExtendWith(MockitoExtension.class)
//public class QcSamplingImplTest implements GRNConstants {
//
//    @Mock
//    private CacheDAO cacheDAO;
//
//    @Mock
//    private QcStatusDAO qcStatusDAO;
//
//    @InjectMocks
//    private QcSamplingImpl qcSampling;
//
//    @InjectMocks
//    private GRNCacheService grnCacheService;
//
//    @Mock
//   	private GRNPIDDAO grnPIDMasterDAO;
//
//    GRNItem grnItem = new GRNItem();
//
//    @BeforeEach
//    public void setUp() {
//        grnItem.setPid("P1");
//        grnItem.setGrnCode("grn1");
//        grnItem.setInvoiceId("invoice1");
//        grnItem.setQcStatus("fail");
//        grnItem.setGrnEstimatedQuantity(100L);
//    }
//
//    private QcConfig getQcConfig() {
//    	QcConfig qcConfig = new QcConfig();
//    	qcConfig.setSamplingPercent(10);
//    	return qcConfig;
//    }
//
//    //@Test
//    public void testLoadConfig() {
//        Mockito.when(qcStatusDAO.hasReferenceKey(grnItem.getInvoiceRefNum(), grnItem.getPid())).thenReturn(false);
//        Mockito.when(qcStatusDAO.hasGrnKey(grnItem.getGrnCode(), grnItem.getPid())).thenReturn(false);
//        Mockito.doNothing().when(cacheDAO).loadConfig(grnItem, true);
//        QcConfig qc = new QcConfig();
//        Mockito.when(cacheDAO.getInvoiceQcConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode())).thenReturn(qc);
//        grnCacheService.loadConfig(grnItem);
//        Mockito.verify(qcStatusDAO, Mockito.times(1)).hasReferenceKey(grnItem.getInvoiceRefNum(), grnItem.getPid());
//    }
//}
