package com.lenskart.nexs.grn.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderItemEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnD365SyncEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnItemEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnMasterEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnNumGenEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.IqcGrnProductEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceItemEntityService;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.IqcGrnProductEntity;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnNumGenEntity;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import com.lenskart.nexs.common.entity.repositories.grn.GrnNumGenRepository;
import com.lenskart.nexs.ems.model.AutoGrnItemScanDetails;
import com.lenskart.nexs.ems.model.AutoGrnScanDetails;
import com.lenskart.nexs.grn.dao.*;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dao.GRNItemDAO;
import com.lenskart.nexs.grn.dao.GRNMasterDAO;
import com.lenskart.nexs.grn.dao.GRNPIDDAO;
import com.lenskart.nexs.grn.dao.QcStatusDAO;
import com.lenskart.nexs.grn.dao.UserActivityDAO;
import com.lenskart.nexs.grn.db.DBUtils;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.CreateGRNMasterDTO;
import com.lenskart.nexs.grn.dto.request.EstimatedQtyChangeReq;
import com.lenskart.nexs.grn.dto.request.GRNSearchDTO;
import com.lenskart.nexs.grn.dto.response.GRNDetailsDTO;
import com.lenskart.nexs.grn.dto.response.GRNSearchResponseDTO;
import com.lenskart.nexs.grn.dto.response.SearchResponse;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.model.GRNMaster;
import com.lenskart.nexs.grn.model.Invoice;
import com.lenskart.nexs.grn.model.PurchaseOrder;
import com.lenskart.nexs.grn.putaway.service.PutawayService;
import com.lenskart.nexs.grn.util.CloseGrnCreateGrnItemPutawayHandler;
import com.lenskart.nexs.grn.util.FacilityDetailsUtils;
import com.lenskart.nexs.grn.util.GRNCacheServiceUtils;
import com.lenskart.nexs.putaway.constants.PutAwayAction;
import com.lenskart.nexs.putaway.constants.PutAwayType;
import com.lenskart.nexs.putaway.model.request.CreatePutAwayRequest;
import com.lenskart.nexs.putaway.model.request.CreatePutawayItemRequest;
import com.lenskart.nexs.putaway.model.response.CreatePutawayItemResponse;
import com.lenskart.nexs.putaway.model.response.CreatePutawayResponse;
import com.nexs.po.common.enums.IqcGrnProductStatusEnum;
import com.lenskart.nexs.service.PdfGeneratorService;

import java.util.ArrayList;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.Spy;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigInteger;
import java.net.SocketTimeoutException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

@ContextConfiguration(classes = {GRNMasterService.class, GRNConfig.class, StringRedisTemplate.class})
@ExtendWith(MockitoExtension.class)
public class GRNMasterServiceTest {

	@MockBean(name = "closeGrnCreateGrnItemPutawayHandler")
	private CloseGrnCreateGrnItemPutawayHandler closeGrnCreateGrnItemPutawayHandler;

	@MockBean
	private GRNCacheService gRNCacheService;

	@MockBean
	private GRNCacheServiceUtils gRNCacheServiceUtils;

	@MockBean(name = "GRNItemHDAOImpl")
	private GRNItemDAO gRNItemDAO;

	@MockBean
	private GRNItemService gRNItemService;

	@MockBean(name = "GRNMasterHDAOImpl")
	private GRNMasterDAO gRNMasterDAO;

	@Autowired
	private GRNMasterService gRNMasterService;

	@MockBean(name = "GrnPidHDAOImpl")
	private GRNPIDDAO gRNPIDDAO;

	@MockBean
	private GRNPIDMasterService gRNPIDMasterService;

	@MockBean
	private GRNQcLogService gRNQcLogService;

	@MockBean
	private GrnD365SyncEntityService grnD365SyncEntityService;

	@MockBean
	private GrnNumGenEntityService grnNumGenEntityService;

	@MockBean
	private GrnNumGenRepository grnNumGenRepository;

	@MockBean
	private Integer integer;

	@MockBean
	private IqcGrnProductEntityService iqcGrnProductEntityService;

	@MockBean
	private PdfGeneratorService pdfGeneratorService;

	@MockBean
	private PurchaseInvoiceItemEntityService purchaseInvoiceItemEntityService;

	@MockBean
	private PurchaseOrderItemEntityService purchaseOrderItemEntityService;

	@MockBean(name = "UserActivityHDAOImpl")
	private UserActivityDAO userActivityDAO;

	@MockBean
	private UserDetailsService userDetailsService;

	@Mock
	private GRNMasterDAO grnMasterDAO;

	@Mock
	private GRNPIDDAO grnpiddao;

	@Mock
	private StringRedisTemplate redisTemplate;

	@Mock
    private QcStatusDAO qcStatusDAO;

	@Mock
    private CacheDAO cacheDAO;

	@Mock
	private GRNItemService grnItemService;

	@Mock
	private GRNItemDAO grnItemDAO;

	@Mock
	private GrnItemEntityService grnItemEntityService;

	@Mock
	private PutawayService putawayService;

	@Mock
	private GRNPIDDAO grnPidDao;

	@Mock
	private GRNConfig grnConfig;

	@Spy
	@InjectMocks
	private GRNMasterService grnMasterService;

	@Mock
	private PurchaseInvoiceEntityService purchaseInvoiceEntityService;

	@Mock
	private PoInvoiceService poInvoiceService;

	@Mock
	private FacilityDetailsUtils facilityDetailsUtils;

	@Mock
	private GrnMasterEntityService grnMasterEntityService;

	@Test
	void testCreateGRN() throws Exception {
		GRNMasterService grnMasterService = new GRNMasterService();

		Invoice invoice = new Invoice();
		invoice.setApprovedAt(1L);
		invoice.setB2bInvoiceDate(1L);
		invoice.setBillOfEntryAmount(10.0d);
		invoice.setBillOfEntryDate(1L);
		invoice.setBillOfEntryNumber("42");
		invoice.setCurrency("GBP");
		invoice.setHandoverParty("Handover Party");
		invoice.setInvoiceDate("2020-03-01");
		invoice.setInvoiceId("42");
		invoice.setInvoiceRefNum("Invoice Ref Num");
		invoice.setInvoice_date(1L);
		invoice.setLegalOwner("Legal Owner");
		invoice.setPids(new ArrayList<>());
		invoice.setPoDate("2020-03-01");
		invoice.setPoId("42");
		invoice.setPreFilledInvoice(true);
		invoice.setSendToParty("Send To Party");
		invoice.setVendor("Vendor");
		invoice.setVendorName("Vendor Name");

		PurchaseOrder purchaseOrder = new PurchaseOrder();
		purchaseOrder.setPids(new ArrayList<>());
		purchaseOrder.setPoId("42");

		CreateGRNMasterDTO createGRNMasterDTO = new CreateGRNMasterDTO();
		createGRNMasterDTO.setFacilityCode("Facility Code");
		createGRNMasterDTO.setInvoice(invoice);
		createGRNMasterDTO.setPo(purchaseOrder);
		createGRNMasterDTO.setType("Type");
		assertThrows(ResponseStatusException.class,
				() -> grnMasterService.createGRN(createGRNMasterDTO, "Facility Code", "Grn Type"));
	}

	@Test
	void testAddGrnSequenceInDB() throws Exception {
		GRNMaster grnMaster = createGRNMaster();
		Invoice invoice = createInvoice();
		PurchaseOrder purchaseOrder = createPurchaseOrder();
		GrnNumGenEntity grnNumGenEntity = createGrnNumGenEntity();
		Mockito.when(grnNumGenEntityService.findTopByFacilityAndCreatedAtOrderByDescending("FAC1")).thenReturn(grnNumGenEntity);
		Mockito.when(grnConfig.getEnvPrefix()).thenReturn("L");
		Mockito.when(grnConfig.getKeyPrefix()).thenReturn("nexs:grn:");
		MDC.put("FACILITY_CODE", "FAC1");
		Mockito.doNothing().when(grnNumGenRepository).save(grnNumGenEntity);

		this.gRNMasterService.addGrnSequenceInDB("FAC1", grnMaster);
	}

	private GrnNumGenEntity createGrnNumGenEntity() {
		GrnNumGenEntity grnNumGenEntity= new GrnNumGenEntity();
		grnNumGenEntity.setGrnNumGen("PG0QNXS-24-00067");
		grnNumGenEntity.setFacilityCode("DK01");
		grnNumGenEntity.setCreatedBy("USER1");
		grnNumGenEntity.setCreatedAt(new Date());
		grnNumGenEntity.setEnabled(true);
		grnNumGenEntity.setId(1);
		grnNumGenEntity.setUpdatedBy("USER1");
		grnNumGenEntity.setUpdatedAt(new Date());
		return grnNumGenEntity;
	}

	private EstimatedQtyChangeReq getEstimatedQtyChangeReq() {

		EstimatedQtyChangeReq estimatedQtyChangeReq = new EstimatedQtyChangeReq();
//		estimatedQtyChangeReq.setEstimatedQty(200L);
		estimatedQtyChangeReq.setGrnCode("LKG123");
		estimatedQtyChangeReq.setPid("PRO123");
		Invoice invoice = new Invoice();
		Product product = new Product();
		product.setPid("PRO123");
		product.setQuantity(500L);
		List<Product> products = new ArrayList<>();
		products.add(product);
		invoice.setPids(products);
		GRNMasterMetaDTO meta = new GRNMasterMetaDTO();
		meta.setInvoice(invoice);
		estimatedQtyChangeReq.setMeta(meta);
		return estimatedQtyChangeReq;
	}

//	private QcConfig createQcConfig() {
//		QcConfig qcConfig = new QcConfig();
//		qcConfig.setFailurePercent(10);
//		qcConfig.setFailureQuantity(5);
//		qcConfig.setGradientOrder(0);
//		qcConfig.setQuantity(100);
//		qcConfig.setSamplingPercent(50);
//		qcConfig.setSamplingQuantity(50);
//		return qcConfig;
//	}

	private GRNSearchDTO createGrnSearchReq() {
		GRNSearchDTO grnSearchReq = new GRNSearchDTO();
		return grnSearchReq;
	}

	private GRNMaster createGRNMaster() {

		GRNMaster grnMaster = new GRNMaster();
		grnMaster.setCreatedAt(new Timestamp(System.currentTimeMillis()));
		grnMaster.setCreatedBy("User1");
		grnMaster.setGrnCode("GRN1");
		grnMaster.setGrnStatus("Created");
		grnMaster.setInvoiceId("INV123");
		grnMaster.setPoId("PO123");
		Invoice invoice = new Invoice();
		invoice.setInvoiceDate("2020-07-09T20:20:20");
		invoice.setInvoiceId("INV123");
		invoice.setInvoiceRefNum("INV123");
		invoice.setPoDate("2020-07-09T20:20:20");
		invoice.setPoId("PO123");

		Product product1 = new Product();
		product1.setBrand("VC");
		product1.setCategoryId(1111);
		product1.setPid("P1");
		product1.setPrice(100);
		product1.setQuantity(500);


		Product product2 = new Product();
		product2.setBrand("VC");
		product2.setCategoryId(1111);
		product2.setPid("P2");
		product2.setPrice(200);
		product2.setQuantity(1000);

		List<Product> pids = new ArrayList<>();
		pids.add(product1);
		pids.add(product2);

		invoice.setVendor("China-Vendor-1");
		invoice.setPids(pids);
		grnMaster.setInvoice(invoice);

		return grnMaster;
	}

	private GRNPIDMaster createGRNPIDMaster() {

		GRNPIDMaster grnPid = new GRNPIDMaster();
		grnPid.setBrand("VC");
		grnPid.setCategoryId(1111);
		grnPid.setEstimatedQuantity(100L);
		grnPid.setGrnCode("GRN1");
		grnPid.setInvoiceId("INV123");
		grnPid.setInvoiceReferenceNum("INV123");
		grnPid.setPid("P1");
		grnPid.setPidStatus("Qc_Pending");
		grnPid.setVendor("China-Vendor-1");

		return grnPid;
	}

	private Map<String, Object> createPidCountMap() {

		Map<String, Object> pidCounts = new HashMap<>();
		Map<String, Object> counts1 = new HashMap<>();
		counts1.put("total_count", 4);
		counts1.put("fail_count", 1);

		Map<String, Object> counts2 = new HashMap<>();
		counts2.put("total_count", 5);
		counts2.put("fail_count", 2);

		pidCounts.put("P1", counts1);

		pidCounts.put("P2", counts2);
		return pidCounts;

	}

	//@Test
	public void updateEstimatedQtyTestForGRNStatusQcFailed() {
		Mockito.when(qcStatusDAO.isQcStatusFailed("LKG123", "PRO123")).thenReturn(true);
		GRNPIDMaster grnpidMaster = new GRNPIDMaster();
		Mockito.when(grnpiddao.getGRNPIDMaster("LKG123", "PRO123")).thenReturn(grnpidMaster);
//		Exception ex = assertThrows(ResponseStatusException.class, () -> {grnMasterService.updateEstimatedQty(getEstimatedQtyChangeReq());});
//		assertEquals("417 EXPECTATION_FAILED \"Grn status is qc failed\"", ex.getMessage());
	}

	//@Test
	public void getGRNDetailsByInvoiceTest() throws Exception {

		List<GRNMaster> grns = new ArrayList<>();
		grns.add(createGRNMaster());

		List<GRNPIDMaster> grnPids = new ArrayList<>();
		grnPids.add(createGRNPIDMaster());
		GRNPIDMaster grnPid = createGRNPIDMaster();
		grnPid.setPid("P2");
		grnPids.add(grnPid);

		Map<String, Map<String, Object>> grnPidCounts = new HashMap<>();
		grnPidCounts.put("GRN1", createPidCountMap());

		Mockito.when(grnMasterDAO.getGRNByInvoiceAndUser("INV123", "User1", false)).thenReturn(grns);
		Mockito.when(grnpiddao.getGRNPIDDetails(Mockito.any(List.class))).thenReturn(grnPids);
		Mockito.when(grnItemService.getGRNPidDetails(Mockito.any(List.class))).thenReturn(grnPidCounts);

		List<GRNDetailsDTO> grnList = grnMasterService.getGRNDetailsByInvoice("INV123");
		assertNotNull(grnList);
		assertTrue(grnList.size() > 0);
		assertNotNull(grnList.get(0).getInvoiceReferenceNum());
		assertNotNull(grnList.get(0).getPoId());
		assertNotNull(grnList.get(0).getTotalScanned());
		assertEquals(9, grnList.get(0).getTotalScanned());
		assertNotNull(grnList.get(0).getCreatedOn());
		assertNotNull(grnList.get(0).getCreatedBy());
		assertTrue(grnList.get(0).getGrnPids().size() == 2);

	}

	//@Test
	public void grnSearchTestForSuccess() throws JsonMappingException, JsonProcessingException {
		GRNSearchDTO grnSearchReq = new GRNSearchDTO();
		GRNMasterDetails grnMaster = new GRNMasterDetails();
		grnMaster.setGrnCode("GRN1");
		grnMaster.setGrnStatus("created");
		grnMaster.setPoId("PO123");
		grnMaster.setInvoiceId("INV123");
		grnMaster.setCreatedBy("User1");

		Invoice invoice = new Invoice();
		invoice.setVendor("Lenskart");
		grnMaster.setInvoice(invoice);

		List<GRNMasterDetails> grns = new ArrayList<>();
		grns.add(grnMaster);

		Map<String, Map<String, Object>> map = new HashMap<>();
		Map<String, Object> scanMap = new HashMap<>();
		scanMap.put("TOTAL_SCANNED", 10);
		scanMap.put("TOTAL_PASSED", 5);
		map.put("GRN1", scanMap);
		Mockito.when(grnMasterDAO.searchGRN(grnSearchReq, false)).thenReturn(grns);
		Mockito.when(grnItemDAO.getGRNScanDetails(Mockito.anyList())).thenReturn(map);

		SearchResponse<List<GRNSearchResponseDTO>> result = grnMasterService.grnSearch(grnSearchReq);
		assertNotNull(result);
		assertNotNull(result.getData());
		assertTrue(result.getData().size() > 0);
		assertNotNull(result.getData().get(0));
	}

	//@Test
	public void grnSearchTestForException() throws JsonMappingException, JsonProcessingException {
		GRNSearchDTO grnSearchReq = new GRNSearchDTO();
		Mockito.when(grnMasterDAO.searchGRN(grnSearchReq, false)).thenReturn(null);
		Exception ex = assertThrows(ResponseStatusException.class, () -> {
			grnMasterService.grnSearch(createGrnSearchReq());
		});
		assertEquals("204 NO_CONTENT \"No data found\"", ex.getMessage());
	}

	@Test
	void testValidateGRN() throws JsonProcessingException, SocketTimeoutException {
		Invoice invoice = createInvoice();
		grnMasterService.validateGRN(invoice, "Fac1");
		Assertions.assertEquals("ABC", "ABC", "Validate Grn done");
	}

	//@Test
	public void getBigint() {
		BigInteger b = BigInteger.ZERO;
		int a = Integer.parseInt(DBUtils.getOrDefault(b, 0).toString());
		assertEquals(0, a);
	}

	@Test
	public void validatePutawayCreatedForAllGrnItems() throws Exception {
		GRNMaster grnMaster = getGrnMaster("GRN123");

		List<GrnItemEntity> grnItemEntities = new ArrayList<>();
		GrnItemEntity entity = getGrnItemEntity();
		grnItemEntities.add(entity);

		Mockito.when(grnItemEntityService.findByGrnCodeAndPutawayCodeIsNull("GRN123")).thenReturn(grnItemEntities);

		CreatePutAwayRequest request = getCreatePutAwayRequest();
		List<CreatePutawayResponse> response = getCreatePutawayResponse();
		List<String> failedBarcodeList = new ArrayList<>();
		Mockito.when(putawayService.createGrnItemPutaway("GRN123", "UGRN123", grnItemEntities)).thenReturn(response);
		Mockito.doNothing().when(grnItemEntityService).saveOrUpdateAll(grnItemEntities);

		grnMasterService.validatePutawayCreatedForAllGrnItems(grnMaster, 2);
		Assertions.assertEquals("ABC", "ABC", "Id should be set to default value");
	}

	private GRNMaster getGrnMaster(String grnCode) {
		GRNMaster grnMaster = new GRNMaster();
		grnMaster.setGrnCode(grnCode);
		grnMaster.setPoId("PO1");
		grnMaster.setInvoiceId("123");
		grnMaster.setUnicomGrnCode("UGRN123");
		grnMaster.setGrnStatus("created");
		grnMaster.setFacility("FAC1");
		grnMaster.setCreatedBy("User1");
		grnMaster.setGrnType(GRNConstants.TRANSFER_TYPE);
		return grnMaster;
	}

	private CreatePutAwayRequest getCreatePutAwayRequest() {
		CreatePutAwayRequest request = new CreatePutAwayRequest();
		CreatePutawayItemRequest itemRequest = new CreatePutawayItemRequest();
		List<CreatePutawayItemRequest> itemRequests = new ArrayList<>();
		request.setGrnNumber("GRN123");
		request.setFacilityCode("defaultFacilityCode");
		request.setUser("defaultUser");
		request.setType(PutAwayType.PUTAWAY_GRN_ITEM);
		itemRequest.setBarcodeNumber("defaultBarcodeNumber");
		itemRequest.setPid("defaultPid");
		itemRequest.setAction(PutAwayAction.create);
		itemRequest.setBoxBarcode("defaultBoxBarcode");
		itemRequest.setCondition("defaultCondition");
		itemRequests.add(itemRequest);
		request.setBarcodes(itemRequests);
		request.setReferences(new HashMap<>());
		return request;
	}

	private List<CreatePutawayResponse> getCreatePutawayResponse() {
		List<CreatePutawayResponse> responses = new ArrayList<CreatePutawayResponse>();
		CreatePutawayResponse response = new CreatePutawayResponse();
		response.setGrnNumber("GRN123");
		response.setLocationId("defaultLocationId");
		response.setScanType("defaultScanType");
		response.setSuccessful(true);
		response.setErrorMessage("defaultErrorMessage");
		List<CreatePutawayItemResponse> itemResponseList = new ArrayList<>();
		CreatePutawayItemResponse itemResponse = new CreatePutawayItemResponse();
		itemResponse.setBarcodeNumber("ABC");
		itemResponse.setNewPutawayId("1412");
		itemResponse.setOldPutawayId("1411");
		itemResponseList.add(itemResponse);
		response.setBarcodeInfo(itemResponseList);
		responses.add(response);
		return responses;
	}

	private GrnItemEntity getGrnItemEntity() {
		GrnItemEntity entity = new GrnItemEntity();
		entity.setId(BigInteger.valueOf(1L));
		entity.setBarcode("ABC");
		entity.setGrnCode("GRN123");
		entity.setStatus("PASSED");
		entity.setPid("P1");
		entity.setPoId("PLKH03-24-043280");
		entity.setInvoiceRefNum("42639");
		entity.setVendorCode("WHOLESALE01");
		entity.setExpiryDate(new Date());
		entity.setLotNo("defaultLotNo");
		entity.setEstimatedQty(0L);
		entity.setQcPassBoxBarcode("BXBL8820");
		entity.setQcStatus("pass");
		entity.setQcFailCode("defaultQcFailCode");
		entity.setQcReason("defaultQcReason");
		entity.setQcMasterSampling(5);
		entity.setQcGradientSampling(10);
		entity.setFailureThresholdQcMaster(2);
		entity.setFailureThresholdQcGradient(3);
		entity.setFacility("defaultFacility");
		entity.setChannelStatus(1);
		return entity;
	}

	@Test
	public void closePutawayGRN() throws Exception {
		GRNMaster grnMaster = getGrnMaster("GRN123");
		grnMasterService.closePutawayGRN("GRN123", GRNConstants.TRANSFER_TYPE);
	}

	@Test
	public void checkEligibilityForIQC() throws Exception {
		List<IqcGrnProductEntity> entities =  new ArrayList<>();
		IqcGrnProductEntity iqcGrnProductEntity = getIqcGrnProductEntity();
		entities.add(iqcGrnProductEntity);
		Mockito.when(iqcGrnProductEntityService.findByGrnCode("GRN123")).thenReturn(entities);
		grnMasterService.checkEligibilityForIQC("GRN123");
	}

	public IqcGrnProductEntity getIqcGrnProductEntity() {
		IqcGrnProductEntity entity = new IqcGrnProductEntity();
		entity.setId(123L);
		entity.setInvoiceRefNumber("121");
		entity.setGrnCode("GRN123");
		entity.setTotalQty(10);
		entity.setQcFailQty(2);
		entity.setQcPassQty(8);
		entity.setRetryCount(0);
		entity.setEnabled(true);
		entity.setStatus(IqcGrnProductStatusEnum.IQC_COMPLETE);
		return entity;
	}

	@Test
	public void closeGrn() throws Exception {
		GRNMaster grnMaster = getGrnMaster("GRN123");
		validatePutawayCreatedForAllGrnItems();
		List<GRNPIDMaster> grnPidMasterList = new ArrayList<>();
		GRNPIDMaster grnpidMaster = getGrnPisMaster("GRN123");
		grnPidMasterList.add(grnpidMaster);
		Mockito.when(grnPidDao.getGRNPIDS("GRN123")).thenReturn(grnPidMasterList);
		Mockito.when(grnConfig.getCloseGrnSyncUnsynedBarcodePutawayCount()).thenReturn(2);

		List<GrnPOInvoicePidData> pidPOInvoiceForGrn = new ArrayList<>();
		Mockito.when(grnItemDAO.getPidPOInvoiceForGrn("GRN123")).thenReturn(pidPOInvoiceForGrn);
		List<BoxMapping> boxMappingsList = createBoxMappingList("GRN123");
		Mockito.when(grnItemDAO.getEmptyBoxMappings("GRN123")).thenReturn(new ArrayList<>());
		Mockito.when(grnConfig.getKeyPrefix()).thenReturn("nexs");

		Mockito.doNothing().when(grnMasterService).pushCloseGrnCallInKafka("GRN123","FAC1",true);
		grnMasterService.closeGRN("GRN123", "FAC1", grnMaster, true);
	}

	private List<BoxMapping> createBoxMappingList(String grnCode) {
		List<BoxMapping> boxMappings = new ArrayList<>();
//		BoxMapping boxMapping = new BoxMapping("ABC", grnCode, "P1");
//		boxMappings.add(boxMapping);
		return boxMappings;
	}

	private GRNPIDMaster getGrnPisMaster(String grnCode) {
		GRNPIDMaster grnpidMaster = new GRNPIDMaster();
		grnpidMaster.setGrnCode(grnCode);
		grnpidMaster.setPid("P1");
		grnpidMaster.setInvoiceId("123");
		grnpidMaster.setInvoiceReferenceNum("INV123");
		grnpidMaster.setVendor("WHOLESALE01");
		grnpidMaster.setBrand("VC");
		grnpidMaster.setCategoryId(1111);
		grnpidMaster.setPidDescription("Pid Desc");
		grnpidMaster.setPrice(3.50);
		grnpidMaster.setTaxRate(3.50);
		grnpidMaster.setCgstRate(0);
		grnpidMaster.setSgstRate(0);
		grnpidMaster.setIgstRate(0);
		grnpidMaster.setEstimatedQuantity(0L);
		grnpidMaster.setPidStatus("PASSED");
		grnpidMaster.setManualOverride((short) 0);
		return grnpidMaster;
	}


	@Test
	public void createGrnDetails() throws Exception {
		AutoGrnScanDetails request = createAutoGrnScanDetails();
		List<PurchaseInvoiceEntity> invoiceEntity = createPurchaseInvoiceEntity();
		Invoice invoice = createInvoice();

		Mockito.when(grnConfig.getBarcodeMaxSize()).thenReturn(2);
		Mockito.when(purchaseInvoiceEntityService.findByPoNum("PO1")).thenReturn(createPurchaseInvoiceEntity());
		Mockito.when(poInvoiceService.getPurchaseOrderDetails("PO1")).thenReturn(createPurchaseOrder());
		Mockito.when(poInvoiceService.getInvoiceDetailsByVendorInvoiceNum("INVOICE1")).thenReturn(invoice);
		Mockito.when(facilityDetailsUtils.isUnicomFacility("FAC1")).thenReturn(false);

		CreateGRNMasterDTO createGRNMasterDTO = createGRNMasterDTO(createPurchaseOrder(), invoice,
				invoiceEntity.get(0).getFacilityCode(), invoiceEntity.get(0).getCreatedBy());
		List<GrnMasterEntity> grnMasterEntities = new ArrayList<>();
		GRNMaster grnMaster = createGRNMaster();

		Mockito.when(grnMasterEntityService.findByInvoiceId(invoice.getInvoiceRefNum())).thenReturn(grnMasterEntities);
		Mockito.when(grnMasterDAO.createGRN(any(GRNMaster.class))).thenReturn(true);

		Mockito.doNothing().when(grnMasterService).validateGRN(invoice,"FAC1");
		Mockito.doNothing().when(grnMasterService).addGrnSequenceInDB(any(String.class), any(GRNMaster.class));

		grnMasterService.createGrnDetails(request);
		Assertions.assertEquals("ABC", "ABC", "GRN created");
	}

	private GrnMasterEntity createGrnMasterEntity() {
		GrnMasterEntity grnMaster = new GrnMasterEntity();
		grnMaster.setGrnCode("GRN123");
		grnMaster.setGrnStatus("creates");
		grnMaster.setGrnType("REGULAR");
		grnMaster.setInvoiceId("1");
		grnMaster.setPoId("PO1");
		grnMaster.setUpdatedAt(new Date());
		grnMaster.setCreatedAt(new Date());
		grnMaster.setCreatedBy("LSP05068");
		grnMaster.setUpdatedBy("LSP05068");
		return grnMaster;
	}

	private CreateGRNMasterDTO createGRNMasterDTO(PurchaseOrder purchaseOrder, Invoice invoice,
												  String facilityCode, String createdBy) {
		CreateGRNMasterDTO createGRNMasterDTO = new CreateGRNMasterDTO();
		createGRNMasterDTO.setPo(purchaseOrder);
		createGRNMasterDTO.setInvoice(invoice);
		createGRNMasterDTO.setFacilityCode(facilityCode);
		MDC.put("FACILITY_CODE", "FAC1");
		MDC.put("USER_ID", "USER1");
		return createGRNMasterDTO;
	}

	private AutoGrnScanDetails createAutoGrnScanDetails() {
		AutoGrnScanDetails request = new AutoGrnScanDetails();
		request.setPoNum("PO1");
		request.setVendorInvoiceNumber("INVOICE1");
		request.setFacilityCode("FAC1");
		request.setLegalOwner("LEG1");
		request.setInvoiceDate("2024-03-09");
		request.setCreatedBy("SYSTEM");
		request.setGrnItemDetailList(generateAutoGrnItemScanDetails());
		return request;
	}

	private List<AutoGrnItemScanDetails> generateAutoGrnItemScanDetails() {
		List<AutoGrnItemScanDetails> autoGrnItemScanDetailsList = new ArrayList<>();
		AutoGrnItemScanDetails grnItemScanDetails = new AutoGrnItemScanDetails();

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.MONTH, 56);
		Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());

		grnItemScanDetails.setBarcode("BARCODE1");
		grnItemScanDetails.setProductId("123");
		grnItemScanDetails.setExpiryDate(timestamp);
		autoGrnItemScanDetailsList.add(grnItemScanDetails);

		return autoGrnItemScanDetailsList;
	}

	private Invoice createInvoice() {
		Invoice invoice = new Invoice();
		List<Product> pids = new ArrayList<>();
		invoice.setInvoiceRefNum("1");
		invoice.setInvoiceId("INVOICE1");
		invoice.setPoId("PO1");
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
//		if (purchaseOrder.getApprovedAt() != null) {
//			Date approvedAt = purchaseOrder.getApprovedAt();
//			invoice.setPoDate(format.format(approvedAt));
//			Timestamp poApprovedAt = new Timestamp(approvedAt.getTime());
//			poApprovedAt.setHours(poApprovedAt.getHours() + 5);
//			poApprovedAt.setMinutes(poApprovedAt.getMinutes() + 30);
//			invoice.setApprovedAt(poApprovedAt.getTime());
//
//		}
//		if (invoiceEntity.getInvoiceDate() != null) {
//			Date invoiceDate = invoiceEntity.getInvoiceDate();
//			invoice.setInvoiceDate(format.format(invoiceDate));
//			Timestamp invoiceDateTime = new Timestamp(invoiceDate.getTime());
//			invoiceDateTime.setHours(invoiceDateTime.getHours() + 5);
//			invoiceDateTime.setMinutes(invoiceDateTime.getMinutes() + 30);
//			invoice.setInvoice_date(invoiceDateTime.getTime());
//		}
		invoice.setPreFilledInvoice(true);
		invoice.setVendor("VENDOR1");
		invoice.setVendorName("NAME1");
		invoice.setCurrency("INR");
		invoice.setB2bInvoiceDate(new Date().getTime());
		invoice.setSendToParty(null);
		invoice.setHandoverParty(null);
		invoice.setBillOfEntryNumber(null);
		invoice.setBillOfEntryAmount(null);
		invoice.setBillOfEntryDate(null);
//		for (PurchaseInvoiceItemEntity item : invoiceItemEntities) {
		Product product = new Product();
		pids.add(product);
//		}
		invoice.setPids(pids);
		invoice.setLegalOwner("LEG1");

		return invoice;
	}

	private PurchaseOrder createPurchaseOrder() throws JsonProcessingException {
		PurchaseOrder po = new PurchaseOrder();
		List<POProduct> pIds = new ArrayList<>();

		POProduct poProduct = new POProduct();
		poProduct.setPid(String.valueOf(123));
		poProduct.setQuantity(1);
		poProduct.setPrice(101);
		poProduct.setTotalVendorCostPrice(101);
		poProduct.setCgstRate(0);
		poProduct.setSgstRate(0);
		poProduct.setIgstRate(0);
		poProduct.setPriceWithTaxes(0);
		poProduct.setCategoryId(13355);

		pIds.add(poProduct);
		po.setPoId("PO1");
		po.setPids(pIds);
		return po;
	}

	private List<PurchaseInvoiceEntity> createPurchaseInvoiceEntity() {
		List<PurchaseInvoiceEntity> purchaseInvoiceEntities = new ArrayList<>();
		PurchaseInvoiceEntity entity = new PurchaseInvoiceEntity();
		entity.setInvoiceRefNumber(1);
		entity.setVendorInvoiceNumber("INVOICE1");
		entity.setPoNum("PO1");
		entity.setLegalOwner("LEG1");
		entity.setType("REGULAR");
		entity.setInvoiceDate(new Date());
		entity.setStatus("CREATED");
		entity.setVersion(1);
		entity.setType("REGULAR");
		entity.setVendorId("VENDOR1");
		entity.setFacilityCode("FAC1");
		entity.setVendorStateCode("102");
		entity.setTotalNumPid(1);
		entity.setTotalInvoiceAmount(101.0);
		entity.setTotalInvoiceQty(1);
		entity.setOrderAcceptedQuantity(1);
		entity.setOrderRejectQty(0);
		entity.setOrderPendQty(0);
		entity.setCurr("INR");
		entity.setCurrencyConvRate(1.00);
		entity.setCreatedBy("SYSTEM");
		entity.setUpdatedBy("SYSTEM");
		entity.setCreatedAt(new Date());
		entity.setUpdatedAt(new Date());
		entity.setEnabled(true);
		purchaseInvoiceEntities.add(entity);
		return purchaseInvoiceEntities;
	}
}
