package com.lenskart.nexs.grn.service.strategy.impl;

import com.lenskart.nexs.grn.config.BarcodeConfig;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.dao.BarcodeDAO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.FixedKeySet;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class BarcodeValidationImplTest {

    @Mock
    private BarcodeConfig barcodeConfig;

    @Mock
    private BarcodeDAO barcodeDAO;

    @Mock
    private GRNConfig grnConfig;

    @InjectMocks
    private BarcodeValidationImpl barcodeValidation;

    GRNItemDTO grnItem = new GRNItemDTO();

    Set<String> nexsSet = new HashSet<>();

    @BeforeEach
    public void setup() {
        grnItem.setBarcode("NEXA00025");

        nexsSet.add("NEXA00001_NEXA00099");
    }

    /*//@Test
    public void testValidateBarcode() {
        Set<String> set = new HashSet<>();
        set.add("NEXA");
        Mockito.when(barcodeConfig.getNexsBeginCharsSet()).thenReturn(set);
        Set<String> nexaSet;
        Mockito.when(barcodeConfig.getNexsSet()).thenReturn(nexsSet);
        assertTrue(barcodeValidation.validateBarcode(grnItem));
    }

    //@Test
    public void testValidateBarcodeFalse() {
        Set<String> set = new HashSet<>();
        set.add("NEXA");
        grnItem.setBarcode("NEXA00000");
        Mockito.when(barcodeConfig.getNexsBeginCharsSet()).thenReturn(set);
        Set<String> nexaSet;
        Mockito.when(barcodeConfig.getNexsSet()).thenReturn(nexsSet);
        assertFalse(barcodeValidation.validateBarcode(grnItem));
    }*/
}
