package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.grn.constants.GRNQcLogEvents;
import com.lenskart.nexs.grn.dao.GRNQcLogDAO;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.ProductMismatchReqDTO;
import com.lenskart.nexs.grn.dto.response.PIDResponseDTO;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GRNQcLogServiceTest {

    @Mock
    private GRNPIDMasterService grnPidMasterService;

    @Mock
    private GRNQcLogDAO grnQcLogDAO;

    @InjectMocks
    private GRNQcLogService grnQcLogService;

    //@Test
    public void testProductMismatch() {
        ProductMismatchReqDTO productMismatchReqDTO = new ProductMismatchReqDTO();
        GRNMasterMetaDTO meta = new GRNMasterMetaDTO();
        productMismatchReqDTO.setMeta(meta);
        productMismatchReqDTO.setScannedPid("P1");
        productMismatchReqDTO.setPid("P1");
        productMismatchReqDTO.setGrnCode("GRN1");
        productMismatchReqDTO.setBarcode("barcode1");
        productMismatchReqDTO.setAction("action");
        productMismatchReqDTO.setReason("reason1");
        PIDResponseDTO response = new PIDResponseDTO();
        response.setPid("P2");
        Mockito.when(grnPidMasterService.getPID(Mockito.any())).thenReturn(response);
        Mockito.doNothing().when(grnQcLogDAO).saveLog(Mockito.any());
        assertTrue(grnQcLogService.productMismatch(productMismatchReqDTO));
    }

    //@Test
    public void testProductMismatchNext() {
        ProductMismatchReqDTO productMismatchReqDTO = new ProductMismatchReqDTO();
        GRNMasterMetaDTO meta = new GRNMasterMetaDTO();
        productMismatchReqDTO.setMeta(meta);
        productMismatchReqDTO.setScannedPid("P1");
        productMismatchReqDTO.setPid("P1");
        productMismatchReqDTO.setGrnCode("GRN1");
        productMismatchReqDTO.setBarcode("barcode1");
        productMismatchReqDTO.setAction("action");
        productMismatchReqDTO.setReason("reason1");
        PIDResponseDTO response = new PIDResponseDTO();
        response.setPid("P2");
        Mockito.when(grnPidMasterService.getPID(Mockito.any())).thenThrow(RuntimeException.class);
        Mockito.doNothing().when(grnQcLogDAO).saveLog(Mockito.any());
        assertTrue(grnQcLogService.productMismatch(productMismatchReqDTO));
    }

    //@Test
    public void testProductMismatchFalse() {
        ProductMismatchReqDTO productMismatchReqDTO = new ProductMismatchReqDTO();
        GRNMasterMetaDTO meta = new GRNMasterMetaDTO();
        productMismatchReqDTO.setMeta(meta);
        productMismatchReqDTO.setScannedPid("P1");
        productMismatchReqDTO.setPid("P1");
        productMismatchReqDTO.setGrnCode("GRN1");
        productMismatchReqDTO.setBarcode("barcode1");
        productMismatchReqDTO.setAction("action");
        productMismatchReqDTO.setReason("reason1");
        PIDResponseDTO response = new PIDResponseDTO();
        response.setPid("P1");
        Mockito.when(grnPidMasterService.getPID(Mockito.any())).thenReturn(response);
        assertFalse(grnQcLogService.productMismatch(productMismatchReqDTO));
    }

    //@Test
    public void testLogGRNQcAction() {
        Mockito.doNothing().when(grnQcLogDAO).saveLog(Mockito.any());
        grnQcLogService.logGRNQcAction("GRN1", "P1", "barcode1", GRNQcLogEvents.DUPLICATE);
        Mockito.verify(grnQcLogDAO, Mockito.times(1)).saveLog(Mockito.any());
    }
}
