package com.lenskart.nexs.grn.service;

import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.dao.CacheDAO;
import com.lenskart.nexs.grn.dao.QcStatusDAO;
import com.lenskart.nexs.grn.model.GRNItem;
//import com.lenskart.nexs.grn.model.QcConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CacheUpdateServiceTest {

    @Mock
    private QcStatusDAO qcStatusDAO;

    @Mock
    private CacheDAO cacheDAO;

    @InjectMocks
    private CacheUpdateService cacheUpdateService;

    GRNItem grnItem = new GRNItem();

    //@Test
    public void testUpdateCacheState() {
        grnItem.setQcStatus(GRNConstants.QC_FAIL);
        grnItem.setInvoiceRefNum("INV123");
        grnItem.setGrnCode("GRN1");
        grnItem.setPid("P1");
        Mockito.when(qcStatusDAO.decrementFailCount(grnItem.getGrnCode(), grnItem.getPid())).thenReturn(10L);
        Mockito.when(qcStatusDAO.decrementScanCount(grnItem.getGrnCode(), grnItem.getPid())).thenReturn(10L);
        Mockito.when(qcStatusDAO.decrementItemQtyCount(grnItem.getInvoiceRefNum(), grnItem.getPid())).thenReturn(10L);
        Mockito.doNothing().when(qcStatusDAO).setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), "PENDING");
        cacheUpdateService.updateCacheState(grnItem, "PENDING", GRNConstants.UPDATE);
        Mockito.verify(qcStatusDAO, Mockito.times(1)).decrementFailCount(grnItem.getGrnCode(), grnItem.getPid());
        Mockito.verify(qcStatusDAO, Mockito.times(1)).decrementScanCount(grnItem.getGrnCode(), grnItem.getPid());
    }

    //@Test
    public void testUpdateCacheStateNext() {
        grnItem.setQcStatus(GRNConstants.QC_FAIL);
        grnItem.setInvoiceRefNum("INV123");
        grnItem.setGrnCode("GRN1");
        grnItem.setPid("P1");
//        QcConfig qc = new QcConfig();
        Mockito.when(qcStatusDAO.decrementFailCount(grnItem.getGrnCode(), grnItem.getPid())).thenReturn(10L);
        Mockito.when(qcStatusDAO.decrementScanCount(grnItem.getGrnCode(), grnItem.getPid())).thenReturn(10L);
        Mockito.when(qcStatusDAO.decrementItemQtyCount(grnItem.getInvoiceRefNum(), grnItem.getPid())).thenReturn(10L);
//        Mockito.doNothing().when(cacheDAO).setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), qc);
//        Mockito.doNothing().when(cacheDAO).setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), qc);
        Mockito.doNothing().when(qcStatusDAO).setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), "PENDING");
        cacheUpdateService.updateCacheState(grnItem, "PENDING");
        Mockito.verify(qcStatusDAO, Mockito.times(1)).decrementFailCount(grnItem.getGrnCode(), grnItem.getPid());
        Mockito.verify(qcStatusDAO, Mockito.times(1)).decrementScanCount(grnItem.getGrnCode(), grnItem.getPid());
    }

    //@Test
    public void testUpdateRecovery() {
        grnItem.setQcStatus(GRNConstants.QC_FAIL);
        grnItem.setInvoiceRefNum("INV123");
        grnItem.setGrnCode("GRN1");
        grnItem.setPid("P1");
//        QcConfig qc = new QcConfig();
//        Mockito.doNothing().when(cacheDAO).setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), qc);
//        Mockito.doNothing().when(cacheDAO).setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), qc);
        Mockito.doNothing().when(qcStatusDAO).setGrnStatus(grnItem.getGrnCode(), grnItem.getPid(), "PENDING");
        Mockito.doNothing().when(qcStatusDAO).setTotalFailed(grnItem.getGrnCode(), grnItem.getPid(), 10);
        cacheUpdateService.updateRecovery(grnItem, "PENDING", 10, 10L);
//        Mockito.verify(cacheDAO, Mockito.times(1)).setInvoiceConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), qc);
//        Mockito.verify(cacheDAO, Mockito.times(1)).setGrnConfig(grnItem.getGrnCode(), grnItem.getPid(), qc);
    }
}
