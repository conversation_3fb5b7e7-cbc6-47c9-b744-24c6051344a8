version: '3'

services:
  grn:
    image: 928401551325.dkr.ecr.ap-southeast-1.amazonaws.com/nexs-grn-dev:latest
    container_name: grn
    restart: unless-stopped
    ports:
      - "8101:8080"
    environment:
      - TZ=Asia/Kolkata
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - KAFKA_BROKERS=${KAFKA_BROKERS}
      - MYSQL_HOST=${MYSQL_HOST}
      - MYSQL_USER=${NEXS_MYSQL_DB_USERNAME}
      - MYSQL_PASSWORD=${NEXS_MYSQL_DB_PWD}
      - NEXS_MYSQL_HOST=${MYSQL_HOST}
      - NEXS_MYSQL_USER=${NEXS_MYSQL_DB_USERNAME}
      - NEXS_MYSQL_PASSWORD=${NEXS_MYSQL_DB_PWD}
      - INV_MYSQL_HOST=${MYSQL_HOST}
      - INV_MYSQL_USER=${NEXS_MYSQL_DB_USERNAME}
      - INV_MYSQL_PASSWORD=${NEXS_MYSQL_DB_PWD}
      - REDIS_SENTINEL_MASTER=${REDIS_SENTINEL_MASTER}
      - REDIS_SENTINEL_NODES=${REDIS_SENTINEL_NODES}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - UNICOM_HOST_PORT=http://unicom-adapter:8080
      - GRN_ENTITIES_HOST_PORT=http://grn-entities:8080
      - GRN_UNICOM_TOPIC=dev-nexs-grn-sync-unicom
      - GRN_NUM_PREFIX=${GRN_NUM_PREFIX}
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    deploy:
      resources:
        limits:
          memory: 1024M
        reservations:
          memory: 128M

networks:
  default:
    external:
      name: nexs